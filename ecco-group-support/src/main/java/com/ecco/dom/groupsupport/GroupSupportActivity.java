package com.ecco.dom.groupsupport;

import java.math.BigDecimal;
import java.util.Set;
import java.util.UUID;

import javax.persistence.*;
import org.jspecify.annotations.NonNull;
import javax.validation.constraints.NotNull;

import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.EvidenceCapable;
import com.ecco.dom.Identified;

import static com.ecco.infrastructure.hibernate.AntiProxyUtils.identifier;

import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import com.ecco.serviceConfig.dom.ServiceCategorisation;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Type;
import org.joda.time.DateTime;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect;
import org.springframework.format.annotation.DateTimeFormat;

import com.querydsl.core.annotations.QueryInit;

@SuppressWarnings("serial")
@Entity
@Table(name = "grp_activities")
@Getter
@Setter
@Configurable
@Slf4j
public class GroupSupportActivity extends AbstractUnidentifiedVersionedEntity<Long> implements EvidenceCapable {

    public static final String DISCRIMINATOR_SUPPORT = "supp";
    public static final String DISCRIMINATOR_COMMS = "comm";
    public static final String DISCRIMINATOR_AUX = "aux";

    public static final int DEFAULT_SERVICE_ALLOCATION_ID = -400;

    @PersistenceContext
    @Transient
    private transient EntityManager em;

    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    @GeneratedValue(generator="gsTableGenerator")
    @TableGenerator(
            name = "gsTableGenerator", initialValue = 1, pkColumnValue = "groupsupport",
            allocationSize = 1, table = "hibernate_sequences")
    private Long id = null;

    /**
     * The course linked to this session, if any
     */
    @Column
    @Nullable
    private Long parentId;

    /**
     * This is a course, with sessions in it
     */
    private Boolean course;

    /** uuid key to allow remote commands to reference what they created */
    @NonNull
    @Column(name = "uuid", nullable = false, columnDefinition = "CHAR(36)")
    @Type(type = "uuid-char")
    private UUID uuid;

    @OneToOne(cascade= {CascadeType.PERSIST, CascadeType.REMOVE})
    @JoinColumn(name="serviceRecipientId")
    @QueryInit("*.*.*")
    private GroupSupportServiceRecipient serviceRecipient;

    @Setter(AccessLevel.PRIVATE)
    @Column(insertable = false, updatable = false)
    private Integer serviceRecipientId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "activityTypeId")
    ListDefinitionEntry groupSupportActivityType;

    // a discriminator by name, not type - to avoid lots of boilerplate of sub-entities for now
    String discriminator_orm;

    String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "venueId")
    private ListDefinitionEntry venue;
    public static String VENUE_LISTNAME = "venue";

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "serviceAllocationId")
    @QueryInit("*.*")
    private ServiceCategorisation serviceAllocation;

    int capacity;

    int minutes;

    @DateTimeFormat(style="SS")
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    @NotNull
    DateTime fromDate;
    @DateTimeFormat(style="SS")
    @Type(type="org.jadira.usertype.dateandtime.joda.PersistentDateTime")
    DateTime toDate;

    BigDecimal value;

    /**
     * Invited referrals are those that are persisted in this set - see EditInvitationCommandHandler.
     */
    // m2m with association info
    @BatchSize(size=10)
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "multiId.activity", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<GroupActivity_Referral> activityReferrals;

    @BatchSize(size=10)
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "multiId.groupSupportActivity", cascade = CascadeType.ALL, orphanRemoval = true)
    Set<GroupSupportActivity_Worker> activityWorkers;

    public String displayName() {
        String tmp = "";
        if (groupSupportActivityType != null) {
            tmp = groupSupportActivityType.getName().concat(" ");
        }
        tmp = tmp.concat(StringUtils.isEmpty(tmp) ? "[ " + description + " ]" : description);
        if (id != null) {
            tmp = tmp.concat(" [id " + id + "]");
        }
        return tmp;
    }

    @Transient
    public Long getServiceId() {
        return serviceAllocation == null ? null : identifier(serviceAllocation.getService());
    }

    @Transient
    public Long getProjectId() {
        return serviceAllocation == null ? null : identifier(serviceAllocation.getProject());
    }

    public GroupSupportActivity() {
        injectServices();
    }

    public Object readResolve()  {
        injectServices();
        return this;
    }

    private void injectServices() {
        // NOTE: This expects to find @Configurable on the class
        AnnotationBeanConfigurerAspect.aspectOf().configureBean(this);
    }

    @PrePersist
    public void prePersist() {
        ensureToDateIsValid();
        createServiceRecipient();
    }

    @PreUpdate
    public void ensureToDateIsValid() {
        if (toDate != null && toDate.isBefore(fromDate)) {
            log.warn("setting toDate to null as user created toDate:{} on or before fromDate: {}",
                    toDate.toString(), fromDate.toString());
            toDate = null;
        }
    }

    protected void createServiceRecipient() {
        this.serviceRecipient = new GroupSupportServiceRecipient();
        this.serviceRecipient.setGroupSupport(this);
        serviceRecipient.setServiceAllocation(em.getReference(ServiceCategorisation.class, DEFAULT_SERVICE_ALLOCATION_ID));
    }


    @Override
    public EvidenceCapable getParentEvidenceCapable() {
        return null; // doesn't yet support having a parent
    }

    @Override
    public Integer countSiblings() {
        return null;
    }
    @Override
    public Identified getGrandParent() {
        return null;
    }

}
