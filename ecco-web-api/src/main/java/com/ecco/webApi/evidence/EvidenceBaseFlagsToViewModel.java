package com.ecco.webApi.evidence;

import com.ecco.dom.EvidenceBaseFlag;


/**
 * Representation of a flag across a collection of flags spanning work and srIds
 */
public class EvidenceBaseFlagsToViewModel {

    public static void apply(EvidenceBaseFlagsViewModel viewModel, EvidenceBaseFlag input) {
        if (input == null) {
            throw new NullPointerException("input EvidenceBaseFlag must not be null");
        }

        viewModel.id = input.getId();
        viewModel.serviceRecipientId = input.getServiceRecipientId();
        viewModel.serviceAllocationId = input.getServiceRecipient().getServiceAllocationId();
        viewModel.flagId = input.getFlagDefId();
        viewModel.value = input.isValue();
        viewModel.workUuid = input.getWorkId();
        viewModel.workDate = input.getWork().getWorkDate().toLocalDateTime();
    }

}
