package com.ecco.webApi.evidence;

import com.ecco.dto.ChangeViewModel;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.dom.BaseCommand;
import com.ecco.infrastructure.spring.data.BaseCommandRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import kotlin.Pair;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.hateoas.Link;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.util.Assert;

import javax.persistence.Transient;
import org.jspecify.annotations.Nullable;
import javax.servlet.ServletContext;
import java.io.IOException;
import java.io.Serializable;
import java.util.Optional;

import static com.ecco.security.SecurityUtil.getUser;
import static com.ecco.webApi.viewModels.Result.COMMAND_APPLIED;
import static lombok.AccessLevel.NONE;
import static org.springframework.util.ObjectUtils.nullSafeEquals;

public abstract class BaseCommandHandler<VM extends BaseCommandViewModel,
                                        KEY extends Number,
                                        ENTITY extends BaseCommand<KEY>,
                                        PARAMS> {

    protected final Logger log = LoggerFactory.getLogger(getClass());
    protected final ObjectMapper objectMapper;
    protected final Class<VM> vmClass;

    protected final BaseCommandRepository<ENTITY, KEY> commandRepository;

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Autowired
    @Transient
    @Getter(NONE)
    @Setter(NONE)
    protected transient ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ServletContext servletContext;

    public BaseCommandHandler(@NonNull ObjectMapper objectMapper, @NonNull BaseCommandRepository<ENTITY, KEY> commandRepository,
                              @NonNull Class<VM> vmClass) {
        this.objectMapper = objectMapper;
        this.vmClass = vmClass;
        this.commandRepository = commandRepository;
    }

    /**
     * A standard command to be processed
     */
    public @NonNull Result handleCommand(@NonNull Authentication authentication, PARAMS params, @NonNull String requestBody) throws IOException {
        VM viewModel = objectMapper.readValue(requestBody, vmClass);
        return handleCommand(authentication, params, requestBody, viewModel);
    }

    /**
     * Allow the method to be called directly with the DTO
     * ONLY used by CommandSMSNotificationAgent
     */
    public @NonNull Result handleCommand(long userId, PARAMS params, @NonNull VM viewModel) throws IOException {
        Result earlyResult = preProcessCommand(viewModel);
        if (earlyResult != null) return earlyResult;

        var requestBody = objectMapper.writeValueAsString(viewModel);
        return createCommandAndHandleInternal(createFakeAuthenticationFor(userId), params, requestBody, viewModel, userId);
    }

    private Authentication createFakeAuthenticationFor(long userId) {
        return new UsernamePasswordAuthenticationToken(userId, null);
    }

    /** Internal, private method */
    @NonNull
    private Result handleCommand(@NonNull Authentication authentication, @Nullable PARAMS params, @NonNull String requestBody, VM viewModel) {
        Result earlyResult = preProcessCommand(viewModel);
        if (earlyResult != null) return earlyResult;

        long userId = getUser(authentication).getId();
        return createCommandAndHandleInternal(authentication, params, requestBody, viewModel, userId);
    }

    /**
     * Check the command is valid, has not already been applied and skip if a draft
     */
    private Result preProcessCommand(VM viewModel) {
        Assert.state(viewModel.valid(), "Nonnull requirements not met");

        ENTITY duplicate = commandRepository.findOneByUuid(viewModel.uuid);
        if (duplicate != null) {
            return new Result("command ignored", duplicate.getId());
        }
        return null;
    }

    // handle a draft command in the private method, so override methods don't forget
    private @NonNull Result createCommandAndHandleInternal(@NonNull Authentication authentication, PARAMS params, @NonNull String requestBody, @NonNull VM viewModel, long userId) {

        // for a draft, skip the handleInternal (and triggerEvent?) but still save
        if (Boolean.TRUE.equals(viewModel.draft)) {
            if (this.isDraftable()) {
                final ENTITY command = createCommand(null, params, requestBody, viewModel, userId);
                // ensure the command itself is a draft
                command.setDraft(true);

                // clear previous draft - we're in a transaction right so it's safe to delete first, else we will delete our new drafts also
                ((DraftableCommandHandler<VM, KEY, ENTITY, PARAMS>) this).clearDrafts(authentication, params, requestBody, viewModel, command, userId);

                ENTITY savedCommand = persistCommand(command);
                return new Result("command draft applied", savedCommand.getId());
            } else {
                return new Result("command draft ignored");
            }
        }

        var pair = handleInternalAndSaveCommand(authentication, params, requestBody, viewModel, userId);

        // clear any drafts after saving, if any - but we need to know what to clear for the entity/author in question
        // eg, svcrec_commands should clear the serviceRecipientId for the userId
        // so we delegate to the handler, since that will best know what to do
        // NB we could clear here but would result in a poorer user experience, options might be:
        //  - clear all drafts for userId and commandName
        //      - but they may expect drafts on other files to be retained - as that was their message!
        //      - and there may be many of the same commandName to make up the page (eg goals)
        //  - clear all drafts for userId and commandName and some 'id' - but how do we relate that id to clearing drafts easily, over letting the handler do it?
        if (isDraftable()) {
            ((DraftableCommandHandler<VM, KEY, ENTITY, PARAMS>) this).clearDrafts(authentication, params, requestBody, viewModel, pair.component1(), userId);
        }

        triggerEvent(viewModel, pair.component1());
        return new Result(
                pair.component2().filter(it -> !it.getMessages().isEmpty())
                        .map(it -> String.join(", ", it.getMessages())).orElse(COMMAND_APPLIED),
                pair.component1().getId(),
                pair.component2().map(result -> new Link[]{result.getLink()}).orElse(null));
    }

    protected boolean isDraftable() {
        return this instanceof DraftableCommandHandler;
    }

    /**
     * The bare minimum in handling the command and saving the command (no 'triggerEvents' etc) so that
     * we can override when the order is required to be reversed - eg when deleting service recipient the
     * command is needed to be saved before the deletion, else the command references the deleted entity.
     */
    protected @NonNull Pair<ENTITY, Optional<CommandResult>> handleInternalAndSaveCommand(
            @NonNull Authentication authentication,
            PARAMS params, @NonNull String requestBody, @NonNull VM viewModel, long userId) {
        CommandResult result = handleInternal(authentication, params, viewModel);
        final ENTITY command = createCommand(result != null ? result.getTargetId() : null, params, requestBody, viewModel, userId);
        ENTITY savedCommand = persistCommand(command);
        return new Pair<>(savedCommand, Optional.ofNullable(result));
    }

    /**
     * This need to do the action that is represented by the viewModel.
     *
     * Updating a goal should preserve a non-null work date and comment from a previous work item (e.g. when
     * CommentCommand came in first), but will set workDate = timestamp when it's null (otherwise workDate can be null
     * which we really don't ever want as null sort order varies between diff databases).
     *
     * @return Optional {@link Link} to api for the resource that was created or edited (as rel="edit").
     */
    protected abstract @Nullable CommandResult handleInternal(@NonNull Authentication auth, @NonNull PARAMS params, @NonNull VM viewModel);

    /** Save this command if necessary. This could choose to avoid saving because this is a
     *  duplicate such as an access audit event on the saame day */
    protected @NonNull ENTITY persistCommand(@NonNull ENTITY command) {
        Assert.notNull(command);
        return commandRepository.save(command);
    }

    protected void triggerEvent(@NonNull VM viewModel, @NonNull ENTITY command) {
        // there are only a handful of places that use 'getContextPath'
        // external urls are not a reliable source - but at the point of emailing they are needed
        // so perhaps methods such as ServletUriComponentsBuilder.fromCurrentContextPath() might be useful to get the whole path

        CommandCreatedEvent event = new CommandCreatedEvent(servletContext.getContextPath(), command, viewModel);
        // covers email/sms, where a further database transaction is required (and spring has this mechanism)
        applicationEventPublisher.publishEvent(event);
        // covers other CommandCreatedEvent subscribers (search CommandCreatedEvent)
        messageBus.publishAfterTxEnd(event);
    }

    /**
     * Construct the command to persist.
     * @param targetId The id of any newly created entity, required for persisting with the command (passed from LinkWithId via {@link #handleInternalAndSaveCommand})
     * @param params Request params
     * @param requestBody Request body
     * @param viewModel Request command view model
     * @param userId Command users id
     * @return Newly constructed command
     */
    protected abstract @NonNull ENTITY createCommand(@Nullable Serializable targetId, PARAMS params, @NonNull String requestBody, @NonNull VM viewModel, long userId);

    protected <T> void warnIfPrevValueDoesntMatch(long serviceRecipientId, long actionDefId, ChangeViewModel<T> command, T prevValue, String field) {
        if (!nullSafeEquals(prevValue, command.from)) {
            log.warn("srId {} ActionDef {}: Received command to change {} from {} to {}, but the previous value was {}",
                    serviceRecipientId, actionDefId, field, command.from, command.to, prevValue);
        }
    }

    protected void warnIfPrevValueDoesntMatch(long serviceRecipientId, long actionDefId, ChangeViewModel<LocalDate> command, DateTime prevValue, String field) {
        if (!nullSafeEquals(prevValue, asUTCDateTimeOrNull(command.from))) {
            log.warn("srId {} ActionDef {}: Received command to change {} from {} to {}, but the previous value was {}",
                    serviceRecipientId, actionDefId, field, command.from, command.to, prevValue);
        }
    }

    protected <T> void warnIfPrevValueDoesntMatch(VM commandVM, ChangeViewModel<T> change, T prevValue, String field) {
        if (!nullSafeEquals(prevValue, change.from)) {
            log.warn("WRONG COMMAND 'from' VALUE: received command to change {} from {} to {}, but the previous value was {}. Command {}",
                    field, change.from, change.to, prevValue, commandVM);
        }
    }

    protected DateTime asUTCDateTimeOrNull(LocalDate date) {
        return date == null ? null : date.toDateTimeAtStartOfDay(DateTimeZone.UTC);
    }

}
