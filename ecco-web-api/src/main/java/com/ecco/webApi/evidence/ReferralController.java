package com.ecco.webApi.evidence;

import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.calendar.CombinedEntry;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.webApi.buildings.BuildingController;
import com.ecco.webApi.calendar.EventResourceAssembler;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.*;
import com.ecco.dom.*;
import com.ecco.dto.ReferralRelationshipDto;
import com.ecco.dto.ServicesProjectsDto;
import com.ecco.infrastructure.health.HealthEndpoint;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.service.EventService;
import com.ecco.service.ReferralService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.repositories.ProjectRepository;
import com.ecco.serviceConfig.repositories.ServiceRepository;
import com.ecco.webApi.annotations.DeleteJson;
import com.ecco.webApi.contacts.ClientDefinitionFromViewModel;
import com.ecco.webApi.contacts.ClientToViewModel;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.controllers.*;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.FluentIterable;
import com.ecco.calendar.core.webapi.EventResource;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.composed.web.rest.json.PostJsonReturningJson;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.StreamSupport;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;
import static org.joda.time.DateTimeZone.UTC;
import static org.springframework.format.annotation.DateTimeFormat.ISO.DATE;
import static org.springframework.hateoas.IanaLinkRelations.SELF;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@PreAuthorize("hasRole('ROLE_STAFF')")
@RestController
public class ReferralController extends ReferralBaseController {

    private final ObjectMapper objectMapper;
    private final CreateServiceRecipientCommandController createServiceRecipientCommandController;
    private final ReferralRepository referralRepository;
    private final FixedContainerRepository fixedContainerRepository;
    private final ReportUnsecuredDelegator reportUnsecuredDelegator;
    private final EntityRestrictionService entityRestrictionService;
    private final EventService eventService;
    private final ReferralService referralService;

    private final ReferralToViewModel referralToViewModel;
    private final Function<EntityRestrictionService, ReferralToViewModel> referralToViewModelAll;
    private final ReferralSummaryToViewModel referralSummaryToViewModel;
    private final ReferralFromViewModel referralFromViewModel;

    private final EventResourceAssembler eventResourceAssembler;
    private final ServiceRecipientSummaryService serviceRecipientSummaryService;
    private final ListDefinitionRepository listDefinitionRepository;

//    @PersistenceContext
//    private transient EntityManager em;

    public ReferralController(CreateServiceRecipientCommandController createServiceRecipientCommandController,
                              ObjectMapper objectMapper,
                              FundingSourceRepository fundingSourceRepository, LocalAuthorityRepository localAuthorityRepository,
                              ReferralRepository referralRepository, ServiceRepository serviceRepository, ProjectRepository projectRepository,
                              ReferralService referralService, EntityRestrictionService entityRestrictionService, IndividualRepository individualRepository,
                              ListDefinitionRepository listDefinitionRepository,
                              FixedContainerRepository fixedContainerRepository,
                              EventService eventService,
                              SignatureRepository signatureRepository,
                              ClientRepository clientRepository,
                              ClientToViewModel clientToViewModel,
                              @Qualifier("referralEventAssembler") EventResourceAssembler eventResourceAssembler,
                              ReportUnsecuredDelegator reportUnsecuredDelegator,
                              ServiceCategorisationRepository serviceCategorisationRepository,
                              ServiceRecipientSummaryService serviceRecipientSummaryService) {
        super(referralRepository, clientRepository, entityRestrictionService, clientToViewModel, listDefinitionRepository);
        this.createServiceRecipientCommandController = createServiceRecipientCommandController;
        this.objectMapper = objectMapper;
        this.referralRepository = referralRepository;
        this.reportUnsecuredDelegator = reportUnsecuredDelegator;
        this.referralService = referralService;
        this.entityRestrictionService = entityRestrictionService;
        this.eventService = eventService;
        this.fixedContainerRepository = fixedContainerRepository;
        this.referralFromViewModel = new ReferralFromViewModel(fundingSourceRepository, localAuthorityRepository,
                serviceRepository, serviceCategorisationRepository, projectRepository, individualRepository,
                signatureRepository, clientRepository, listDefinitionRepository);
        this.eventResourceAssembler = eventResourceAssembler;
        this.referralToViewModel = new ReferralToViewModel(listDefinitionRepository);
        this.referralSummaryToViewModel = new ReferralSummaryToViewModel();
        this.referralToViewModelAll = (EntityRestrictionService restrictionService) -> new ReferralToViewModel(listDefinitionRepository)
                .withContacts()
                .withReviewDates()
                // apply _readOnly if not part of the users acl security
                .withRestrictions(EntityRestrictionService.getServicesProjectsDto(restrictionService));
        this.serviceRecipientSummaryService = serviceRecipientSummaryService;
        this.listDefinitionRepository = listDefinitionRepository;
    }

    /**
     * @return the referral if access is allowed.  Referral will have _readOnly set if not allowed direct access
     *  to the referral but allowed access to parent. Throws NotFoundException if no referral found for that id
     *  @throws AccessDeniedException if not allowed access to referral or a parent
     */
    @GetJson("/referrals/{id}/")
    @HealthEndpoint("referral.full.single")
    public ReferralViewModel findOneReferral(@PathVariable long id) {
        return this.findReferralWithEvents(id, this.referralToViewModel);
    }

    // get the id - with hateos the whole URL is the id
    // but for the purposes of retrofitting, getting the id like this is acceptable
    // NB see https://github.com/spring-projects/spring-hateoas/issues/66
    // NB and http://tommyziegler.com/how-to-expose-the-resourceid-with-spring-data-rest/
    public static Pattern findOneReferralPattern = Pattern.compile("referrals/(\\d+)/");
    public static final Function<String, Long> EXTRACT_ID_FN = (href) -> {
        Matcher matches = findOneReferralPattern.matcher(href);
        matches.find();
        String rId = matches.group(1);
        return Long.parseLong(rId);
    };

    @GetJson("/referrals/{id}/summary/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    public ReferralSummaryViewModel findOneReferralSummary(@PathVariable long id) {
        ReferralSummary referral = referralRepository.findOneReferralSummary(id);
        EntityRestrictionService.verifyAccess(referral.serviceRecipientId, referral.serviceIdAcl, referral.projectIdAcl, entityRestrictionService);
        return referralSummaryToViewModel.apply(referral);
    }

    /**
     * Used by reports (ROLE_REPORTS) to get related referral information.
     * NB One of a few methods in the class to query for multiple referrals (in one go)
     */
    @GetJson("/referrals/byServiceRecipients/summary/")
    @PreAuthorize("hasAnyRole('ROLE_REPORTS')")
    public Iterable<ReferralSummaryViewModel> findAllReferralSummaryByServiceRecipients(@RequestParam(name = "ids") List<Integer> serviceRecipientIds) {
        List<ReferralSummary> referrals = referralRepository.findAllAsReferralSummary(QReferral.referral.serviceRecipient.id.in(serviceRecipientIds));
        referrals.forEach(r -> EntityRestrictionService.verifyAccess(r.serviceRecipientId, r.serviceIdAcl, r.projectIdAcl, entityRestrictionService));
        return referrals.stream().map(referralSummaryToViewModel).collect(toList());
    }

    /**
     * Used by group support (ROLE_STAFF) to get related referral information.
     * NB One of a few methods in the class to query for multiple referrals (in one go)
     */
    @GetJson("/referrals/byServiceRecipients/summaryNoAcl/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF')")
    public Iterable<ReferralSummaryViewModel> findAllUnsecuredAclReferralSummaryByServiceRecipients(@RequestParam(name = "ids") List<Integer> serviceRecipientIds) {
        List<ReferralSummary> referrals = referralRepository.findAllAsReferralSummary(QReferral.referral.serviceRecipient.id.in(serviceRecipientIds));
        // acl access could fail if staff have different access to the group support people on the list
        // where we'd get a 403 red bar error on the page - so we apply and use the 'readOnly' property
        // as per ReferralRepositoryImpl.findAllReferralSummaryByClient
        // load the users restrictions - which loads services and projects
        ServicesProjectsDto restrictions = EntityRestrictionService.getServicesProjectsDto(entityRestrictionService);
        referrals.forEach(r -> {
            r._readOnly = !restrictions.canAccess(r.serviceIdAcl, r.projectIdAcl);
            ReferralRepositoryImpl.populateParentServiceRecipientId(r, referralRepository);
        });
        return referrals.stream().map(referralSummaryToViewModel).collect(toList());
    }

    /**
     * Return the referral matching this serviceRecipientId
     */
    @GetJson("/referrals/byServiceRecipient/{serviceRecipientId}/summaryAsReferral/")
    @PreAuthorize("hasAnyRole('ROLE_STAFF','ROLE_REPORTS')")
    public ReferralSummaryViewModel findOneReferralSummaryByServiceRecipientAsReferral(@PathVariable Integer serviceRecipientId) {
        ReferralSummary referral = referralRepository.findOneReferralSummaryByServiceRecipientId(serviceRecipientId);
        // as we move to more servicerecipient-based data/reporting, some reports haven't caught up
        // and so the report asks for a referral when it might not be (eg tasks report), so we instead
        // we load the sr so that the report might have a change at continuing
        if (referral == null) {
            var srSummary = serviceRecipientSummaryService.findOne(serviceRecipientId);
            EntityRestrictionService.verifyAccess(srSummary.serviceRecipientId, srSummary.serviceIdAcl, srSummary.projectIdAcl, entityRestrictionService);
            var rvm = new ReferralSummaryViewModel();
            // fudged building, or repair etc
            rvm.mapFrom(srSummary);
            return rvm;
        }
        EntityRestrictionService.verifyAccess(referral.serviceRecipientId, referral.serviceIdAcl, referral.projectIdAcl, entityRestrictionService);
        return referralSummaryToViewModel.apply(referral);
    }

    // DUPL of findSummaryByClientId except uses a referral summary
    // NB security applied by setting the readOnly flag if no access allowed
    @GetJson("/clients/{clientId}/referrals/summary/")
    public Iterable<ReferralSummaryViewModel> findUnsecuredSummaryByClientAsReferral(@PathVariable long clientId) {
        List<ReferralSummary> referrals = referralRepository.findAllReferralSummaryByClient(clientId);
        return referrals.stream().map(referralSummaryToViewModel).collect(toList());
    }

    private ReferralViewModel findReferralWithEvents(long id, ReferralToViewModel referralToViewModel) {
        Referral referral = referralRepository.findById(id).orElseThrow(() -> new NotFoundException(id));

        verifyAccess(referral);

        if (referral.getClient() == null) {
            // at least be hepful to client code to describe what the problem is
            throw new IllegalStateException("referral has no client: " + id);
        }

        Collection<CombinedEntry> events = eventService.getNearbyCalendarEventsForContact(referral.getClient().getContact().getCalendarId(), null);

        return assignReferralWithNearbyEvents(referral, events, referralToViewModel);
    }

    // TODO add ReferralToViewModel.withEvents to avoid this loading then assigning
    private ReferralViewModel assignReferralWithNearbyEvents(Referral referral, Collection<CombinedEntry> events, ReferralToViewModel referralToViewModel) {
        ReferralViewModel result = referralToViewModel.apply(referral);
        result.calendarEvents = events == null ? null
                : FluentIterable.from(events).transform(this.eventResourceAssembler::toModel).toList();

        return result;
    }

    /**
     * Returns normally if write-access is allowed, or read-only (because can access parent referral)
     * and throws {@link AccessDeniedException} if cannot access at all.
     */
    private void verifyAccess(@NonNull Referral referral) {
        if (canAccess(referral)) {
            return; // full access to this referral
        }
        if (referral.getParentEvidenceCapable() != null && canAccess(referral.getParentEvidenceCapable())) {
            return; // read-only
        }
        // no parent or no access to parent, no access
        throw new AccessDeniedException("access denied for referral id: " + referral.getId());
    }
    private boolean canAccess(Referral referral) {
        ServicesProjectsDto restrictions = EntityRestrictionService.getServicesProjectsDto(entityRestrictionService);
        return restrictions.canAccess(referral.getServiceRecipient().getPermissionServiceId(), referral.getServiceRecipient().getPermissionProjectId());
    }

    /**
     * Return the referral matching this serviceRecipientId
     */
    @PreAuthorize("hasAnyRole('ROLE_STAFF', 'ROLE_COMMISSIONER')")
    @GetJson("/referrals/byServiceRecipient/{serviceRecipientId}/")
    public ReferralViewModel findOneByServiceRecipient(@PathVariable Integer serviceRecipientId) {
        Referral referral = referralRepository.findByServiceRecipient_Id(serviceRecipientId);
        // return the behaviour as if one result
        ReferralToViewModel referralToViewModel = new ReferralToViewModel(listDefinitionRepository)
                .withRestrictions(EntityRestrictionService.getServicesProjectsDto(entityRestrictionService));
        return referralToViewModel.apply(referral);
    }

    /**
     * Return the first referral with this code. Currently there could be more referrals as there are no restrictions on the code.
     */
    @GetJson("/referrals/byCode/{code}/")
    public ReferralViewModel findOneByCode(@PathVariable String code) {
        List<Referral> referrals = referralRepository.findAllByCode(code);
        // return the behaviour as if one result
        if (referrals.isEmpty()) {
            throw new NotFoundException(code);
        } else {
            return referrals.stream().map(referralToViewModel).findFirst().get();
        }
    }

    /**
     * Return the serviceRecipientId only of this referral.
     */
    @GetJson("/referrals/{id}/serviceRecipientId/")
    public Integer findServiceRecipientIdByReferralId(@PathVariable Integer id) {
        return referralRepository.getServiceRecipientId(id);
    }

    /**
     * Returns associated referrals for this referral (eg family relationships)
     * (see ReferralFlowAction.setupMultipleReferralHeader for current live usage)
     */
    @GetJson("/referrals/{id}/related/")
    public Iterable<ReferralRelationshipDto> findRelatedReferrals(@PathVariable long id) {

        Referral referral = referralRepository.findById(id).orElse(null);
        Long primaryReferralId = referralService.getPrimaryReferralId(referral);

        if (primaryReferralId == null) {
            throw new NotFoundException(id);
        }

        return findRelatedReferralsToPrimary(primaryReferralId);
    }

    /**
     * When we know we have a primary referral, get the relationships directly.
     * This is used in reports, so we could just return a ReferralViewModel should we need
     * more properties from ReferralDto.
     */
    @GetJson("/referrals/primary/{primaryReferralId}/related/")
    public Iterable<ReferralRelationshipDto> findRelatedReferralsToPrimary(@PathVariable long primaryReferralId) {
        return referralService.getRelationshipReferralsDto(primaryReferralId);
    }

    /**
     * Returns child referrals of this parent referral (eg central processing)
     */
    @GetJson("/service-recipients/{srid}/children/")
    public Iterable<ReferralViewModel> findChildReferrals(@PathVariable int srid) {

        long referralId = referralRepository.getReferralIdByServiceRecipientId(srid);
        List<Referral> referrals = referralRepository.findAllByParentReferral_Id(referralId);
        return referrals.stream().map(referralToViewModel).collect(toList());
    }

    /**
     * Returns referrals relevant for the current user
     */
    @Deprecated
    @RequestMapping(value = "/referrals/",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public Iterable<ReferralViewModel> findForOffline() {
        return findMyLive();
    }

    /**
     * Returns my live referrals - default filter is 'live'.  If the support worker has a syncGroup set, then
     * returns all referrals where the allocated support worker is a member of the current user's syncGroup.
     * NB find offline referrals based on 'my' referrals and calendar events nearby
     */
    @GetJson("/user/_self/referrals/filter/live/")
    public Iterable<ReferralViewModel> findOffline(
            HttpServletResponse response,
            @RequestParam(required = false) Instant sinceInstant
    ) {
        cacheForXSecs(15, response);
        return getReferralsAssignedToMeLive(true, sinceInstant);
    }

    @GetJson("/user/_self/referrals/")
    public Iterable<ReferralViewModel> findMyLive() {
        return getReferralsAssignedToMeLive(false, null);
    }

    /** Pick referrals with commands that were saved to the database (created is when it was saved -
     * createdCommand is client-side) since we last synced.
     * We go an hour before to allow some client clock skew and for last-sync-time being when sync was completed.
     * The result is that we'll continually get referrals for an hour after they were last modified */
    private Predicate<Referral> hasUpdatesSinceSync(@NonNull Instant lastSync) {
        DateTime since = new DateTime(lastSync.toEpochMilli()).minusHours(1);
        // Note: We could exclude UserAccessAuditCommand from this so that a view doesn't trigger sync
        return r -> !commandRepository.findAllByServiceRecipientIdAndCreatedGreaterThanEqualOrderByCreatedDesc(
                        r.getServiceRecipientId(), since
                ).isEmpty();
    }

    public Collection<ReferralViewModel> getReferralsAssignedToMeLive(boolean offline, @Nullable Instant lastSync) {
        User user = SecurityUtil.getAuthenticatedUser();
        if (user.getSyncGroup() != null) {
            // TODO: Filter group result to LIVE referrals only: ECCO-1318
            List<Referral> referrals = referralRepository.findAllReferralsForSupportWorkerInGroup(user.getSyncGroup().getName());
            var stream = referrals.stream();
            if (lastSync != null) {
                stream = stream.filter(hasUpdatesSinceSync(lastSync));
            }
            return stream.map(referralToViewModel).collect(toList());
        }

        // ===============
        // ADD REFERRALS ASSIGNED TO ME
        ReportCriteriaDto dto = new ReportCriteriaDto();
        // LIVE - although we could also exclude deleted, as per ReferralListController - referral.requestedDelete.isNull();
        // (but this now only benefits a few cases - those requested, but not deleted)
        dto.setReferralStatus(ReferralStatusName.LiveAtEnd.getName());
        Individual assignedToMe = user.getContact();
        dto.setSupportWorkerId(assignedToMe.getId().intValue());

        // NB this doesn't call 'addChildSrIdsIfNecessary' which reportReferrals would
        var referrals = reportUnsecuredDelegator.reportReferralsRaw(null, dto);

        // OFFLINE DATA: get the referral's 'appointments' and 'review dates', and 'contacts'
        var stream = StreamSupport.stream(referrals.spliterator(), false);
        if (lastSync != null) {
            stream = stream.filter(hasUpdatesSinceSync(lastSync));
        }
        var resultDtos = stream.map(r -> {
            if (offline) {
                Collection<CombinedEntry> events = eventService.getNearbyCalendarEventsForContact(r.getClient().getContact().getCalendarId(), null);
                // and 'review dates', and 'contacts' - see getReferralWithNearbyEvents
                return assignReferralWithNearbyEvents(r, events, this.referralToViewModelAll.apply(this.entityRestrictionService));
            } else {
                return this.referralToViewModel.apply(r);
            }
        }).collect(toList());


        // ===============
        // ADD REFERRALS 'near me' IN THE CALENDAR - NB OBEYS ACLs now via findReferralWithEvents
        // add referrals+appts for referrals who have appointments near me
        // currently these are events with 'CustomEventWithServiceRecipient' (EventType.Review, EventType.Interview, EventType.Meeting)
        // and 'DemandSchedule'
        Collection<CombinedEntry> userEvents = eventService.getNearbyCalendarEventsForContact(user.getContact().getCalendarId(), null);
        // determine the live/offline referrals by extracting referralIds from CustomEventWithServiceRecipient
        Set<Long> referralIdsToAddFromUsersNearbyAppts = userEvents.stream()
                .map(e -> referralService.getReferralId(e.getIcalEntry()))
                .filter(Objects::nonNull)
                .collect(toSet());
        List<Long> resultIds = resultDtos.stream().map(r -> r.referralId).collect(toList());
        for (Long referralId : referralIdsToAddFromUsersNearbyAppts) {
            if (!resultIds.contains(referralId)) {
                // NB this doesn't call 'addChildSrIdsIfNecessary' which reportReferrals would
                var referral = this.findReferralWithEvents(referralId, offline
                        ? this.referralToViewModelAll.apply(this.entityRestrictionService)
                        : this.referralToViewModel);
                // Don't include closed referrals.
                // NB Here we mimic the approach of ReferralSummaryView which gets the status of an individual Referral
                // This ignores future exit dates, unlike the report method ReferralStatusCommonPredicates.live
                // we thought we could ignore this - it feels like a response to incorrect 'live' status
                // but we've corrected up the status - this is about seeing closed clients on 'my clients' and offline
                // NB The true solution is to use ReferralCloseOff event to check with the user that things are being removed
                //  (eg calendar events - and recurring, tasks etc)
                // NB this is the correct use of closed
                if (!ReferralSummary.Support.isClosed(referral)) {
                    resultDtos.add(referral);
                }
            }
        }

        return resultDtos;

    }

    /**
     * Used by 'quick log' to get live referrals in the service
     * NB One of a few methods in the class to query for multiple referrals (in one go)
     */
    @PostJsonReturningJson("/referrals/summary/")
    public Stream<ReferralSummaryViewModel> referralsSummary(@RequestBody ReportCriteriaDto dto) {
        // gets the referrals this user has access to, according to the criteria
        return reportUnsecuredDelegator.reportReferralSummaryViewModel(dto, null, false);
    }

    @GetJson("/buildings/{buildingId}/referrals/")
    public Iterable<ReferralViewModel> findByBuildingId(@PathVariable int buildingId) {
        var bldgsHierarchical = BuildingController.getFixedContainersHierarchical(fixedContainerRepository, buildingId);
        var bldgIds = StreamSupport.stream(bldgsHierarchical.spliterator(), false).map(b -> b.getId()).toList();
        // TODO OR live only OR change address to NFA? OR close off removes 'residenceId'?
        List<Referral> referrals = referralRepository.findAllByClient_ResidenceIdIn(bldgIds);
        return referrals.stream().map(referralToViewModel).collect(toList());
    }

    @GetJson("/buildings/{buildingId}/calendar/clients/")
    public List<EventResource> findCalendarEventsForBuildingClients(
            @PathVariable int buildingId,
            @RequestParam @DateTimeFormat(iso = DATE) LocalDate start,
            @RequestParam @DateTimeFormat(iso = DATE) LocalDate end
    ) {
        var calendarIds = referralRepository.findDistinctCalendarIdsByClient_Residence_Id(buildingId);
        var entries = eventService.getCalendars(calendarIds, start.toDateTimeAtStartOfDay(UTC), end.toDateTimeAtStartOfDay(UTC));
        return eventResourceAssembler.toResourceFromEntries(calendarIds, entries);
    }

    @PostJsonReturningJson("/referrals/byClient/query/")
    public Iterable<ReferralViewModel> queryAllFilteredReferralsByClientExample(@RequestBody ClientViewModel exemplar) {
        Iterable<Referral> referrals = referralService.queryReferralsByClientExample(new ClientDefinitionFromViewModel().apply(exemplar), false);
        return StreamSupport.stream(referrals.spliterator(), false).map(referralToViewModel).collect(toList());
    }

    @PostJsonReturningJson("/referrals/unfiltered/byClient/query/")
    public Iterable<ReferralViewModel> queryAllUnfilteredReferralsByClientExample(@RequestBody ClientViewModel exemplar) {
        Iterable<Referral> referrals = referralService.queryReferralsByClientExample(new ClientDefinitionFromViewModel().apply(exemplar), true);
        return StreamSupport.stream(referrals.spliterator(), false).map(referralToViewModel).collect(toList());
    }
    @PostJson("/referrals/")
    public Result create(@RequestBody ReferralViewModel referralViewModel) throws IOException {
        return createImport(referralViewModel);
    }

    /**
     * Version that is not secured by Spring Security
     */
    @PreAuthorize("isFullyAuthenticated()")
    @NonNull
    Result createImport(ReferralViewModel referralViewModel) throws IOException {
        Assert.isNull(referralViewModel.referralId, "No id should be set on POST");

        // for creating, we should only do a code match if we aren't null
        if (StringUtils.isNotBlank(referralViewModel.referralCode)) {
            List<Referral> matchingReferrals = referralRepository.findAllByCode(referralViewModel.referralCode);
            throwForExistingOrTooManyMatches(matchingReferrals);
        }

        CreateReferralCommandViewModel createVm = new CreateReferralCommandViewModel(referralViewModel);
        CreateServiceRecipientParams params = new CreateServiceRecipientParams(ReferralServiceRecipient.PREFIX);
        String createVmStr = objectMapper.writeValueAsString(createVm);
        Result commandResult = createServiceRecipientCommandController.createServiceRecipientCommand(SecurityContextHolder.getContext().getAuthentication(), params, createVmStr);

        // return what was expected previously - before a command was used
        if (commandResult.isCommandSuccessful()) {
            // TODO we could avoid this if BaseCommandHandler.createCommandAndHandleInternal was to return the id of the entity, not command
            long rId = ReferralController.EXTRACT_ID_FN.apply(commandResult.getLink(SELF.value()).getHref());
            return new Result(rId);
        }
        // if an error - return it
        return commandResult;
    }

    /**
     * Update a referral with several properties from the view model
     * For now we carefully control which properties and circumstances the data is updated
     */
    @RequestMapping(value = "/referrals/byCode/{code}/", method = RequestMethod.PUT, consumes = APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public Result update(@PathVariable String code, @RequestBody ReferralViewModel referralViewModel) {
        Assert.notNull(referralViewModel.referralCode, "referralCode is required on body of the PUT");

        List<Referral> matchingReferrals = referralRepository.findAllByCode(referralViewModel.referralCode);
        if (matchingReferrals.size() != 1) {
            throw new RuntimeException("Incorrect referral count, need 1 but found " + matchingReferrals.size() + " from referralCode: " + referralViewModel.referralCode);
        }

        Referral referral = matchingReferrals.get(0);
        final Referral referralIn = referralFromViewModel.apply(referralViewModel);

        // apply desired properties - if nothing already applied
        if (referralIn.getSupportWorker() != null) {
            if (referral.getSupportWorker() == null) {
                referral.setSupportWorker(referralIn.getSupportWorker());
            }
        }

        if (referralIn.getSrcGeographicArea() != null) {
            referral.setSrcGeographicArea(referralIn.getSrcGeographicArea());
            referralRepository.updateArea(referral.getId(), referralIn.getSrcGeographicArea().getId());
        }

        // RT 'hack' - see referralDetails_rainbow1.jsp "customObjectData['referral.localAuthority']"
        HashMap<String, Object> coIn = referralIn.getCustomObjectData();
        if (coIn != null && coIn.containsKey(ReferralViewModel.textMap_localAuthorityKey)) {
            HashMap<String, Object> co = referral.getCustomObjectData();
            // if not set, apply
            if (!referral.getCustomObjectData().containsKey(ReferralViewModel.textMap_localAuthorityKey)) {
                co.put(ReferralViewModel.textMap_localAuthorityKey, coIn.get(ReferralViewModel.textMap_localAuthorityKey));
            }
            // get the imported property in a hidden textmap so could manipulate later without data import
            co.put(ReferralViewModel.textMap_localAuthorityKey + "Imported", coIn.get(ReferralViewModel.textMap_localAuthorityKey));
        }

        referralService.setReferral(referral);
        //em.flush();

        return new Result(referral.getId());
    }

    /**
     * Fulfils the need where the acceptance tests want to set a referral 'code'
     * which is not available through the ui
     */
    @RequestMapping(value = "/applyCode", method = RequestMethod.POST)
    public String applyCode(@RequestParam long id, @RequestParam String code) {
        Referral r = referralRepository.findById(id).orElseThrow();
        r.setCode(code);
        referralRepository.save(r);
        return "{}";
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @DeleteJson("/referrals/{id}/")
    public Result delete(@PathVariable Long id, @RequestBody ReferralViewModel referralViewModel) {
        Assert.isNull(referralViewModel.referralId, "No id should be set in request body on DELETE");

        Referral existing = referralRepository.findById(id).orElseThrow();

        if (existing.getClient() != null) {
            Assert.notNull(referralViewModel.clientId, "Must specify clientId when deleting a referral");
            Assert.state(referralViewModel.clientId.equals(existing.getClient().getId()), "The supplied clientId did not match");
        }

        referralRepository.deleteById(id);
        return new Result("deleted");
    }


    @RequestMapping(value = "/hidden/referrals/", method = RequestMethod.GET, produces = APPLICATION_JSON_VALUE)
    public Iterable<ReferralSummary> findRequestedDelete() {
        return referralRepository.findAllIncludingHiddenAsReferralSummary(QReferral.referral.requestedDelete.isNotNull());
    }

    /**
     * Move a referral to another clientId
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @PostJson("/move/referrals/{referralId}/client/{clientId}/")
    public Result move(@PathVariable long referralId, @PathVariable long clientId) {
        referralService.moveReferralToClient(referralId, clientId);

        return new Result("moved", referralId);
    }

    /**
     * Move a referral to another serviceId
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @PostJson("/move/referrals/{referralId}/service/{serviceId}/")
    public Result moveService(@PathVariable long referralId, @PathVariable long serviceId) {
        referralService.moveReferralToService(referralId, serviceId);

        return new Result("moved", referralId);
    }

    /**
     * Move a referral to the 'hidden' collection.
     * It will then not appear on any normal queries for referrals.
     * This does not update any other properties of the referral.
     *
     * @param referralViewModel the referral to hide, only the referralId is relevant
     */
    @PostJson("/hidden/referrals/")
    @ResponseStatus(HttpStatus.OK)
    public Result deleteRequest(@RequestBody ReferralViewModel referralViewModel) {
        Assert.notNull(referralViewModel.referralId, "referralId is required on body of the POST");
        referralService.hideReferral(referralViewModel.referralId);

        return new Result("hidden", referralViewModel.referralId);
    }

    /**
     * Delete a referral from the 'hidden' collection.
     * It will then start to appear on normal queries for referrals again.
     * This does not update any other properties of the referral.
     *
     * @param id the referral to cancelDeleteRequest
     */
    @RequestMapping(value = "/hidden/referrals/{id}/", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    public Result cancelDeleteRequest(@PathVariable long id) {
        referralService.unhideReferral(id);
        return new Result("unhidden", id);
    }


}
