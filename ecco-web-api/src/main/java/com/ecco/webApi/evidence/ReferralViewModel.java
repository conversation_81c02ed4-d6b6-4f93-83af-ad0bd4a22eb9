package com.ecco.webApi.evidence;

import com.ecco.dom.Agency;
import com.ecco.dom.FundingSource;
import com.ecco.dom.ReferralServiceRecipient;
import com.ecco.webApi.contacts.AgencyViewModel;
import com.ecco.webApi.contacts.ContactViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.webApi.viewModels.ServiceRecipientSourceViewModel;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class ReferralViewModel extends ReferralSummaryViewModel implements ServiceRecipientSourceViewModel {

    public ArrayList<Long> individualContacts = new ArrayList<>();

    public List<ContactViewModel> contacts;

    /**
     * The id of the {@link Agency} (provider) which delivers this service
     */
    public Long deliveredById;

    /**
     * The date the provider started providing the service
     */
    public LocalDate deliveredByStartDate;

    /**
     * The name of the {@link FundingSource} who funded this referral
     */
    public String fundingSource;

    // START: FOR ACCEPTANCE TESTS

    /**
     * For legacy (non-Activiti) referrals, the index of the current
     * Referral Aspect in the sequence of Referral Aspects.
     *
     * @deprecated Legacy - provided for use by acceptance tests only.
     */
    @Deprecated
    public String currentTaskDefinitionIndex;

    @Deprecated
    public DateTime created;
    // END: FOR ACCEPTANCE TESTS

    // START: FOR API
    /** For import only.  TODO: Replace with commands for import */
    public boolean callAcceptOnService;
    // END: FOR API


    // START: FOR RT DATA IMPORT
    // we don't yet have this as a field in the table but probably should
    // so its here to indicate that it has special processing - to a textMap
    // we could alternatively avoid the special processing and
    // work out a ReferralAggregateImporter.fieldAliases to set directly to a map
    public static final String textMap_localAuthorityKey = "referral.localAuthority";
    /**
     * The local authority.
     *
     * @deprecated Legacy - provided for RT data import only
     */
    @Deprecated
    public String localAuthority;
    // END: FOR RT DATA IMPORT


    /**
     * The source of the referral, either the name of the agency that
     * initiated the referral, or "self referral".
     */
    public String source;
    /**
     * The source agency - if there is one
     */
    public AgencyViewModel sourceAgency;

    /**
     * For import purposes
     */
    public String importServiceName;
    public String importProjectName;

    /**
     * The name of the agency that delivers the referred service, or null if
     * the service is delivered in-house by the service provider.
     */
    public String delivererAgencyName;

    /** The SVG XML of the signature */
    public String dataProtectionSignatureSvgXml;

    /**
     * The name of the interviewer1 assigned, as displayed in the user interface.
     */
    public String interviewer1WorkerDisplayName;
    /**
     * The name of the interviewer2 assigned, as displayed in the user interface.
     */
    public String interviewer2WorkerDisplayName;

    /**
     * The reason this referral was exited.
     * This is not the signposted/rejected reason.
     */
    public String exitReason;

    public String exitComment;

    public String signpostedComment;

    /**
     * The reason this referral was rejected/signposted.
     * This is not the exit reason.
     */
    public String signpostedReason;

    /**
     * The name of the agency that the referral was signposted/rejected to
     */
    public String signpostedAgencyName;

    /**
     * The name of the support worker assigned to this referral, as displayed
     * in the user interface.
     */
    public String supportWorkerDisplayName;

    /**
     * Within central processing, referrals can be created/allocated to different services after an initial assessment
     * or central process. The parentReferralId represents the referral which this referral was created from.
     */
    public Long parentReferralId;

    /**
     * One child referral is allowed to be the "primary service" for that user.
     */
    public boolean isPrimaryChildReferral;

    /**
     * The relationship this client is to the primary referral
     */
    public Integer primaryRelationshipId;

    /**
     * Map of string values we want as key:value pairs available for viewing and editing
     */
    public HashMap<String, String> textMap = new HashMap<>();

    /**
     * Map of dd/MM/yyyy dates we want as key:value pairs available for viewing and editing
     */
    public HashMap<String, String> dateMap = new HashMap<>();

    /** The calendar events that link to this referral */
    public List<EventResource> calendarEvents;

    /** Map to entries where options are in sessionData.getListDefinitions[id] */
    public Map<String, ListDefinitionEntryViewModel> choicesMap = new HashMap<>();

    public List<Integer> childServiceRecipientIds;

    /** Future review dates.
     *  Populated when finding referrals with events, such as when finding just one full referral, but
     *  not always for findAll type of scenarios. */
    public List<LocalDate> reviewDates;

    public ReferralViewModel() {
        super();
        this.prefix = ReferralServiceRecipient.PREFIX;
    }

    // Property accessors are unfortunately required for com.ecco.data.client.dataimport.csv.CSVBeanReader.
    // See ECCO-703

}
