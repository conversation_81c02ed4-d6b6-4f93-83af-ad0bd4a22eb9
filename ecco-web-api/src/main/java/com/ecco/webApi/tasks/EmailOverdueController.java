package com.ecco.webApi.tasks;

import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.dom.IndividualUserSummary;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.evidence.repositories.TaskStatusRepository;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.security.dom.Group;
import com.ecco.security.dom.User;
import com.ecco.security.repositories.UserRepository;
import com.ecco.service.acls.CachedAclVisibilityService;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.messaging.EmailService;
import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.TaskStatusToViewModel;
import com.ecco.webApi.viewModels.TaskStatusViewModel;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import org.jspecify.annotations.Nullable;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/email-alert")
public class EmailOverdueController extends EmailBaseController {

    private final TaskStatusRepository taskStatusRepository;
    private final CachedAclVisibilityService aclService;
    private final SoftwareFeatureService featureService;
    private final TaskStatusToViewModel taskStatusToViewModel;

    // NB Also consider setting -Decco.websiteUrl for the email content
    @Value("${enableEmailAlerts:false}")
    private boolean enableEmailAlerts;

    @Autowired
    public EmailOverdueController(@NonNull TaskStatusRepository taskStatusRepository,
                                @NonNull UserRepository userRepository,
                                @NonNull CachedAclVisibilityService aclService,
                                @NonNull EmailService emailService,
                                @NonNull ServiceTypeService serviceTypeService,
                                @NonNull ServiceCategorisationRepository serviceCategorisationRepository,
                                @NonNull MessageSource messageSource,
                                @NonNull TaskDefinitionRepository taskDefinitionRepository,
                                @NonNull ServiceRecipientRepository serviceRecipientRepository,
                                @NonNull SoftwareFeatureService featureService,
                                @NonNull ApplicationProperties applicationProperties) {
        super(emailService, serviceTypeService, taskDefinitionRepository, serviceCategorisationRepository, serviceRecipientRepository, userRepository, messageSource, applicationProperties);
        this.taskStatusRepository = taskStatusRepository;
        this.aclService = aclService;
        this.featureService = featureService;
        this.taskStatusToViewModel = new TaskStatusToViewModel(serviceRecipientRepository);
    }

    // runtime config changes may be possible in ScheduledTaskRegistrar
    // runs sometime between 5am and 6am on Sunday - if no provided -DemailSchedule=""
    @Scheduled(cron = "0 #{new java.util.Random().nextInt(59)} 5 * * SUN", zone = "UTC")
    public void trigger() {

        if (!featureService.featureEnabled("ecco.email")) {
            // we don't log this elsewhere, it's the first thing that will be checked, and so avoids log entries
            //log.error("EMAIL NOT SENT - not enabled");
            return;
        }

        if (enableEmailAlerts) {
            emailUserOverdueTasks();
            emailManagerOverdueTasks();
        }
    }

    @GetMapping("/tasks-users/")
    public Result emailUserOverdueTasks() {
        findTasksAndSendUserEmails(null);
        return new Result("Processed all entries");
    }

    @GetMapping("/tasks-users/{userId}/")
    public Result emailUserIdOverdueTasks(@PathVariable Long userId) {
        var user = userRepository.findById(userId).orElseThrow();
        findTasksAndSendUserEmails(user);
        return new Result("Processed all entries for user " + user.getUsername());
    }

    @GetMapping("/tasks-managers/")
    public Result emailManagerOverdueTasks() {
        findTasksAndSendManagerEmails(null);
        return new Result("Processed all entries");
    }

    @GetMapping("/tasks-managers/{userId}/")
    public Result emailManagerIdOverdueTasks(@PathVariable Long userId) {
        var user = userRepository.findById(userId).orElseThrow();
        findTasksAndSendManagerEmails(user);
        return new Result("Processed all entries for user " + user.getUsername());
    }

    private void findTasksAndSendUserEmails(@Nullable User user) {
        findOverdueTasks(user)
                .stream()
                .collect(Collectors.groupingBy(t -> t.assignee))
                .values()
                .forEach(tasks ->
                    // NB emails are sent in the same transaction
                    this.sendEmail(tasks, tasks.get(0).assigneeEmail, this.getSubject(tasks)));
    }

    private void findTasksAndSendManagerEmails(@Nullable User manager) {
        // if needed to collect as we go - but we instead group by serviceAllocationId
        HashMap<Integer, List<IndividualUserSummary>> managersBySvcCatId = new HashMap<>();

        var tasksOverdue = findOverdueTasks(null);

        // gather the tasks per svcCat
        var tasksOverdueBySvcCatId = tasksOverdue
                .stream()
                .collect(Collectors.groupingBy(t -> t.serviceAllocationId));
        tasksOverdueBySvcCatId.forEach((svcAllocId, value) -> {
            var svcCat = this.serviceCategorisationRepository.findById(svcAllocId).orElseThrow();
            var managers = this.aclService.getUsersWithAccessToByGroups(svcCat.getServiceId(), svcCat.getProjectId(), Group.MANAGER_GROUP, Group.SENIORMANAGER_GROUP);
            if (!managersBySvcCatId.containsKey(svcAllocId)) {
                managersBySvcCatId.put(svcAllocId, managers);
            }
        });

        // for each manager, find the tasks for their services
        var uniqueManagers = managersBySvcCatId.values().stream().flatMap(Collection::stream).distinct()
                .filter(m -> manager == null || manager.getId().equals(m.getId()))
                .toList();
        uniqueManagers.forEach(m -> {
            var allManagerSvcCatIds = managersBySvcCatId.entrySet().stream().filter(e -> e.getValue().contains(m)).map(Map.Entry::getKey).toList();
            var allManagerTasks = tasksOverdue.stream().filter(t -> allManagerSvcCatIds.contains(t.serviceAllocationId)).toList();

            // NB emails are sent in the same transaction
            this.sendEmail(allManagerTasks, m.getEmail(), this.getSubject(allManagerTasks) + " [manager]");
        });
    }

    private List<TaskStatusViewModel> findOverdueTasks(@Nullable User user) {
        var tasks = user == null
                ? taskStatusRepository.findAllByCompletedNullAndAssignedUserIsNotNullAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(getNow())
                : taskStatusRepository.findAllByAssignedUserAndCompletedNullAndDueDateBeforeOrderByAssignedUserAscDueDateAsc(user, getNow());
        return tasks.map(taskStatusToViewModel).toList();
    }

    @NonNull
    @Override
    protected String getSubject(List<TaskStatusViewModel> tasks) {
        return "Overdue tasks: " + tasks.size();
    }
}
