package com.ecco.webApi.viewModels;

import com.ecco.dom.Agency;
import com.ecco.dom.Individual;
import com.ecco.webApi.contacts.AgencyToViewModel;
import com.ecco.webApi.contacts.AgencyViewModel;

public interface ServiceRecipientSourceViewModel {

    public static String sourceAsSelfReferral = "self referral";

    public static void sourceOfReferral(boolean selfReferral, Agency agency, Individual referrer,
                                        ServiceRecipientSourceViewModel result, AgencyToViewModel agencyToViewModel) {
        result.setSelfReferral(selfReferral);

        // This used to be if-else, but given database could have selfReferral = true and agency and indivual,
        // then we populate as much as we can, and put source as the highest in heirarchy
        if (selfReferral) {
            result.setSource(ServiceRecipientSourceViewModel.sourceAsSelfReferral);
        }
        if (referrer != null) {
            result.setSource("individual");
            result.setReferrerIndividualId(referrer.getId());
        }
        if (agency != null) {
            result.setSource(agency.getCompanyName());
            result.setReferrerAgencyId(agency.getId());
            result.setSourceAgency(agencyToViewModel.apply(agency));
        }
    }

    void setSelfReferral(boolean selfReferral);
    void setSource(String source); // one-liner of the source
    void setReferrerIndividualId(Long individualId);
    void setReferrerAgencyId(Long agencyId);
    void setSourceAgency(AgencyViewModel sourceAgency);
}
