package com.ecco.webApi.buildings;

import com.ecco.webApi.contacts.address.AddressViewModel;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.ChangeViewModel;

@NullMarked
public class BuildingCommandViewModel extends BaseCommandViewModel {

    @Nullable
    public Integer serviceRecipientId;

    public String operation;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<Boolean> disabled;

    @Nullable
    public ChangeViewModel<String> externalRef;

    @Nullable
    public ChangeViewModel<Integer> location;

    @Nullable
    public ChangeViewModel<AddressViewModel> address;

    @Nullable
    public ChangeViewModel<Integer> resourceType;

    @Nullable
    public ChangeViewModel<Integer> chargeCategoryId;

    @Nullable
    public ChangeViewModel<Integer> parentId;

    private BuildingCommandViewModel() {
        super(UriComponentsBuilder
                .fromUriString("buildings/commands/")
                .toUriString());
    }

    // Mirror buildings/commands.ts
    /**
     * @param operation "add" or "update"
     * @param serviceRecipientId null when doing add, required when doing update.
     */
    public BuildingCommandViewModel(String operation, @Nullable Integer serviceRecipientId) {
        this();
        this.operation = operation;
        this.serviceRecipientId = serviceRecipientId;
    }

    public BuildingCommandViewModel changeName(@Nullable String from, String to) {
        this.name = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeExternalRef(@Nullable String from, @Nullable String to) {
        this.externalRef = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeAddressLocationId(Integer from, @Nullable Integer to) {
        this.location = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeParentId(@Nullable Integer from, @Nullable Integer to) {
        this.parentId = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeResourceTypeId(@Nullable Integer from, @Nullable Integer to) {
        this.resourceType = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeChargeCategoryId(@Nullable Integer from, @Nullable Integer to) {
        this.chargeCategoryId = ChangeViewModel.create(from, to);
        return this;
    }
}
