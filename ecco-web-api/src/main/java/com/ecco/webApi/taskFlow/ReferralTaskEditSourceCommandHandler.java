package com.ecco.webApi.taskFlow;

import org.jspecify.annotations.NonNull;

import javax.annotation.Nonnull;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.ecco.dao.ServiceRecipientContactRepository;
import com.ecco.dom.*;
import com.ecco.dom.repairs.RepairServiceRecipient;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.servicerecipient.ServiceRecipientSource;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.controllers.WorkflowTaskController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
public class ReferralTaskEditSourceCommandHandler extends ServiceRecipientTaskCommandHandler<ReferralTaskEditSourceCommandViewModel> {

    @PersistenceContext
    private EntityManager entityManager;

    @NonNull private final ReferralRepository referralRepository;
    @Nonnull private final ServiceRecipientContactRepository serviceRecipientContactRepository;
    @Nonnull private final ServiceRecipientRepository serviceRecipientRepository;
    @Nonnull private final RepairRepository repairRepository;

    @Autowired
    public ReferralTaskEditSourceCommandHandler(ObjectMapper objectMapper,
                                                @NonNull WorkflowTaskController workflowTaskController,
                                                ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                ServiceRecipientRepository serviceRecipientRepository,
                                                ReferralRepository referralRepository,
                                                RepairRepository repairRepository,
                                                ServiceRecipientContactRepository serviceRecipientContactRepository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository, ReferralTaskEditSourceCommandViewModel.class);

        this.serviceRecipientRepository = serviceRecipientRepository;
        this.referralRepository = referralRepository;
        this.repairRepository = repairRepository;
        this.serviceRecipientContactRepository = serviceRecipientContactRepository;
    }

    @Override
    protected CommandResult handleTaskInternal(Authentication auth, @NonNull ServiceRecipientTaskParams params,
                                               ReferralTaskEditSourceCommandViewModel vm) {
        var sr = this.serviceRecipientRepository.findById(params.serviceRecipientId).get();

        ServiceRecipientSource srSource = null;
        // REFERRAL
        if (sr.getPrefix().equals(ReferralServiceRecipient.PREFIX)) {
            srSource = referralRepository.findByServiceRecipient_Id(params.serviceRecipientId);
        }
        // REPAIR
        if (sr.getPrefix().equals(RepairServiceRecipient.PREFIX)) {
            srSource = repairRepository.findRepairByServiceRecipientId(params.serviceRecipientId).get();
        }

        if (vm.agencyChange != null) {
            Agency agency = vm.agencyChange.to == null ? null
                    : entityManager.getReference(Agency.class, vm.agencyChange.to);
            srSource.setReferrerAgency(agency);
        }

        if (vm.referrerChange != null) {
            Individual referrer = vm.referrerChange.to == null ? null
                    : entityManager.getReference(Individual.class, vm.referrerChange.to);
            srSource.setReferrerIndividual(referrer);
            srSource.setSelfReferral(false);
        }

        // update into 'contacts'
        if (vm.referrerChange != null && vm.referrerChange.to != null) {
            saveAsContact(params, vm.referrerChange);
        } else if (vm.agencyChange != null && vm.agencyChange.to != null) {
            saveAsContact(params, vm.agencyChange);
        }

        if (vm.selfReferralChange != null) {
            if (vm.selfReferralChange.to) {
                srSource.setReferrerAgency(null);
            }
            srSource.setSelfReferral(vm.selfReferralChange.to);
        }
        return null;
    }

    // NB this is the referral contacts svcrec_contacts, not the svccat_contacts
    // the svccat_contacts is added to when Agency/Professionals are created
    private void saveAsContact(ServiceRecipientTaskParams params, @NonNull ChangeViewModel<Long> vm) {
        ReferralTaskBaseAcceptCommandHandler.saveAsContact(this.serviceRecipientContactRepository, params.serviceRecipientId, vm.to);
    }
}
