package com.ecco.webApi.taskFlow;

import com.ecco.dao.ServiceRecipientContactRepository;
import org.jspecify.annotations.NonNull;

import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.evidence.repositories.ServiceRecipientRepository;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.servicerecipient.ServiceRecipientCaseStatusUpdate;
import com.ecco.webApi.controllers.WorkflowTaskController;
import com.ecco.calendar.core.util.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ecco.dao.AgencyRepository;
import com.ecco.dao.ReferralRepository;
import com.ecco.dao.commands.ServiceRecipientCommandRepository;
import com.fasterxml.jackson.databind.ObjectMapper;


@Component
public class ReferralTaskAcceptOnServiceCommandHandler
        extends ReferralTaskBaseAcceptCommandHandler<ReferralTaskAcceptOnServiceCommandViewModel> {

    @Autowired
    public ReferralTaskAcceptOnServiceCommandHandler(
            @NonNull ObjectMapper objectMapper,
            @NonNull WorkflowTaskController workflowTaskController,
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull ServiceRecipientRepository serviceRecipientRepository,
            @NonNull ReferralRepository referralRepository,
            @NonNull IncidentRepository incidentRepository,
            @NonNull RepairRepository repairRepository,
            @NonNull ListDefinitionRepository listDefinitionRepository,
            @NonNull AgencyRepository agencyRepository,
            @NonNull ServiceRecipientContactRepository repository) {
        super(objectMapper, workflowTaskController, serviceRecipientCommandRepository,
                ReferralTaskAcceptOnServiceCommandViewModel.class,
                serviceRecipientRepository,
                referralRepository,
                incidentRepository,
                repairRepository,
                agencyRepository,
                listDefinitionRepository, repository);
    }

    @Override
    protected void changeState(ReferralTaskAcceptOnServiceCommandViewModel vm, ServiceRecipientCaseStatusUpdate r) {
        switch (vm.acceptedState.to) {
            case ACCEPTED:
                r.acceptOnService();
                break;
            case SIGNPOSTED:
                r.prepareAcceptOnServiceSignpost();
                signpost(vm, r, false);
                break;
            case UNSET:
                r.setAcceptedOnService(false);
                // TODO reset lots to null - Need to discuss with Adam how this might affect shared code
                break;
        }

        // Amendments apply to any state if there are any - then apply them
        if (vm.acceptedDate != null) {
            r.setDecisionMadeOn(vm.acceptedDate.to == null ? null : DateTimeUtils.convertFromUsersLocalDate(vm.acceptedDate.to));
        }
    }

    @Override
    protected void updateState(ReferralTaskAcceptOnServiceCommandViewModel vm, ServiceRecipientCaseStatusUpdate r) {
        if (!r.isAcceptedOnService()) {
            signpost(vm, r, true);
        }

        // Amendments apply to any state if there are any - then apply them
        if (vm.acceptedDate != null) {
            r.setDecisionMadeOn(vm.acceptedDate.to == null ? null : DateTimeUtils.convertFromUsersLocalDate(vm.acceptedDate.to));
        }
    }

}
