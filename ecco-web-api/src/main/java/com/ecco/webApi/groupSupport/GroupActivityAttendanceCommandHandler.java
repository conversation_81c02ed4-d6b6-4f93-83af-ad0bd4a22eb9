package com.ecco.webApi.groupSupport;

import com.ecco.dom.Referral;
import com.ecco.dom.groupsupport.*;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.groupsupport.repositories.GroupSupportAttendanceRepository;
import com.ecco.groupsupport.repositories.GroupSupportCommandRepository;
import com.ecco.webApi.CommandResult;
import com.ecco.webApi.evidence.BaseCommandHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;

@Component
public class GroupActivityAttendanceCommandHandler
    extends BaseCommandHandler<GroupActivityAttendanceCommandViewModel, Long, GroupSupportCommand, Void> {

    @PersistenceContext
    private EntityManager entityManager;

    private final GroupSupportAttendanceRepository repository;

    private final GroupSupportActivityInvolvementRepository involvementRepository;

    public GroupActivityAttendanceCommandHandler(ObjectMapper objectMapper, GroupSupportCommandRepository commandRepository,
            GroupSupportAttendanceRepository repository, GroupSupportActivityInvolvementRepository involvementRepository) {
        super(objectMapper, commandRepository, GroupActivityAttendanceCommandViewModel.class);
        this.repository = repository;
        this.involvementRepository = involvementRepository;
    }

    @Override
    protected CommandResult handleInternal(@NonNull Authentication auth, @Nullable Void params, @NonNull GroupActivityAttendanceCommandViewModel viewModel) {
        GroupActivity_Referral_MultiId multiId = new GroupActivity_Referral_MultiId();
        multiId.setActivity(entityManager.getReference(GroupSupportActivity.class, viewModel.activityId));
        multiId.setReferral(entityManager.getReference(Referral.class, viewModel.referralId));

        GroupActivity_Referral activityReferral = involvementRepository.findById(multiId).orElse(null);
        GroupSupportAttendance attendance = repository.findOne(viewModel.activityId, viewModel.referralId, viewModel.attendedAt);

        if (activityReferral == null) {
            activityReferral = new GroupActivity_Referral();
            activityReferral.setMultiId(multiId);
            involvementRepository.save(activityReferral);
        }

        if (attendance == null) {
            attendance = new GroupSupportAttendance();
            attendance.setParentId(multiId);
            attendance.setAttendedAt(viewModel.attendedAt);
        }

        if (viewModel.cancelled != null) {
            attendance.setCancelled(viewModel.cancelled.to);
        }

        if (viewModel.attendedAllDay != null) {
            attendance.setAttendedAllDay(viewModel.attendedAllDay.to);
        }

        repository.save(attendance);
        return null;
    }

    @NonNull
    @Override
    protected GroupActivityAttendanceCommand createCommand(Serializable targetId, @Nullable Void params, @NonNull String requestBody, @NonNull GroupActivityAttendanceCommandViewModel viewModel,
                                                  long userId) {
        return new GroupActivityAttendanceCommand(viewModel.uuid, viewModel.timestamp, userId, viewModel.activityUuid, requestBody);
    }
}