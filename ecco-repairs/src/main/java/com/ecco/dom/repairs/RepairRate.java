package com.ecco.dom.repairs;

import com.ecco.infrastructure.entity.AbstractUnidentifiedVersionedEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Configurable;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "repairs_rates")
@Configurable
@Getter
@Setter
public class RepairRate extends AbstractUnidentifiedVersionedEntity<Integer> {

    @Id
    @Column(name="id", nullable=false) // oracle doesn't like using unique=true
    private Integer id = null;

    private String area;
    private String ref;
    private String code;
    private String description;
    private String unit;
    private BigDecimal rate;

}
