package com.ecco.acceptancetests.api.rota;

import com.ecco.acceptancetests.api.BaseJsonTest;
import org.junit.Ignore;
import org.junit.Test;


/**
 * Tests for the scheduler are done on the rota schedules - eg Rota...APITests.
 * However, this class is to help us remember this, and to document the setup
 */
@SuppressWarnings({"unused"})
public class SchedulerAPITests extends BaseJsonTest {

    @Test
    @Ignore("only for local setup for now")
    public void configSetupScheduler() {
        // update cfg_module set enabled=1 where name='rota';
        // ui - feature rota.scheduler
        // ui - rota permission
        // update cfg_settings set keyvalue=35 where keyname='care.scheduler.days';
        // insert into appointmenttypes
        // list def for care-checks (disable complete/not-complete) add new ones / care-locations
        // create visit schedule, withOUT direct task (its exclusive) and 'visit tasks' target schedule
        // if not sync'd on cal_eventstatus, then sync with https://demo.eccosolutions.co.uk/housing/api/rota/prePopulate
        // create a 'carer' in the system assigned specifically to the service, AND a hr job (admin -> staff, new job, link access)
        // login, 'move' a date, or use specific date: https://demo.eccosolutions.co.uk/housing/nav/r/care/2025-05-30
        // 'visit'
        // reports-definitions 'scheduler' report (TODO copy over)

        // scheduler menu item shows all rota, including care runs with some 'undefined' fields
        // assign a client to a worker directly, eg 'Cherry Tree House' on demo rota
        // goto the scheduler and find client on service
        // ** NB in the app, at least on demo sites, the uncompleted visits stack up for carers!
        // see

    }

}
