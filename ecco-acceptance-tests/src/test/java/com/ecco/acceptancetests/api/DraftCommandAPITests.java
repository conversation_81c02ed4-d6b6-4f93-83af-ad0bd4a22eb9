package com.ecco.acceptancetests.api;

import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.contacts.ContactCommandViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.viewModels.Result;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpServerErrorException;

import java.util.UUID;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class DraftCommandAPITests extends BaseJsonTest {

    @Test
    public void handlerPersistsDraftCommands() {
        var vm = new ContactCommandViewModel(1); // sysadmin
        vm.draft = true;
        var response = commandActor.executeCommand(vm);
        assertThat(response.getBody().getMessage()).isEqualTo("command draft applied");

        var result = contactActor.findContactCommand(vm.uuid.toString());
        assertThat(result).isNotNull();
    }

    @Test
    public void handlerIgnoresDraftCommands() {

        // GIVEN a contact
        int contactId = createContact();

        // THEN update a draft
        updateContact(contactId, true);

        // CHECK the contact hasn't changed
        var contact = contactActor.getContactById(contactId);
        assertThat(contact.getBody().mobileNumber).isEqualTo("07");

        // CHECK the handler hasn't triggered an event
        // but can't from API tests
    }

    @Test
    public void handlerClearsDraftCommandsUponSave() {

        // GIVEN a contact, and a draft
        int contactId = createContact();
        var draftUuid = updateContact(contactId, true);

        // WHEN update for real
        updateContact(contactId, false);

        // CHECK the contact has changed
        var contact = contactActor.getContactById(contactId);
        assertThat(contact.getBody().mobileNumber).isEqualTo("078");

        // CHECK the draft has gone
        // NB this causes the exception, preventing our assertThat
        // CHECK the draft has gone
        // NB this causes the exception, preventing our assertThat
        assertThrows(HttpServerErrorException.class, () -> {
            var result = contactActor.findContactCommand(draftUuid.toString());
            assertThat(result).isNull();
        });
    }

    @Test
    public void handlerClearsDraftCommandsUponNewDraft() {

        // GIVEN a contact, and a draft
        int contactId = createContact();
        var draftUuid = updateContact(contactId, true);

        // WHEN update a new draft
        updateContact(contactId, true, "0781");

        // CHECK the contact has not changed
        var contact = contactActor.getContactById(contactId);
        assertThat(contact.getBody().mobileNumber).isEqualTo("07");

        // CHECK the draft has gone
        // NB this causes the exception, preventing our assertThat
        assertThrows(HttpServerErrorException.class, () -> {
            var result = contactActor.findContactCommand(draftUuid.toString());
            assertThat(result).isNull();
        });
    }

    private UUID updateContact(int contactId, boolean draft) {
        return updateContact(contactId, draft, "078");
    }

    private UUID updateContact(int contactId, boolean draft, String mobile) {
        ResponseEntity<Result> response;
        var vm = new ContactCommandViewModel(contactId);
        vm.draft = draft;
        vm.mobileNumber = ChangeViewModel.create("07", mobile);
        response = commandActor.executeCommand(vm);

        // CHECK the response
        var text = draft ? "command draft applied" : "command applied";
        assertThat(response.getBody().getMessage()).isEqualTo(text);

        return vm.uuid;
    }

    private int createContact() {
        int contactId;
        var i = new IndividualViewModel();
        i.setFirstName("draft");
        i.setLastName("ignored");
        i.setMobileNumber("07");
        contactId = Integer.parseInt(contactActor.createIndividual(i).getBody().getId());
        return contactId;
    }

}
