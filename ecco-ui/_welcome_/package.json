{"name": "ecco-welcome-menu", "version": "0.1.0", "private": true, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "0.0.0", "@types/node": "^14.18.12", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "application-properties": "0.0.0", "ecco-components": "0.0.0", "ecco-dto": "0.0.0", "ecco-finance": "0.0.0", "ecco-incidents": "^0.0.0", "ecco-repairs": "^0.0.0", "ecco-offline": "0.0.0", "ecco-rota": "0.0.0", "ecco-spa-global": "0.0.0", "font-awesome": "^4.3.0", "jquery": "3.6.0", "jquery-migrate": "3.3.2", "prop-types": "^15.5.8", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0"}, "devDependencies": {"@jest/globals": "29.7.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "ecco-webpack-config": "0.0.0", "eslint": "^7.11.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "rimraf": "5.0.10", "typescript": "5.8.3"}, "scripts": {"analyze": "ecco-webpack --env visualize", "build": "echo 'not for production so no yarn build'", "clean": "<PERSON><PERSON><PERSON> build", "emit": "ecco-webpack", "fix": "tsc && eslint --fix src", "lint": "tsc && eslint src", "lint-strict": "tsc && eslint --max-warnings 0 src", "start": "ecco-webpack serve --env publicUrl=/ --open", "test": "echo \"Should be ecco-jest but no tests\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}