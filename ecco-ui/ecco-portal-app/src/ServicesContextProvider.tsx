import * as React from 'react';
import {FC} from 'react';

import {
    EccoAPI,
    nullAuditHistoryIntegrations,
    nullTaskIntegrations,
    ServicesContext,
    setGlobalEccoAPI,
    withSessionData
} from "ecco-components";
import {
    AddressedLocationAjaxRepository,
    AddressHistoryAjaxRepository,
    ApiClient,
    ChartAjaxRepository,
    ContactsAjaxRepository,
    ContractAjaxRepository,
    InvoicesAjaxRepository,
    SupportWorkAjaxRepository,
    TaskCommandAjaxRepository,
    UserAjaxRepository,
    ServiceRecipientAjaxRepository,
} from "ecco-dto";
import {RotaAjaxRepository} from "ecco-rota";
import {sessionDataFn} from "./services";
import {
    getBuildingRepository,
    getCalendarRepository,
    getClientRepository,
    getCommandQueueRepository,
    getEvidenceEffectsRepository,
    getFormEvidenceRepository,
    getReferralRepository,
    getRiskWorkRepository,
    getQuestionnaireSnapshotRepository,
    getSignatureRepository,
    getSupportWorkRepository,
    getWorkerRepository
} from "ecco-offline-data";
//import {SrFileLandingPageChooser, SupportHistoryWrapper} from "ecco-offline";
import {FinanceAjaxRepository} from "ecco-finance";
//import {taskIntegrations, auditHistoryIntegrations} from "ecco-offline";
import {ConfigurablePageComponentRegistry} from "ecco-components/EccoAPI";
import {IncidentAjaxRepository} from "ecco-incidents";
import {RepairAjaxRepository} from "ecco-repairs";

const defaultPageComponentRegistry = new ConfigurablePageComponentRegistry({
    "default": () => <>null</>, // graphs on overview cause issues
    //"finance": FinancePage
    "support-history": () => <>null</>
});

/** Shows a loading spinner while resolving sessionData then publishes access to all repositories
 * in EccoAPI via ServicesContext (for use via useServicesContext()) */
export const ServicesContextProvider: FC<{client: ApiClient}> = (props) => {
    const {client, children} = props;
    const supportWorkRepository = new SupportWorkAjaxRepository(client);
    const eccoAPI: EccoAPI = {
        baseURI: "TODO: it's for /offline|online/ in environment.ts",
        apiClient: client,
        pageComponentRegistry: defaultPageComponentRegistry,
        auditHistoryIntegrations: nullAuditHistoryIntegrations,
        taskIntegrations: nullTaskIntegrations,
        getAddressRepository: () => new AddressedLocationAjaxRepository(client),
        addressHistoryRepository: new AddressHistoryAjaxRepository(client),
        getBuildingRepository: getBuildingRepository,
        calendarRepository: getCalendarRepository(),
        chartRepository: new ChartAjaxRepository(client),
        clientRepository: getClientRepository(),
        getCommandRepository: getCommandQueueRepository,
        contactsRepository: new ContactsAjaxRepository(client),
        contractRepository: new ContractAjaxRepository(client),
        getEvidenceEffectsRepository,
        financeRepository: new FinanceAjaxRepository(client),
        formEvidenceRepository: getFormEvidenceRepository(),
        incidentsRepository: new IncidentAjaxRepository(client),
        repairsRepository: new RepairAjaxRepository(client),
        invoicesRepository: new InvoicesAjaxRepository(client),
        referralRepository: getReferralRepository,
        riskWorkRepository: getRiskWorkRepository(),
        rotaRepository: new RotaAjaxRepository(client, sessionDataFn),
        getSignatureRepository,
        supportWorkRepository: getSupportWorkRepository(),
        supportSmartStepsSnapshotRepository: supportWorkRepository,
        questionnaireSnapshotRepository: getQuestionnaireSnapshotRepository(),
        userRepository: new UserAjaxRepository(client),
        workersRepository: getWorkerRepository(),
        tasksRepository: new TaskCommandAjaxRepository(client),
        serviceRecipientRepository: new ServiceRecipientAjaxRepository(client),
        sessionData: null!
    };

    return withSessionData(sessionData => {
        eccoAPI.sessionData = sessionData;

        // NB because taskIntegrations is undefined above, we set it here
        //eccoAPI.taskIntegrations = taskIntegrations;
        //eccoAPI.auditHistoryIntegrations = auditHistoryIntegrations;

        setGlobalEccoAPI(eccoAPI);
        return <ServicesContext.Provider value={eccoAPI}>{children}</ServicesContext.Provider>;
    });
};
