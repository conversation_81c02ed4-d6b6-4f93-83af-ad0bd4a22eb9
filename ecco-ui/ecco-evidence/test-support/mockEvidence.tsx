import {CommandQueue} from "ecco-commands";
import {
    Action,
    ActionDto,
    ActionGroup,
    ActionGroupDto,
    ConfigResolverDefault,
    EvidenceDef,
    EvidenceGroup,
    EvidencePageType,
    FeatureSetDto,
    Messages,
    Outcome,
    OutcomeDto,
    Question,
    QuestionAnswerChoice,
    QuestionAnswerFree,
    QuestionAnswerSnapshotDto,
    QuestionGroup,
    ServiceCategorisation,
    ServiceDto,
    ServiceType,
    ServiceTypeDto,
    SessionData,
    SessionDataDto,
    SupportAction,
    TaskSettings
} from "ecco-dto";
import * as React from "react";
import {FC} from "react";
import {CommandForm, withCommandForm} from "ecco-components";
import {EccoV3EditPane} from "ecco-components-core";
import {Box, Paper} from "@eccosolutions/ecco-mui";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    QuestionGroupParametersDto,
    ServiceParametersDto,
    TaskDefinition,
    TaskDefinitionEntry as TaskDefinitionEntryDto
} from "ecco-dto/service-config-dto";
import {ActionInstanceControlDataSetup, ActionInstanceFeatures} from "../controlDataStructures";
import {EvidenceContext} from "../EvidenceContext";
import {SmartStepStatusTransitions} from "../smartsteps/SmartStepStatusTransitions";
import {EvidencePageData} from "../EvidencePageRoot";
import {SmartStepData, SmartStepInit, SmartStepState} from "../smartsteps/SmartStepRoot";
import {CommentEntryData, CommentEntryInit, CommentEntryState} from "../CommentEntryRoot";
import {EccoDate} from "@eccosolutions/ecco-common";

/**
 * CommandForm wrapper
 */
export const EmbeddedCommandForm: FC<{testCmdQueue: CommandQueue}> = props => {
    return (
        <CommandForm onCancel={() => {}} onFinished={() => {}} commandQueue={props.testCmdQueue}>
            {withCommandForm(form => (
                <Paper elevation={4}>
                    <Box m={2} p={2}>
                        <EccoV3EditPane
                            title="EccoV3EditPane"
                            saveEnabled={true}
                            show={true}
                            action="update"
                            onCancel={() => alert("cancelled")}
                            onSave={() => form.submitForm()}
                        >
                            {props.children}
                        </EccoV3EditPane>
                    </Box>
                </Paper>
            ))}
        </CommandForm>
    );
};

const freetext1: QuestionAnswerFree = {
    id: 1,
    // unused: minimum,maximum
    valueType: "textarea" // "textarea" | "text" | "number" | "checkbox" | "money" | "integer" | "date";
};
const freedate: QuestionAnswerFree = {
    id: 2,
    valueType: "date"
};
export const question1: Question = {
    id: 1,
    name: "question1",
    disabled: false,
    answerRequired: true,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [freetext1], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [], //QuestionAnswerChoice[]
    parameters: {}
};
const freetextMD: QuestionAnswerFree = {
    id: 2,
    // unused: minimum,maximum
    valueType: "markdown" // "textarea" | "text" | "number" | "checkbox" | "money" | "integer";
};
export const questionMD: Question = {
    id: 2,
    name: "questionMD",
    disabled: false,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [freetextMD], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [], //QuestionAnswerChoice[]
    parameters: {}
};
const freetextText: QuestionAnswerFree = {
    id: 3,
    // unused: minimum,maximum
    valueType: "text" // "textarea" | "text" | "number" | "checkbox" | "money" | "integer";
};
export const question2: Question = {
    id: 3,
    name: "questionText",
    disabled: false,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [freetextText], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [], //QuestionAnswerChoice[]
    parameters: {}
};
const choices1: QuestionAnswerChoice = {
    id: 1,
    // unused
    //displayImage: string;
    displayValue: "choice 1",
    value: "1",
    disabled: false
};
const choices2: QuestionAnswerChoice = {
    id: 2,
    // unused
    //displayImage: string;
    displayValue: "choice 2",
    value: "2",
    disabled: false
};
export const question3: Question = {
    id: 4,
    name: "questionChoices",
    disabled: false,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [choices1, choices2],
    parameters: {}
};
export const questionDisabled: Question = {
    id: 5,
    name: "questionDisabled",
    disabled: true,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [{id: 0, valueType: "textarea"}], //QuestionAnswerFree[]
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [],
    parameters: {}
};
export const questionDate: Question = {
    id: 6,
    name: "questionDate",
    disabled: false,
    answerRequired: false,
    orderby: 0,
    // NB we can only have one answer per question, so there is no need to define more than one freeType
    // nor free/choice combination
    /** the type of answer for this question */
    freeTypes: [freedate],
    /** a collection of answer choices allowed for this question, if choices  */
    choices: [],
    parameters: {}
};

export const questiongroup1: QuestionGroup = {
    id: 1,
    name: "questiongroup1",
    disabled: false,
    questions: [question1, questionMD, question2, question3, questionDisabled, questionDate],
    parameters: {} as QuestionGroupParametersDto
};

export const action1: ActionDto = {
    id: 1,
    //uuid?: string;
    name: "action1",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
    //statusChangeReasonListName?: string;
};
export const action2: ActionDto = {
    id: 2,
    name: "Clean bedroom",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
};
export const action3: ActionDto = {
    id: 3,
    name: "Go out",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
};
export const actiongroup1: ActionGroupDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "actiongroup1",
    orderby: 1,
    disabled: false,
    actions: [action1, action2, action3]
};
export const outcome1: OutcomeDto = {
    id: 1,
    uuid: Uuid.randomV4().toString(),
    name: "outcome1",
    disabled: false,
    actionGroups: [actiongroup1]
};

export const action4: ActionDto = {
    id: 4,
    name: "Some step",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
};
export const action5: ActionDto = {
    id: 5,
    name: "Another step",
    disabled: false,
    activityTypes: [],
    initialText: null,
    orderby: 1
};
export const actiongroup2: ActionGroupDto = {
    id: 2,
    uuid: Uuid.randomV4().toString(),
    name: "actiongroup2",
    orderby: 1,
    disabled: false,
    actions: [action4, action5]
};
export const outcome2: OutcomeDto = {
    id: 2,
    uuid: Uuid.randomV4().toString(),
    name: "outcome2",
    disabled: false,
    actionGroups: [actiongroup2]
};

export const dummyText = `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Id nibh tortor id aliquet. Sed lectus vestibulum mattis ullamcorper velit sed. Nec nam aliquam sem et tortor. Elit eget gravida cum sociis natoque. Ipsum a arcu cursus vitae. Massa sapien faucibus et molestie ac feugiat sed. Turpis egestas maecenas pharetra convallis posuere morbi leo. Duis at consectetur lorem donec massa sapien faucibus et molestie. Purus sit amet luctus venenatis lectus. Pellentesque nec nam aliquam sem et tortor consequat id porta. Sapien et ligula ullamcorper malesuada. Fringilla ut morbi tincidunt augue interdum velit euismod. Vitae turpis massa sed elementum tempus egestas sed. Netus et malesuada fames ac turpis egestas integer eget aliquet. Blandit cursus risus at ultrices mi tempus imperdiet nulla malesuada. Aliquam eleifend mi in nulla posuere sollicitudin aliquam ultrices sagittis.
Nisi est sit amet facilisis. Vitae congue eu consequat ac felis. Facilisis mauris sit amet massa vitae tortor condimentum lacinia. Nisl condimentum id venenatis a condimentum. Rutrum tellus pellentesque eu tincidunt tortor. Semper auctor neque vitae tempus quam. Pellentesque adipiscing commodo elit at imperdiet dui accumsan sit. Mi ipsum faucibus vitae aliquet nec ullamcorper sit. Enim lobortis scelerisque fermentum dui faucibus. Volutpat ac tincidunt vitae semper quis lectus nulla at.
Pellentesque id nibh tortor id aliquet lectus proin. Blandit cursus risus at ultrices mi. Iaculis eu non diam phasellus vestibulum lorem. Et odio pellentesque diam volutpat commodo. Sodales ut etiam sit amet nisl purus in. Adipiscing elit ut aliquam purus sit. Bibendum enim facilisis gravida neque convallis a cras. Duis at consectetur lorem donec massa sapien faucibus et molestie. Massa tincidunt nunc pulvinar sapien et ligula ullamcorper malesuada proin. Non odio euismod lacinia at quis risus sed vulputate odio. Vel turpis nunc eget lorem dolor sed viverra ipsum. A lacus vestibulum sed arcu.
Odio aenean sed adipiscing diam. Volutpat ac tincidunt vitae semper quis lectus. Tristique magna sit amet purus. Euismod in pellentesque massa placerat duis. Tellus molestie nunc non blandit massa enim nec dui nunc. Faucibus pulvinar elementum integer enim neque volutpat ac tincidunt vitae. Tempus quam pellentesque nec nam aliquam sem et tortor consequat. Ac felis donec et odio pellentesque diam volutpat commodo sed. Orci sagittis eu volutpat odio. Purus faucibus ornare suspendisse sed nisi lacus. Nulla facilisi etiam dignissim diam quis enim lobortis scelerisque. Habitant morbi tristique senectus et netus et. Porta non pulvinar neque laoreet suspendisse. Nunc scelerisque viverra mauris in aliquam sem fringilla ut morbi. Tellus integer feugiat scelerisque varius. Pulvinar pellentesque habitant morbi tristique senectus et netus et malesuada. Pharetra et ultrices neque ornare aenean euismod. Proin sagittis nisl rhoncus mattis rhoncus.
Nisi quis eleifend quam adipiscing vitae. Eget mauris pharetra et ultrices neque ornare aenean euismod elementum. Non arcu risus quis varius quam quisque id diam. Pharetra magna ac placerat vestibulum. Lobortis feugiat vivamus at augue eget arcu dictum varius duis. Sed id semper risus in hendrerit gravida. Ultrices eros in cursus turpis massa. Non tellus orci ac auctor augue mauris augue. Arcu vitae elementum curabitur vitae nunc. Convallis a cras semper auctor neque vitae tempus quam. Varius quam quisque id diam vel.`;

const service1: ServiceDto = {
    id: 1,
    name: "service 1",
    parameters: {} as ServiceParametersDto,
    serviceTypeId: 1,
    disabled: false
};


const serviceTypeDto: ServiceTypeDto = {} as ServiceTypeDto;
serviceTypeDto.id = 1;
serviceTypeDto.supportOutcomes = [outcome1, outcome2];
serviceTypeDto.riskAreas = [];
serviceTypeDto.questionGroups = [questiongroup1];
const taskSettings: TaskSettings = {};
taskSettings["questionGroupsById"] = "1";
taskSettings["outcomesById"] = "1";
taskSettings["commentLabel"] = "my comment";
taskSettings["tookPlaceOn"] = "dateTime";
taskSettings["showCommentComponents"] = "clientStatus,meetingStatus,type";
taskSettings["validateComment"] = "allowNullComment,type";
taskSettings["clientStatusListName"] = "clientStatus-list";
taskSettings["meetingStatusListName"] = "meetingStatus-list";
taskSettings["commentTypesById"] = "300,301";

const taskDefDtoGeneralQuestionnaire: TaskDefinitionEntryDto = {
    name: "generalQuestionnaire",
    orderby: 1,
    allowNext: true,
    dueDateSchedule: "",
    outcomeSettings: {},
    settings: {...taskSettings, questionnaireAsBlank: "y"}
};
const taskDefDtoNewMixedQuestionnaireEvidence: TaskDefinitionEntryDto = {
    name: "newMixedQuestionnaireEvidence",
    orderby: 1,
    allowNext: true,
    dueDateSchedule: "",
    outcomeSettings: {},
    settings: taskSettings
};
const taskDefDtoNewMixedSupportEvidence: TaskDefinitionEntryDto = {
    name: "newMixedSupportEvidence",
    orderby: 1,
    allowNext: true,
    dueDateSchedule: "",
    outcomeSettings: {},
    settings: taskSettings
};
const taskDefDtoNeedsEvidence: TaskDefinitionEntryDto = {
    name: "needsAssessment",
    orderby: 1,
    allowNext: true,
    dueDateSchedule: "",
    outcomeSettings: {},
    settings: taskSettings
};

serviceTypeDto.taskDefinitionEntries = [
    taskDefDtoGeneralQuestionnaire,
    taskDefDtoNewMixedQuestionnaireEvidence,
    taskDefDtoNewMixedSupportEvidence,
    taskDefDtoNeedsEvidence
];

function getOutcomeDto(id: number): OutcomeDto {
    return serviceTypeDto.supportOutcomes.find(outcome => outcome.id === id)! as OutcomeDto;
}

export const serviceType = new ServiceType(serviceTypeDto, {} as Messages, id => getOutcomeDto(id));

export const configResolver = () =>
    ConfigResolverDefault.fromLegacyServiceType(serviceType, sessionData);

const sessionDataDto: SessionDataDto = {} as SessionDataDto;
sessionDataDto.messages = {} as Messages;
sessionDataDto.featureSets = {global: {} as FeatureSetDto};
sessionDataDto.questionGroups = [questiongroup1];
sessionDataDto.softwareModulesEnabled = {};
sessionDataDto.softwareModulesEnabled["hact"] = {name: "hact", enabled: false};
sessionDataDto.serviceCategorisations = [
    {id: 1, serviceId: 1, disabled: false} as any as ServiceCategorisation
];
sessionDataDto.services = [service1];
sessionDataDto.listDefinitions = {};
const taskDefNeedsAssessment: TaskDefinition = {
    id: 1,
    type: "EVIDENCE_SUPPORT",
    name: "needsAssessment",
    description: "does a needs assessment",
    display: true,
    displayOverview: true,
    internal: false,
    metadata: {}
};
const taskDefNeedsAssessmentReduction: TaskDefinition = {
    id: 1,
    type: "EVIDENCE_SUPPORT",
    name: "needsAssessmentReduction",
    description: "does a needs assessment + reduction",
    display: true,
    displayOverview: true,
    internal: false,
    metadata: {}
};
const taskDefGeneralQuestionnaire: TaskDefinition = {
    id: 2,
    type: "EVIDENCE_QUESTIONNAIRE",
    name: "generalQuestionnaire",
    description: "does a questionnaire",
    display: true,
    displayOverview: true,
    internal: false,
    metadata: {}
};
const taskDefNewMixedQuestionnaireEvidence: TaskDefinition = {
    id: 3,
    type: "EVIDENCE_QUESTIONNAIRE",
    name: "newMixedQuestionnaireEvidence",
    description: "does a questionnaire with smart steps",
    display: true,
    displayOverview: true,
    internal: false,
    // see questionGroup1 above, 'question1' is id 1, question2 is id 3
    metadata: {
        questionsToActions: [
            [1, 1],
            [3, 2],
            [4, 3]
        ]
    }
};
const taskDefNewMixedSupportEvidence: TaskDefinition = {
    id: 3,
    type: "EVIDENCE_SUPPORT",
    name: "newMixedSupportEvidence",
    description: "does a support with questions",
    display: true,
    displayOverview: true,
    internal: false,
    // see questionGroup1 above, 'question1' is id 1, question2 is id 3
    metadata: {
        questionsTaskName: "generalQuestionnaire", // TODO COULD THIS BE newMixedEvidence??
        questionsToActions: [
            [1, 1],
            [3, 2],
            [4, 3]
        ]
    }
};
const taskDefNewMixedSupportOnlyQnsEvidence: TaskDefinition = {
    id: 3,
    type: "EVIDENCE_SUPPORT",
    name: "newMixedSupportOnlyQnsEvidence",
    description: "does a support with questions",
    display: true,
    displayOverview: true,
    internal: false,
    // see questionGroup1 above, 'question1' is id 1, question2 is id 3
    metadata: {
        questionsTaskName: "generalQuestionnaire", // TODO COULD THIS BE newMixedEvidence??
        questionsToActions: [
            [1, 0],
            [3, 0],
            [4, 0]
        ]
    }
};
sessionDataDto.taskDefinitions = [
    taskDefNeedsAssessment,
    taskDefNeedsAssessmentReduction,
    taskDefGeneralQuestionnaire,
    taskDefNewMixedQuestionnaireEvidence,
    taskDefNewMixedSupportEvidence,
    taskDefNewMixedSupportOnlyQnsEvidence
];
sessionDataDto.serviceTypesById = {};
sessionDataDto.serviceTypesById["1"] = serviceTypeDto;
sessionDataDto.supportOutcomes = [outcome1, outcome2];
sessionDataDto.listDefinitions["listName"] = [
    {
        id: 188,
        name: "success",
        listName: "listName",
        businessKey: "188",
        disabled: false,
        defaulted: false,
        order: null,
        metadata: {iconClasses: "fa-check-circle", value: "0", displayName: ""}
    }
];
sessionDataDto.listDefinitions["clientStatus-list"] = [
    {
        id: 200,
        name: "seen and okay",
        listName: "clientStatus-list",
        businessKey: "200",
        disabled: false,
        defaulted: false,
        order: null
    }
];
sessionDataDto.listDefinitions["meetingStatus-list"] = [
    {
        id: 210,
        name: "arrived",
        listName: "meetingStatus-list",
        businessKey: "210",
        disabled: false,
        defaulted: false,
        order: null
    }
];
sessionDataDto.listDefinitions["commenttypes"] = [
    {
        id: 300,
        name: "one-to-one",
        listName: "commenttypes",
        businessKey: "300",
        disabled: false,
        defaulted: false,
        order: null
    },
    {
        id: 301,
        name: "phone call",
        listName: "commenttypes",
        businessKey: "301",
        disabled: false,
        defaulted: false,
        order: null
    }
];
export const sessionData = new SessionData(sessionDataDto);

// clone from testData.ts, but with actionId 1 and 2 (and another 2)
var supportActions: SupportAction[] = [
    {
        id: 101812,
        actionInstanceUuid: Uuid.randomV4().toString(),
        name: "Support to maximise income e.g. ...",
        goalName: "pg",
        goalPlan: "",
        status: 3,
        statusChange: true,
        actionId: 1,
        actionGroupId: 83,
        outcomeId: 83,
        score: 8,
        statusChangeReasonId: null
    },
    {
        id: 101725,
        actionInstanceUuid: Uuid.randomV4().toString(),
        name: "Accept support and engage with keyworker...",
        goalName: "read jobs section in paper",
        goalPlan: "",
        status: 1,
        statusChange: true,
        actionId: 2,
        actionGroupId: 83,
        outcomeId: 83,
        score: null,
        statusChangeReasonId: null
    }
    //,{"id":101726,actionInstanceUuid: Uuid.randomV4().toString(), "name":"Something...","goalName":"read jobs section on internet","goalPlan":"","status":1,"statusChange":true,"actionId":2,"actionGroupId":83,"outcomeId":83,"score":null,"statusChangeReasonId":null}
] as SupportAction[];

// see testQuestionnaireData and createQuestionAnswerInits which uses configResolver, defined in testBaseData
// which points to sessionData config 'questiongroup1' which has "questions: [question1, questionMD, question2, question3, questionDisabled],"
var questionAnswers: QuestionAnswerSnapshotDto[] = [
    // used directly in EvidencePageQuestionnaire.test.tsx
    {
        id: 1500,
        questionId: 1,
        answer: "this is a textarea",
        workDate: undefined
    },
    {
        id: 1501,
        questionId: 3,
        answer: "this is text answer",
        workDate: undefined
    }
];

/**
 * Default init data.
 * NB We could create a SmartStepData (or use a real API) and use createSmartStepInit.
 * CLONE of ecco-test-app/testUtils
 */
export function testSmartStepInit(): SmartStepInit {
    const outcome = new Outcome(outcome1);
    const actionGroup = new ActionGroup(actiongroup1, outcome);
    const action = new Action(action1, actionGroup);

    const srId = 99;

    const evidenceDef = new EvidenceDef(
        "needsAssessment",
        EvidencePageType.assessment,
        EvidenceGroup.needs,
        undefined
    );

    const smartStepData: SmartStepData = {
        snapshot: null,
        actionDefId: action1.id,
        sessionData: sessionData,
        evidenceDef: evidenceDef,
        configResolver: configResolver(),
        getWorkUuid: () => Uuid.randomV4(),
        serviceRecipientId: srId,
        readOnly: false
    };

    const smartStepStateBase = ActionInstanceControlDataSetup.fromEmpty(false, null);
    const smartStepState: SmartStepState = {
        ...smartStepStateBase,
        actionDefId: action1.id,
        controlUuid: Uuid.randomV4(),
        related: false
    };

    return {
        initData: smartStepData,
        initState: smartStepState,
        context: {} as EvidenceContext,
        controlFeatures: {} as ActionInstanceFeatures,
        statusTransitions: new SmartStepStatusTransitions(
            evidenceDef.getEvidencePageType()!,
            false
        ),
        actionDef: action
    };
}

export function testCommentEntryInit(): CommentEntryInit {
    const srId = 99;

    const evidenceDef = new EvidenceDef(
        "needsAssessment",
        EvidencePageType.assessment,
        EvidenceGroup.needs,
        undefined
    );

    // TODO move from CommentEntryData below and use in <CommentType> itself
    let commentTypes = serviceType
        .getCommentTypesById(sessionData, evidenceDef.getTaskName())
        .map(ld => {
            return {
                id: ld!.getId(),
                name: ld!.getDisplayName(),
                disabled: ld!.getDisabled()
            };
        });

    const commentEntryData: CommentEntryData = {
        sessionData: sessionData,
        commentTypes: commentTypes,
        evidenceDef: evidenceDef, // refactor out
        configResolver: configResolver() // refactor out
    } as any as CommentEntryData;

    const commentEntryState: CommentEntryState = {
        comment: "my comment",
        workDate: EccoDate.todayLocalTime()
    } as any as CommentEntryState;
    //stateSetter={() => {}}

    return {
        initData: commentEntryData,
        initState: commentEntryState
    };
}

export function testQuestionnaireWithSmartStepData(): EvidencePageData {
    const data = testBaseData("newMixedQuestionnaireEvidence", EvidencePageType.questionnaireOnly);
    data.supportActions = supportActions;
    return data;
}

export function testSupportWithQuestionnaireData(): EvidencePageData {
    const data = testBaseData("newMixedSupportEvidence", EvidencePageType.assessment);
    data.supportActions = supportActions;
    data.questionAnswers = questionAnswers;
    return data;
}

export function testSupportWithOnlyQuestionnaireData(): EvidencePageData {
    const data = testBaseData("newMixedSupportOnlyQnsEvidence", EvidencePageType.assessment);
    data.supportActions = [];
    data.questionAnswers = [];
    return data;
}

export function testSmartStepData(): EvidencePageData {
    const data = testBaseData("needsAssessment", EvidencePageType.assessment);
    data.supportActions = supportActions;
    return data;
}

export function testQuestionnaireData(): EvidencePageData {
    const data = testBaseData("generalQuestionnaire", EvidencePageType.questionnaireOnly);
    data.comment = "initial...";
    data.questionAnswers = questionAnswers;
    return data;
}

function testBaseData(taskName: string, type: EvidencePageType): EvidencePageData {
    const evidenceDef = new EvidenceDef(
        taskName,
        type,
        EvidenceGroup.fromName(taskName),
        undefined
    );

    const srId = 99;

    const commentTypes = [
        {
            id: 1,
            name: "item 1",
            disabled: false
        },
        {
            id: 2,
            name: "item 2",
            disabled: false
        }
    ];

    return {
        sessionData: sessionData,
        serviceId: 1, // useful for Hact control
        taskName: taskName, // could be within evidenceDef
        evidenceDef: evidenceDef, // refactor out
        configResolver: configResolver(), // refactor out
        workUuid: Uuid.randomV4(),
        workUuid2: Uuid.randomV4(), // for our wip mixed smartstep+questionnaire page, we create entirely separate histories and so need a second workUuid
        serviceRecipientId: srId,
        readOnly: false,
        comment: undefined,
        commentTypeId: undefined,
        commentTypes: commentTypes,
        clientStatusId: undefined,
        meetingStatusId: undefined,
        workDate: undefined,
        supportActions: [],
        questionAnswers: []
    };
}
