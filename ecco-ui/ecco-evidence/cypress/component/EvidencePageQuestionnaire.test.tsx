import {mount} from "cypress/react";
import * as React from "react";
import {
    EvidencePage,
    EvidencePageLoaderForCommandForm,
    EvidencePageSetupForCommandForm
} from "../../EvidencePage";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {sessionData, testQuestionnaireData} from "../../test-support/mockEvidence";
import {CommandForm, EccoAPI} from "ecco-components";
import {Command} from "ecco-commands";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";
import {EccoDate} from "@eccosolutions/ecco-common";
import {getFailAllMethodsMock} from "ecco-components/test-support/mock-utils";
import {
    ServiceRecipient,
    ServiceRecipientAjaxRepository,
    ServiceRecipientRepository
} from "ecco-dto";

const serviceRecipientRepository = getFailAllMethodsMock<ServiceRecipientRepository>(
    ServiceRecipientAjaxRepository
);
serviceRecipientRepository.findOneServiceRecipientById = (srId: number) => {
    const d = {
        serviceRecipientId: 99,
        serviceAllocationId: 1,
        contactId: 99,
        contactName: "Me - 99",
        calendarId: "my-cal-id"
    } as any as ServiceRecipient;
    return Promise.resolve(d);
};
serviceRecipientRepository.findServiceRecipientDraftCommands = (srId: number) => {
    return Promise.resolve([]);
};

const overrides = {
    serviceRecipientRepository: serviceRecipientRepository,
    sessionData: sessionData
} as EccoAPI;


describe("EvidencePage Questionnaire tests", () => {

    it("evidence page loader questionnaire", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {/* see EvidencePageTest.tsx */}
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <EvidencePageLoaderForCommandForm
                            srId={99}
                            taskName={"generalQuestionnaire"}
                        >
                            <EvidencePage />
                            <CommandFormTestOutput cmdEmitted={cmdEmitted || []} />
                        </EvidencePageLoaderForCommandForm>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        cy.contains("questionMD");
    });

    it("evidence page questionnaire", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest>
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <>
                            {/* EvidencePageRoot - directly, when no commandForm required */}
                            {/* EvidencePageLoaderForCommandForm - when loading data */}
                            <EvidencePageSetupForCommandForm initData={testQuestionnaireData()}>
                                <EvidencePage />
                                <CommandFormTestOutput cmdEmitted={cmdEmitted} />
                                {/* cmdEmitted here, and could show errors etc...*/}
                            </EvidencePageSetupForCommandForm>
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
        cy.contains("questionMD");
        cy.contains("questionDisabled").should("not.exist");
        cy.contains("questionDate");
        // see also EvidencePageSmartStep.test.tsx
        // select a questionDate of today - gathered from cypress ui
        cy.get(".MuiIconButton-label > .MuiSvgIcon-root").click();
        cy.contains("OK").click();

        // REMOVE init answer to reveal required field
        cy.get('[name*="answer"]').eq(0).clear();

        // FILL IN required fields
        cy.get('[name*="evidence-comment"]').type("comment...");

        cy.get('[name*="answer"]').eq(0).type("this is a required textarea");

        cy.contains("took place on").siblings("div.MuiInput-formControl").find("input").click();
        cy.contains("OK").click();
        cy.get('[name*="commentTypeId"]').parent().click().get("li").eq(1).click();
        // SUBMIT
        cy.findByRole("button", {name: "submit"}).click();

        // TEST
        const today = EccoDate.todayLocalTime().formatIso8601();
        cy.contains(`"comment":{"from":"initial...","to":"initial...comment..."}`);
        cy.contains(`"answerChange":{"from":null,"to":"${today}"}`);
        cy.contains(`"answerChange":{"from":null,"to":"this is a required textarea"}`);
    });
});
