import {mount} from "cypress/react";
import * as React from "react";
import {sessionData, testSmartStepInit} from "../../test-support/mockEvidence";
import {EccoAPI} from "ecco-components/EccoAPI";
import {FC, useState} from "react";
import {Paper} from "@eccosolutions/ecco-mui";
import {SmartStepInit, SmartStepState} from "../../smartsteps/SmartStepRoot";
import {SmartStep} from "../../smartsteps/SmartStep";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";

const SmartStepTestLayout: FC = () => {
    // create/hold the props
    const init: SmartStepInit = testSmartStepInit();
    const [state, setState] = useState<SmartStepState>(init.initState);
    const stateSetter = (update: Partial<SmartStepState>) => {
        setState({...state, ...update});
    };

    return (
        <>
            <SmartStep init={init} state={state} stateSetter={stateSetter} />
            <Paper elevation={2}>
                <p>state: {JSON.stringify(state)}</p>
            </Paper>
        </>
    );
};

const overrides = {
    sessionData: sessionData
} as EccoAPI;

describe("SmartStep tests", () => {
    it("smartstep", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <SmartStepTestLayout />
            </TestServicesContextProvider>
        );
        cy.findByLabelText("target").type("16/05/2022");
        cy.contains('"targetDate":"2022-05-16"');
    });
});
