import {EccoDate} from "@eccosolutions/ecco-common";
import {PrefixType} from "ecco-dto/service-recipient-dto";
import {
    <PERSON>oleanChange,
    CommandDto,
    RepairDto,
    NumberChangeOptional,
    StringChangeOptional
} from "ecco-dto";
import {CommandDtoServer} from "ecco-dto/evidence-dto";
import {
    BaseServiceRecipientTaskUpdateCommand,
    CreateServiceRecipientCommand
} from "../referral/commands";
import {ServiceRecipientTaskBaseCommandDto} from "ecco-dto/evidence/evidence-command-dto";

export interface CreateRepairCommandDto extends CommandDto, CommandDtoServer {
    prefix: PrefixType;
    repairViewModel: RepairDto;
}

export class CreateRepairCommand extends CreateServiceRecipientCommand {
    private static prefix: PrefixType = "m";
    public static discriminator = "createSvcRec"; // as per CreateServiceRecipientCommand

    constructor(protected repair: RepairDto) {
        super(
            CreateRepairCommand.prefix,
            `service-recipient/command/create/${CreateRepairCommand.prefix}/`
        );
    }

    public toDto(): CreateRepairCommandDto {
        return {
            commandName: CreateRepairCommand.discriminator,
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            prefix: this.prefix,
            repairViewModel: this.repair
        };
    }
}

// used to create the above RepairDetailCommandViewModel
export class RepairDetailCommand extends BaseServiceRecipientTaskUpdateCommand {
    receivedDateChange: StringChangeOptional;
    categoryIdChange: NumberChangeOptional;
    rateIdChange: NumberChangeOptional;
    priorityIdChange: NumberChangeOptional;

    constructor(serviceRecipientId: number, taskHandle: string) {
        super("update", serviceRecipientId, "repairDetails", taskHandle);
    }

    public changeReceivedDate(from: EccoDate | null, to: EccoDate | null) {
        this.receivedDateChange = this.asStringChange(
            from && from.formatIso8601(),
            to && to.formatIso8601()
        );
        return this;
    }

    public changePriorityId(from: number, to: number) {
        this.priorityIdChange = this.asNumberChange(from, to);
        return this;
    }

    public changeCategoryId(from: number, to: number) {
        this.categoryIdChange = this.asNumberChange(from, to);
        return this;
    }

    public changeRateId(from: number, to: number) {
        this.rateIdChange = this.asNumberChange(from, to);
        return this;
    }

    public override hasChanges() {
        return (
            super.hasChanges() ||
            this.receivedDateChange != null ||
            this.categoryIdChange != null ||
            this.rateIdChange != null ||
            this.priorityIdChange != null
        );
    }

    public toDto(): RepairDetailCommandDto {
        return {
            uuid: this.getUuid().toString(),
            commandUri: this.commandUri,
            timestamp: this.timestamp,
            serviceRecipientId: this.serviceRecipientId,
            taskName: this.taskName,
            taskHandle: this.taskHandle,
            userComment: this.userComment,
            receivedDate: this.receivedDateChange,
            categoryId: this.categoryIdChange,
            rateId: this.rateIdChange,
            priorityId: this.priorityIdChange
        };
    }
}
export interface RepairDetailCommandDto extends ServiceRecipientTaskBaseCommandDto {
    receivedDate: StringChangeOptional;
    categoryId: NumberChangeOptional;
    rateId: NumberChangeOptional;
    priorityId: NumberChangeOptional;
}
