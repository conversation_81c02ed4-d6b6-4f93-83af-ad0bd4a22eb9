import {HateoasResource, StringToObjectMap, StringToStringMap} from "@eccosolutions/ecco-common";
import {Agency, Individual} from "./contact-dto";
import {EventResourceDto} from "./calendar-dto";
import {
    ServiceRecipient,
    ServiceRecipientPlainFields,
    ServiceRecipientSecretFields,
    ServiceRecipientStatusFields
} from "./service-recipient-dto";


export type AcceptedState = "ACCEPTED" | "SIGNPOSTED" | "UNSET";


export interface ReferralStatusFields extends ServiceRecipientStatusFields {
    /**
     * The date at which the service user will start, or has started receiving service agreed for
     * this referral, or null (see receivingService and referralStartServiceFlow.jsp).
     * NB This is ONLY set if a referral aspect 'start' or 'auto start' is configured
     * which is likely to be many, but not all sites. Until then we also use
     * decisionMadeOn.
     */
    receivingServiceDate?: string | undefined;

    /**
     * The date at which the service user exited (after starting).
     * NB Introduce a 'closedDate' if we want a single exited/signposted date.
     */
    exitedDate?: string | undefined;

    /**
     * The date or date time at which the service user was accepted or signposted on the service (automatically set but can be changed)
     * This may not reflect the date the service user actually started, but is the date of the decision. See receivingServiceDate and signpostedCommentId.
     * It is editable via ReferralTaskAcceptOnServiceCommandHandler - see acceptedDate.
     * NB all sources should be consistent by now - they are providing this as the a users local date.
     * NB Introduce a 'closedDate' if we want a single exited/signposted date.
     */
    decisionMadeOn?: string | undefined;
}

/** Matches ReferralListRowResource generated in ReferralsListController.list */
export interface ReferralsListRow extends HateoasResource, ReferralStatusFields {
    /** service recipient id - for evidence popup dialog */
    serviceRecipientId: number;

    /** referral id - for client file open in tab */
    referralId: number;

    /** An alternative id for the referral */
    referralCode: string;

    /** The name of the service user who is the subject of this referral. */
    clientDisplayName: string;

    /** The contact id of the worker assigned on the file */
    supportWorkerId: number;

    /** The name of the worker assigned on the file */
    supportWorkerDisplayName: string;

    /** The contact id of the interviewer on the file */
    interviewer1ContactId: number;

    // populated client side
    interviewer1DisplayName: string;
    address: string;
    // populated client side

    /** The date this referral was received by the service provider, or null
     * if that date has not been recorded in Ecco. */
    receivedDate?: string | undefined;

    serviceAllocationId: number;

    /** The source of the referral; either the name of the agency that
     * initiated the referral, or "self referral". */
    source: string;

    selfReferral: boolean;
    agency: string;
    //    referrerIndividualId: number;

    /** ISO-8601 local-date */
    nextDueSlaDate: string;

    /** Task id (aka referralaspectId) */
    nextDueTaskId: number;
}

export interface SourceSummaryFields {
    selfReferral?: boolean | undefined;
    /** The source of the referral; either the name of the agency that
     * initiated the referral, or "self referral".
     * See also referrerAgencyId and referrerIndividualId.
     */
    source: string;

    referrerAgencyId?: number | undefined;

    referrerIndividualId?: number | undefined;

    // CLIENT-SIDE POPULATED
    // The referrer source agency, if there is one
    sourceAgency?: Agency | undefined;
    // NOTE: NOT POPULATED on the server to avoid loading the world
    // However, reports do populate some of this by loading the information client-side based on the id's
    sourceProfessional?: Individual | undefined;
    // CLIENT-SIDE POPULATED
}

export interface ConsentSummaryFields {
    /** The instant by which the referral had consent to disclose signed (if any) */
    consentAgreementDate?: string | undefined;
    consentAgreementStatus?: boolean | undefined;
    /** The uuid of the signature, if there has been a signature on the consent to disclose */
    consentSignedId?: string | undefined;

    /** The instant by which the referral had a data protection agreed (if any) */
    dataProtectionAgreementDate?: string | undefined;
    /** The uuid of the signature, if there has been one */
    dataProtectionSignedId?: string | undefined;
    dataProtectionAgreementStatus?: boolean | undefined;

    agreement1AgreementDate?: string | undefined;
    agreement1SignedId?: string | undefined;
    agreement1AgreementStatus?: boolean | undefined;
    agreement2AgreementDate?: string | undefined;
    agreement2SignedId?: string | undefined;
    agreement2AgreementStatus?: boolean | undefined;
    agreement3AgreementDate?: string | undefined;
    agreement3SignedId?: string | undefined;
    agreement3AgreementStatus?: boolean | undefined;
    agreement4AgreementDate?: string | undefined;
    agreement4SignedId?: string | undefined;
    agreement4AgreementStatus?: boolean | undefined;
    agreement5AgreementDate?: string | undefined;
    agreement5SignedId?: string | undefined;
    agreement5AgreementStatus?: boolean | undefined;
    agreement6AgreementDate?: string | undefined;
    agreement6SignedId?: string | undefined;
    agreement6AgreementStatus?: boolean | undefined;
    agreement7AgreementDate?: string | undefined;
    agreement7SignedId?: string | undefined;
    agreement7AgreementStatus?: boolean | undefined;
    agreement8AgreementDate?: string | undefined;
    agreement8SignedId?: string | undefined;
    agreement8AgreementStatus?: boolean | undefined;
    agreement9AgreementDate?: string | undefined;
    agreement9SignedId?: string | undefined;
    agreement9AgreementStatus?: boolean | undefined;
    agreement10AgreementDate?: string | undefined;
    agreement10SignedId?: string | undefined;
    agreement10AgreementStatus?: boolean | undefined;
}

export interface SignpostedFields extends SignpostedIdFields {
    signpostedComment?: string | undefined;

    /**
     * The signposted reason when the client was never accepted on the service.
     */
    signpostedReason?: string | undefined;
    /**
     * The signposted agency when the referral is signposted
     */
    signpostedAgencyName?: string | undefined;
}

export interface SignpostedIdFields {
    /**
     * The id of the signposted comment of the referral.
     * Non-null means that the referral has been signposted and we use this
     * in combination with decisionMadeOn to determine whether the decision
     * was successful or not. See ReferralStatusCommonPredicates.java closed().
     */
    signpostedCommentId?: number | undefined;
    signpostedReasonId?: number | undefined;
    signpostedAgencyId?: number | undefined;

    /** The referral is signposted back to the referring agency */
    signpostedBack: boolean;
}

export interface FundingSummaryFields {
    /**
     * The ID number of the {@link FundingSource} who funded this referral
     */
    fundingSourceId?: number | undefined;

    /** The local date by which the funding decision was made */
    fundingDecisionDate?: string | undefined;

    /** Hours of support available */
    fundingHoursOfSupport?: number | undefined;

    /** Whether or not the funding request was accepted */
    fundingAccepted: boolean;
}

export interface InterviewSetupSummaryFields {
    /**
     * The date or date time at which the service user was first contacted (automatically set
     * on saving 'setup interview', but can be changed).
     * NB a date when using with ReferralSummaryViewModel, datetime when direct with ReferralSummary.
     */
    firstResponseMadeOn?: string | undefined;

    /**
     * Comments about the set-up of the interview.
     */
    interviewSetupComments?: string | undefined;

    /**
     * The number of times which the interviewee has not attended the interview.
     */
    interviewDna: number;

    /**
     * Comments on an interviewee's non attendance of an interview.
     */
    interviewDnaComments?: string | undefined;

    interviewer1ContactId?: number | undefined;

    //Values for assessment date
    /**
     * The ID of the second interviewer
     */
    interviewer2ContactId?: number | undefined;

    /**
     * The location at which the interview takes place.
     */
    interviewLocation?: string | undefined;

    /**
     * The date and time of the first interview offered.
     */
    firstOfferedInterviewDate?: string | undefined;
}

export interface ExitedSummaryFields {
    /** The date this referral was closed/exited, or null
     * if that date has not been recorded in ECCO.
     * NB This is not the same as a signposted date,
     * an exit date is after a client has been accepted.
     */
    exitedDate?: string | undefined;
    /**
     * The reason the client exited.
     * This is not the same as a signpost reason - see exitedDate.
     */
    exitReasonId?: number | undefined;
    exitReason?: string | undefined;
}

export interface ReferralSummarySecretFields
    extends SourceSummaryFields,
        ConsentSummaryFields,
        SignpostedIdFields,
        FundingSummaryFields,
        InterviewSetupSummaryFields,
        ExitedSummaryFields,
        ReferralStatusFields {
    /** The first name of the service user who is the subject of this referral. */
    firstName: string;

    /** The last name of the service user who is the subject of this referral. */
    lastName: string;

    /** The name of the service user who is the subject of this referral. */
    // NB deprecated - use displayName instead as part of the ServiceRecipient
    clientDisplayName: string;

    /** The contact ID of the client who is the subject of this ReferralSummary.
     * This also exists in ReferralPlainFields, and ContactPlainFields as optional.
     */
    contactId: number;

    /** True if the referral has been requested for deletion. */
    requestedDelete: boolean;

    /**
     * Indicates whether the user has access to this referral.
     * Also see ReferralSecretFields.
     * TODO This is ONLY ACCURATE for the methods which populate it!
     */
    _readOnly?: boolean | undefined;

    /**
     * Within family support, referrals can be created for family members which 'hang' off a primary referral
     * so the primaryReferralId represents the referral which this hangs off.
     */
    primaryReferralId?: number | undefined;
    /**
     * The relationship to the primary referral - see relationshipToPrimaryReferralId
     * A list def - search relationshipsListById.
     */
    primaryRelationshipId?: number | undefined;

    /** The id of the allocated ServiceCategorisation */
    serviceAllocationId: number;

    supportWorkerId?: number | undefined;

    /** An alternative id for the referral */
    referralCode: string;

    /** The user visible client id, which differs from clientId for some organisations such as when we've imported */
    clientCode: string;

    /**
     * The ID of the pending status of the referral.
     */
    pendingStatusId?: number | undefined;

    /** The date this referral was received by the service provider, or null
     * if that date has not been recorded in Ecco. */
    receivedDate?: string | undefined;

    referralReason?: string | undefined;

    latestClientStatusId?: number | undefined;
    latestClientStatusDateTime?: string | undefined;

    /**
     * The date and time of the interview.
     */
    decisionDate?: string | undefined;

    /** Users local date on which it was decided that the referral is appropriate for referral to this service */
    decisionReferralMadeOn?: string | undefined;

    /** See ReferralToViewModel for logic */
    appropriateReferralState: AcceptedState;

    acceptOnServiceState: AcceptedState;

    /**
     * The item selected e.g. the id of the ward for a single ward within a district.
     * Refers to Referral.srcGeographicArea
     */
    srcGeographicAreaId?: number | undefined;

    signpostedReason?: string | undefined;

    /** the main/assigned support worker */
    supportWorkerDisplayName?: string | undefined;

    waitingListScore?: number | undefined;
}

/** This interface must match the Java class ReferralViewModel
 * TODO: Extract superclass at server end so that we match ReferralSummaryViewModel. */
export interface ReferralSummaryDto extends ServiceRecipient, ReferralSummarySecretFields {
    /** The referral ID. - optional because we don't know it when creating a new referral */
    referralId?: number | undefined;

    /** The system's client ID of the client who is the subject of this referral. */
    clientId: number;

    /**
     * Bit 0 = Sun -> Bit 6 = Sat - See DAYS_AS_BITS
     * So having Mon+Thurs would be 2+16
     */
    daysAttending: number;

    /**
     * NOTE: NOT POPULATED ON ReferralSummary to avoid loading the world
     * However, reports do populate some of this by loading the information based on the id's
     */
    //currentRegionName?: string | undefined;
    interviewer1DisplayName?: string | undefined;
}

/** see ReferralRelationshipDto.java */
export interface RelatedRelationship {
    primaryReferralId: number; // the originating referralId which has this relationship
    referralId: number;
    clientId: number;
    clientDisplayName: string;
    birthDate: string;
    /** e.g. father, son, etc. This is null if it is the primary referral */
    relationship: string;
}

export interface ServiceRecipientAssociatedContact {
    serviceRecipientId: number;
    contactId: number; // avoid us looking in individual/agency for the contactId
    contact?: Individual | undefined;
    organisation?: Agency | undefined;
    created: string; // UTC datetime
    archived: string;
    associatedTypeIds: number[];
    // populated client side for reporting
    referralSummary?: ReferralSummaryDto | undefined;
    // populated client side for reporting
}


export interface ReferralPlainFields extends ServiceRecipientPlainFields {
    /** The referral ID. */
    referralId: number;

    /** The client ID of the client who is the subject of this referral. */
    clientId: number;

}

export interface ReferralSecretFields
    extends SignpostedFields,
        ReferralSummarySecretFields,
        ServiceRecipientSecretFields {
    /** list of contacts associated with this referral */
    contacts?: Individual[] | undefined;

    /** The name of the agency that delivers the referred service, or null if
     * the service is delivered in-house by the service provider. */
    delivererAgencyName?: string | undefined;

    /** Contact id of agency delivering the service */
    deliveredById?: number | undefined;

    /** ISO8601 local date for when service delivery will start */
    deliveredByStartDate?: string | undefined;

    /** The SVG XML of the signature */
    dataProtectionSignatureSvgXml?: string | undefined;

    /** the interviewer */
    interviewer1WorkerDisplayName: string;

    // NB in webflow days, this did:
    //    <action-state id="autoStart">
    //         <!-- we can use this date to distinguish between when they became fully-fledged volunteers -->
    //         <set name="referral.receivingServiceDate" value="referral.decisionMadeOn"/>
    //         <set name="referral.receivingService" value="true"><attribute name="name" value="last"/></set>
    //         <transition on="last.success" to="save_endFlow"/>
    //     </action-state>
    callAcceptOnService?: boolean | undefined; // allows the wizard or import to trigger acceptOnService (eg for 'autoStart' task)

    /** the main/assigned support worker */
    supportWorkerDisplayName: string;

    /** Nearby calendar events for the client of this referral */
    calendarEvents: EventResourceDto[];

    /**
     * Bit 0 = Sun -> Bit 6 = Sat - See DAYS_AS_BITS
     * So having Mon+Thurs would be 2+16
     */
    daysAttending: number;

    exitComment?: string | undefined;

    /**
     * This referrals parent id, a master referral, if any. This relates to central processing.
     * NB that family support uses 'primary' nomenclature
     */
    parentReferralId?: number | undefined;

    /** Optionally populated based on report optional data contains "childRecipientIds" */
    childServiceRecipientIds?: number[] | undefined;

    /**
     * One child referral is allowed to be the "primary service" for that user.
     * NB this is a NOT an integral part of central processing - think instead 'isPriorityChildReferral' (parent / child)
     * Its usage is on the 'services' tab - where isPrimaryChildReferral indicates its the 'primary child referral'
     * and shows it with a green line
     * The data is currently stored in the Referral customObjectMap - see Referral.setIsPrimaryChildReferral
     */
    isPrimaryChildReferral?: boolean | undefined;

    /** Items selected using list: optional field and contained in SessionData.getListDefinitionEntries() */
    choicesMap?: StringToObjectMap<{id: number; name: string}> | undefined;

    textMap?: StringToStringMap | undefined;

    dateMap?: StringToStringMap | undefined;

    /**
     * The name of the {@link FundingSource} who funded this referral
     */
    fundingSource?: string | undefined; // TODO both this and Java com.ecco.webApi.viewModels.ReferralViewModel#fundingSource should be renamed fundingSourceName for the sake of clarity.

    /** The reference for the funding */
    fundingPaymentRef?: string | undefined;

    fundingAmount?: number | undefined;

    /** The date (locadate) on which the decision about funding was made */
    fundingReviewDate?: string | undefined;
}

/** A referral.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.ReferralViewModel. */
export type ReferralDto = ReferralPlainFields & ReferralSecretFields;