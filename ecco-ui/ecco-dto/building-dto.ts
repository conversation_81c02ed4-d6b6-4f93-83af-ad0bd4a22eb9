import {HateoasResource, StringToObjectMap, StringToStringMap} from "@eccosolutions/ecco-common";
import {Address} from "./contact-dto";

export const BLDG_SYMBOL = "@";

/** FixedContainerViewModel */
export interface Building extends HateoasResource {
    buildingId: number;
    disabled: boolean;
    /** Parent building id */
    parentId?: number | undefined;
    /** So we can easily say where a shift is for example */
    parentName?: string | undefined;
    serviceRecipientId: number;
    serviceAllocationId: number;
    name: string | null;
    externalRef: string;
    resourceTypeId: number | undefined;
    resourceTypeName: string;
    calendarId: string;
    address?: Address | undefined;
    locationId: number;

    choicesMap?: StringToObjectMap<{id: number; name: string}> | undefined;
    textMap?: StringToStringMap | undefined;
    dateMap?: StringToStringMap | undefined;
}
