export type PrefixType = "r" | "b" | "w" | "i" | "ct" | "gs" | "m";

// sits with ServiceRecipient but is not connected to it
// because the status is currently set from the parent (eg referral etc)
// so this is only joined to the parents
export interface ServiceRecipientStatusFields {

    /** key to look up in messages[] for the status */
    statusMessageKey:
            | "status.toStart"
            | "status.started"
            | "status.forAssessment"
            | "status.signposted"
            | "status.incomplete"
            | "status.exited";
}

export interface ServiceRecipientPlainFields {
    /** Indicate the type of service recipient (r / b / w / ct / gs / m) - see BaseServiceRecipient.getPrefix */
    prefix: PrefixType;

    /** referralId, workerJobId or buildingId. User prefix to work out which */
    parentId?: number | undefined;

    /** The service recipient ID (unique across bldg, hr, referral */
    serviceRecipientId: number;

    /** The contactId for the displayName */
    contactId: number;

    calendarId: string;

    /** The id of the allocated ServiceCategorisation */
    serviceAllocationId: number;

    /** The id of the config */
    serviceTypeId: number;
}

export interface ServiceRecipientSecretFields {
    /** Name to describe this entity, such as the clientDisplayName for a Referral */
    displayName: string;

    /** Converted server-side from parentReferralId - see ReferralRepositoryImpl.populateParentServiceRecipientId */
    parentServiceRecipientId?: number | undefined;
}

// matches ServiceRecipientSummary.java
export interface ServiceRecipient
    extends ServiceRecipientPlainFields,
        ServiceRecipientSecretFields {}
