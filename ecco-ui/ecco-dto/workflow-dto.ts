import {HateoasResource} from "@eccosolutions/ecco-common";

export interface WorkflowPlainFields {
    serviceRecipientId: number;
}

export interface WorkflowSecretFields {
    /** The individual tasks that constitute this workflow. */
    tasks: TaskDto[];
}

/** Data-transfer object representing a workflow.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.WorkflowViewModel. */
export interface WorkflowDto extends WorkflowPlainFields, WorkflowSecretFields {
}

export interface TaskWithTitle extends TaskDto {
    /** Title as looked up via `sessionData.messages` etc - perhaps towards domain object of a task */
    title: string;
}

export interface LinearTaskHandle {
    serviceRecipientId: number;
    taskDefId: number;
    taskInstanceUuid?: string | undefined;
}

/** Data-transfer object representing a task within a workflow.
 * This is different to TaskStatus in evidence-dto.
 * This represents the common properties across linear/activiti.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.WorkflowTaskViewModel. */
export interface TaskDto extends HateoasResource {
    /** The username of the assignee of the task */
    assignedTo: string;

    /** The task is available to be actioned. */
    isAvailable: boolean;

    /** The task has been actioned. */
    isCompleted: boolean;

    /** Handle used to query for the WorkflowTaskDefinition containing settings for this task. */
    taskDefinitionHandle: string;

    /** The name used for display. */
    taskName: string;

    /** The opaque handle of the task */
    taskHandle: string;

    /** Time when the task is due - ISO8601 local-datetime */
    dueDate: string | null;

    /** Time when the task was completed or cancelled - ISO8601 local-datetime */
    endTime: string | null;
}

export interface TaskDefinitionPlainFields {
}

export interface TaskDefinitionSecretFields {
    /** The handle that uniquely identifies this task definition. */
    handle: string;

    /** The Task Type identifier.
     *
     * This is used to look up UI controls used to represent or modify the task,
     * and to look up a human-readable string to describe the task. */
    taskType: string;

    /** Form Settings used to configure UI controls. */
    formSettings: FormSettingsDto;
}

/** Data-transfer object representing a Workflow Task Definition.
 *
 * A Task Definition defines static data that is shared by similar Task instances.
 *
 * This interface must match the Java interface com.ecco.workflow.WorkflowTaskDefinition. */
export interface TaskDefinitionDto extends TaskDefinitionPlainFields, TaskDefinitionSecretFields {
}

/** Data-transfer object representing Form Settings used to configure Workflow
 * UI controls. */
export interface FormSettingsDto {
    [key: string]: string;
}

export interface WorkflowOperations {
    getTasks(): TaskDto[];

    claimTask(task: TaskDto): Promise<void>;

    ensureUnplannedWorkflowTask(task: TaskDto): Promise<void>;

    markComplete(task: TaskDto): void;
}