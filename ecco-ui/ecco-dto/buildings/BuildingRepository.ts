import {Building} from "../building-dto";
import {NumberToObjectMap} from "@eccosolutions/ecco-common";

export interface BuildingRepository {
    findOneBuilding(buildingId: number): Promise<Building>;

    findOneBuildingBySrId(srId: number): Promise<Building>;

    findAllBuildingsInIds(ids: number[]): Promise<Building[]>;

    findAllBuildingCareRuns(buildingId: number): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildings(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
              }
            | undefined
    ): Promise<Building[]>;

    /**
     * @param query.resourceType defaults to BUILDING resource type when omitted
     * @param query.showChildren defaults to true.  Include rooms/runs etc at this location
     */
    findAllBuildingsForUser(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
              }
            | undefined
    ): Promise<Building[]>;

    findAllBuildingsOfLocationId(addressLocationId: number): Promise<Building[]>;

    getCachedBuildingsMap(refresh?: boolean | undefined): Promise<NumberToObjectMap<Building>>;
}
