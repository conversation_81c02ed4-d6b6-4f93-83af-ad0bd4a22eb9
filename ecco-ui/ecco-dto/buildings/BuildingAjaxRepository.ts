import {NumberToObjectMap} from "@eccosolutions/ecco-common";
import {ApiClient} from "../web-api";
import {Building} from "../building-dto";
import {BuildingRepository} from "./BuildingRepository";

export class BuildingAjaxRepository implements BuildingRepository {
    private allBuildings: Promise<NumberToObjectMap<Building>> | null = null;

    constructor(private apiClient: ApiClient) {}

    public findOneBuilding(buildingId: number): Promise<Building> {
        return this.apiClient.get<Building>(`buildings/${buildingId}/`);
    }

    public findOneBuildingBySrId(srId: number): Promise<Building> {
        return this.apiClient.get<Building>(`buildings/byServiceRecipient/${srId}/`);
    }

    public findAllBuildingsInIds(buildingIds: number[]): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/byIds/", {
            query: {
                ids: buildingIds.join(",")
            }
        });
    }

    public findAllBuildingsOf(buildingId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/${buildingId}/children/`);
    }

    public findAllBuildingCareRuns(buildingId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/${buildingId}/careruns/`);
    }

    public findAllBuildings(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
                  addressLocationId?: string | undefined;
              }
            | undefined
    ): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/", {query});
    }

    // NB hierarchical from a single location
    // UNUSED
    public findAllBuildingsOfLocationId(addressLocationId: number): Promise<Building[]> {
        return this.apiClient.get<Building[]>(`buildings/byLocation/${addressLocationId}/`);
    }

    // NB non-hierarchical
    public findAllBuildingsOfLocationIds(addressLocationIds: number[]): Promise<Building[]> {
        return this.apiClient.get<Building[]>("buildings/byLocationIds/", {
            query: {
                ids: addressLocationIds.join(",")
            }
        });
    }

    public findAllBuildingsForUser(
        query?:
            | {
                  resourceType?: string | undefined;
                  showChildren?: "true" | "false" | undefined;
              }
            | undefined
    ): Promise<Building[]> {
        // FIXME: Implement back end filtering when we know what the logic should be
        return this.findAllBuildings(query);
    }

    public getCachedBuildingsMap(refresh = false) {
        if (!this.allBuildings || refresh) {
            this.allBuildings = this.findAllBuildings().then(buildings => {
                const index = {} as NumberToObjectMap<Building>;
                buildings.forEach(building => {
                    index[building.buildingId] = building;
                });
                return index;
            });
        }
        return this.allBuildings;
    }
}
