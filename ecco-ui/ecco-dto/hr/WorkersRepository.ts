import {EccoDate, Result} from "@eccosolutions/ecco-common";
import {PersonUserSummary} from "../contact-dto";
import {StaffDto, StaffJobDto} from "../hr-dto";
import {SessionData} from "../session-data/feature-config-domain";

export interface WorkersRepository {
    findOneWorker(workerId: number): Promise<StaffDto>;

    findOneWorkerJob(workerJobId: number): Promise<StaffJobDto>;

    findWorkersByServiceRecipientIds(srIds: number[] | string[]): Promise<StaffDto[]>;

    findWorkersEmployedAtByIndividualIds(
        individualIds: number[],
        employedAt: EccoDate
    ): Promise<StaffDto[]>;

    findAllWorkers(): Promise<StaffDto[]>;

    findAllWorkersAtBuilding(buildingId: number): Promise<StaffDto[]>;

    findWorkersWithAccessTo(
        serviceId: number,
        projectId?: number | undefined,
        role?: string | undefined
    ): Promise<PersonUserSummary[]>;

    findWorkersWithSameAccess(
        sessionData: SessionData,
        excludeMe?: boolean | undefined,
        role?: string | undefined
    ): Promise<PersonUserSummary[]>;

    saveStaff(staff: StaffDto): Promise<Result>;

    linkUser(workerId: number, linkUsername: string): Promise<Result>;
}
