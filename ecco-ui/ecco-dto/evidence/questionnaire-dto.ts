import {BaseWork} from "../evidence-dto";
import {ReferralSummaryDto} from "../referral-dto";
import {SessionData} from "../session-data/feature-config-domain";
import {FlagEvidenceDto} from "../evidence-dto";
import {Client} from "../client-dto";
import {ServiceRecipient} from "../service-recipient-dto";
import {StaffDto} from "../hr-dto";

/**
 * Period around a reporting range:
 * PRE is before the 'from' date,
 * DURING1 is just inside the 'from' date
 * DURING2 is just inside the 'to' date
 */
export enum SnapshotPeriod {PRE, DURING1, DURING2}

export interface QuestionnaireAnswersSnapshotPlainFields {
    serviceRecipientId: number;
    evidenceGroupKey: string;
}

/**
 * A collection of answers at a point in time for a serviceRecipientId
 * Matches com.ecco.webApi.evidence.QuestionnaireAnswersSnapshotViewModel
 */
export interface QuestionnaireAnswersSnapshotSecretFields {
    // populated client side
    referralSummary: ReferralSummaryDto;
    client?: Client | undefined;
    serviceRecipient?: ServiceRecipient;
    snapshotPeriod: SnapshotPeriod;
    sessionData: SessionData; // populated client side
    /**
     * Snapshot of answers.
     */
    answers: QuestionAnswerSnapshotDto[];
}

export type QuestionnaireAnswersSnapshotDto = QuestionnaireAnswersSnapshotPlainFields & QuestionnaireAnswersSnapshotSecretFields;

/**
 * Evidence of an answer on a service recipient questionnaire
 * Matches com.ecco.webApi.evidence.QuestionnaireAnswerSnapshotViewModel
 */
export interface QuestionAnswerSnapshotDto {
    /**
     * The id of the database entry for this change (GenericTypeAnswer)
     */
    id: number;

    /**
     * The id of the question
     */
    questionId: number;

    /**
     * The answer (which can be an id)
     */
    answer: string;

    /**
     * The datetime the work was carried out - ISO8601 extended format
     * NB Only populated from snapshot reports
     */
    workDate?: string | undefined;
}

/**
 * Evidence of an answer on a service recipient questionnaire
 * Matches com.ecco.webApi.evidence.QuestionnaireEvidenceViewModel
 */
export interface QuestionnaireWorkDto extends BaseWork {
    /**
     * The answers. A snapshot class is sufficient in that it contains all
     * that is needed to display on the history.
     */
    answers: QuestionAnswerSnapshotDto[];
    flags: FlagEvidenceDto[];
    // client-side
    serviceRecipient?: ServiceRecipient;
    staff?: StaffDto;
    // client-side
}