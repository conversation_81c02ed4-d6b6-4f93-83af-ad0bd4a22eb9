import {TaskDto} from "../workflow-dto";
import {SessionData} from "../session-data/feature-config-domain";

export interface TaskRepository {
    ensureUnplannedWorkflowTask(taskHandle: string): Promise<void>;

    postClaimTask(taskHandle: string): Promise<void>;

    postDelegateTask(taskHandle: string, username: string): Promise<void>;

    postMarkCompleted(taskHandle: string): Promise<void>;

    post(url: string): Promise<void>;

    getTasksForUser(username: string, groups?: string[] | undefined): Promise<TaskDto[]>;

    getTasksForGroup(group: string): Promise<TaskDto[]>;

    getTask(taskHandle: string): Promise<TaskDto>;

    getGroupNamesBelongingToMe(sessionData: SessionData): string[];
}
