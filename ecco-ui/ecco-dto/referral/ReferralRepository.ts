import {ReferralSummaryWithEntities, ReferralWithEntities, ServiceRecipientWithEntities} from "../dto";
import {
    ReferralDto,
    ReferralSummaryDto as ReferralSummaryDto,
    RelatedRelationship,
    ServiceRecipientAssociatedContact
} from "../referral-dto";
import {ReportCriteriaDto} from '../reports/ReportCriteriaDto';
import {BaseServiceRecipientCommandDto} from "../evidence-dto";
import {ServiceRecipientTaskBaseCommandDto as ReferralTaskBaseCommand} from "../evidence/evidence-command-dto";


export interface ReferralRepository {
    findOneReferral(referralId: number): Promise<ReferralDto>;

    findOneReferralByServiceRecipientId(serviceRecipientId: number): Promise<ReferralDto>;

    findOneReferralSummaryByServiceRecipientIdUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryDto>;

    findOneReferralSummaryWithEntitiesUsingDto(
        serviceRecipientId: number
    ): Promise<ReferralSummaryWithEntities>;

    /**
     * Returns a ServiceRecipient. Should there be a parent, then the parent property should be set
     * alongside calling addParentConfig, by calling findOneReferralWithEntities. This should be done recursively.
     * NOTE: For offline, just calls findOneReferralWithEntities (and only syncs those)
     */
    findOneServiceRecipientWithEntities(
        serviceRecipientId: number
    ): Promise<ServiceRecipientWithEntities>;

    /**
     * Returns a ReferralWithEntities. Should a referral.parentReferralId exist, then the parent property should be set
     * alongside calling addParentConfig, by calling findOneReferralWithEntities. This should be done recursively.
     */
    findOneReferralWithEntities(serviceRecipientId: number): Promise<ReferralWithEntities>;

    /**
     * Return the referrals expected for use in the offline system.
     * We could do with a pattern that handles different views of the same resource, so if we want 'findAllMyReferrals'
     * and 'findAllReferrals' then this can be handled by the same offline repository. That requires the offline code
     * to understand the same logic as online - so for now we use separate methods to clearly indicate what is being
     * used.
     */
    findAllReferralsForOffline(): Promise<ReferralDto[]>;
    findAllReferralSummary(
        reportDto: ReportCriteriaDto | Promise<ReportCriteriaDto>
    ): Promise<ReferralDto[]>;
    findAllReferralSummaryByServiceRecipientId(srIds: number[]): Promise<ReferralSummaryDto[]>;
    findAllReferralsInsideBuilding(buildingId: number): Promise<ReferralDto[]>;
    findAllReferralWithoutSecuritySummaryByClient(clientId: number): Promise<ReferralSummaryDto[]>;

    findAssociatedContactsByServiceRecipientId(
        serviceRecipientId: number
    ): Promise<ServiceRecipientAssociatedContact[]>;
    findRelatedReferrals(referralId: number): Promise<RelatedRelationship[]>;

    findUnfilteredReferralsByClientName(
        firstName: string,
        lastName: string
    ): Promise<ReferralDto[]>;

    findOneCommand(
        uuid: string,
        optional?: boolean | undefined
    ): Promise<BaseServiceRecipientCommandDto>;

    findLatestCommandPerTaskName(serviceRecipientId: number): Promise<ReferralTaskBaseCommand[]>;

    hideReferral(referral: {referralId: number}): Promise<void>;

    unhideReferral(referral: {referralId: number}): Promise<void>;
}
