import {ReferralSummaryDto} from "./referral-dto";
import {
    BaseOutcomeBasedWorkPlainFields,
    BaseOutcomeBasedWorkSecretFields,
    EvidenceFlags,
    FlagEvidenceDto,
    SupportAction
} from "./evidence-dto";


/** Data-transfer object representing the latest snapshot for an evidenceGroup
 *
 * This interface must match the Java class com.ecco.webApi.evidence.RiskEvidenceSnapshotViewModel
 * Also see evidence/dto SupportSmartStepsSnapshotPlainFields.
 */
export interface RiskFlagsSnapshotDto {
    serviceRecipientId: number;

    /** the evidence group */
    evidenceGroupKey: string;

    /** The id of the parent, e.g. referralId or workerId */
    parentId: number;

    latestFlags: FlagEvidenceDto[];
}


/** probably will have this... */
export interface RiskGroupEvidenceDto {
    id: number;

    // >> from EvidenceRiskAreaToViewModel, but probably should be RiskRatingsDto as RiskGroupEvidenceDto is part of work
    workDate?: string | undefined;
    workUuid?: string | undefined;
    serviceRecipientId: number;
    serviceAllocationId?: number | undefined;
    // populated client side for reporting
    referralSummary?: ReferralSummaryDto | undefined;
    // populated client side for reporting
    // >>

    /** Id of the related RiskArea definition (aka Outcome - in outcomes table at back end) */
    riskAreaId: number;

    /** name of the related RiskArea definition (aka Outcome - in outcomes table at back end) */
    riskAreaName: string;

    trigger: string;
    control: string;

    /** "rag" or "recencyImpact" */
    levelMeasure: string;

    /** 0-20 for recency impact or above RedAmberGreen enum values for "rag" */
    level: number;
}

export interface RiskWorkPlainFields extends BaseOutcomeBasedWorkPlainFields {
}

export interface RiskWorkSecretFields extends BaseOutcomeBasedWorkSecretFields {
    /** Changes to actions on the client's risk assessment made as part of this
     *  work. */
    riskActions: RiskActionEvidenceDto[];

    /** Changes to flags made as part of this work */
    flags: FlagEvidenceDto[];

    riskAreas: RiskGroupEvidenceDto[];

    handledSupportWorkIds?: string[] | undefined; // uuids of support work

    // populated client side
    // a Referral instance of ServiceRecipient for convenience
    referralSummary?: ReferralSummaryDto | undefined;
    // populated client side
}
/** Needs to match ThreatWorkViewModel */
export type RiskWorkEvidenceDto = RiskWorkPlainFields & RiskWorkSecretFields;

/** Needs to match ThreatActionSummaryToViewModel */
export interface RiskActionEvidenceDto extends SupportAction {
}

export interface RiskFlagsSecretFields {
    work: RiskWorkEvidenceDto; // probably without everything - see EvidenceFlagsToViewModel
}

export interface RiskFlags extends EvidenceFlags, RiskFlagsSecretFields {}
