import {IdNameDisabled} from "@eccosolutions/ecco-common";
import {ReferralSummaryDto} from "./referral-dto";

export interface Address {
    // only populated when using AddressedLocationToViewModel
    addressId: number;

    /** Components of the address within a town/city/district, usually up to
     * three entries.
     * NOTE: 3 lines of max len: 63, 31, 31 */
    address: string[];

    /** TODO: maybe...  Extracted from address line 1 server-side and sorted */
    // streetName: string;

    /** The town/city/district of this address. */
    town: string | null;

    county: string | null;

    /** The postcode, or null if not known. */
    postcode: string;

    disabled: boolean;
}

export interface ContactPlainFields {
    /** Primary key */
    contactId: number;
}

export interface ContactSecretFields {
    /** Unique code */
    code?: string | undefined;

    avatarId?: number | undefined;

    discriminator: "agency" | "client" | "individual";

    /** date contact was archived - eg agency/professional - local date, EccoDate.parseIso8601 */
    archived: string | null;

    /** The person's postal address, or null if not known. */
    address?: Address | undefined;

    /** The unique identifier of the client's address (this.address).
     *
     * TODO: This should be moved to the Address interface. */
    addressedLocationId?: number | null | undefined; // Allow null for use in React state update() in ClientDetail.tsx (or should we just avoid doing that)

    /** The person's phone number, or null if not known. */
    phoneNumber?: string | undefined;

    /** The person's mobile phone number, or null if not known. */
    mobileNumber?: string | undefined;

    /** The person's email address, or null if not known. */
    email?: string | undefined;

    /** true if there is a user account associated with this contact. This is omitted on agency and client */
    isUser?: boolean | undefined;

    /** ISO-8601 timestamp of when user last logged in. Null if never */
    userLastLoggedIn?: string | undefined;
}

export interface Contact extends ContactPlainFields, ContactSecretFields {
}

/* This interface must match the Java class com.ecco.webApi.contacts.AgencyViewModel. */
export interface Agency extends Company {
    outOfArea: boolean;

    /** Category of the agency, eg 'police' */
    agencyCategoryId?: number;

    /**
     * A context of a new agency, only for create.
     * Currently, this is the serviceRecipientId which is used to record the agency's relevant service allocation.
     */
    contextId?: number | undefined;
}

export interface Company extends Contact {
    /** The name of the company. */
    companyName: string;

    /** The company's postal address, or null if not known. */
    address?: Address | undefined;

    email: string;

    /** The person's phone number, or null if not known. */
    phoneNumber?: string | undefined;
}

export interface IndividualPlainFields extends ContactPlainFields {
    /** Refers to the Company or Agency this Individual is part of */
    organisationId?: number | undefined;

    // populated client side
    organisation?: Agency | Company | undefined;
    // populated client side

    /** Calender id associated with this individual */
    calendarId?: string | undefined;
}

export interface IndividualSecretFields extends ContactSecretFields {
    /** e.g. Managing Director */
    jobTitle?: string | undefined;

    /** The title of the person ("Mr", "Mrs", etc.) */
    title: string;

    /** The first name of the person. */
    firstName: string;

    /** The last name of the person. */
    lastName: string;

    /** Prefers to be called */
    knownAs?: string | undefined;

    /** The persons pronouns */
    pronounsId?: number | undefined;

    /** The person's preferred contact method. */
    preferredContactMethod?: string | undefined;
}

/* This interface must match the Java class com.ecco.webApi.contacts.IndividualViewModel. */
export interface Individual extends IndividualPlainFields, IndividualSecretFields {
}

export interface PersonPlainFields extends IndividualPlainFields {
}

export interface PersonSecretFields extends IndividualSecretFields {
    /** The client's ID in an external system (e.g. Northgate).
     *
     * The external system that this ID belongs to is identified by
     * this.externalSystemSource. */
    externalSystemRef: string;

    /** The external system that owns the ID specified by
     * this.externalSystemRef.
     *
     * This should match an com.ecco.config.dom.ExternalSystem entity. */
    externalSystemSource: string;

    /** The person's date of birth in ISO 8601 extended format (YYYY-MM-DD),
     * or null if not known. */
    birthDate?: string | undefined;

    /** The person's date of death in ISO 8601 extended format (YYYY-MM-DD) */
    dateOfDeath?: string | undefined;

    /** The person's age (as of now or at death) in years, or null if not known. */
    age?: number | undefined;

    /** The person's gender, or null if not known. */
    genderId?: number | undefined;

    /** The person's gender at birth, or null if not known. */
    genderAtBirthId?: number | undefined;

    /** The person's disability, or null. */
    disabilityId?: number | undefined;

    /** The person's ethnic origin.
     *
     * This value should match an entry in Settings -> Lists -> Ethnic Origins. */
    ethnicOriginId?: number | undefined;

    /** The person's nationality. */
    nationalityId?: number | undefined;

    /** The person's marital status
     *  NB this is sometimes recorded on the referral instead, since changes to this can impact relevant reports for the industry
     */
    maritalStatusId?: number | undefined;

    /** The person's religion.
     *
     * This value should match an entry in Settings -> Lists -> Religions. */
    religionId?: number | undefined;

    /** The person's first language.
     *
     * This value should match an entry in Settings -> Lists -> Languages. */
    firstLanguageId?: number | undefined;

    /** The person's sexual orientation, or null. */
    sexualOrientationId?: number | undefined;
}

/** Data-transfer object representing a person.
 *
 * This interface serves as a base for specific types of person, such as
 * Client or Staff.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.PersonViewModel. */
export interface Person extends PersonPlainFields, PersonSecretFields {
}

/** Data-transfer object representing a summary of a person who has an authority.
 *
 * This interface must match the Java class com.ecco.webApi.viewModels.IndividualUserSummaryViewModel. */
export interface PersonUserSummary extends IdNameDisabled {
    individualId: number;
    firstName: string;
    lastName: string;
    userId: number;
    username: string;
    displayName: string;
    /**
     * Unique reference to the native object of the user (calendarId) who created the event.
     * Typically this is of the format 'entity://HibUser/2'.
     * {@see EntryConverter.convert}
     * {@see CosmoHelper.syncAttendeesWithCalendars()}
     * {@see AttendeeAdapter.java getDirEntryRef}
     */
    calendarIdUserReferenceUri: string; // cosmo representation - see CosmoHelper.syncAttendeesWithCalendars
    calendarId: string; // integration-representation (pointing to the user's collection)
}


/**
 * See com.ecco.webApi.contacts.address.AddressHistoryViewModel
 */
export interface AddressHistoryDto {
    /**
     * The surrogate id.
     */
    id: number;

    /**
     * The service recipient
     */
    serviceRecipientId: number;

    /**
     * The buildingId at this point in time (can be null)
     */
    buildingId?: number | undefined;

    /**
     * The addressId at this point in time (can be null)
     */
    addressId?: number | undefined;

    /**
     * The date and time when the value applied from
     */
    validFrom: string;

    /**
     * The date and time when the value applied to (can be null)
     */
    validTo?: string | undefined;

    // populated client side
    address?: Address | undefined;
    referralSummary?: ReferralSummaryDto | undefined;
    // populated client side
}
