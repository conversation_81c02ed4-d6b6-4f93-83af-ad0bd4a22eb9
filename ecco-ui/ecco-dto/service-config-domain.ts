import {
    ActionDto,
    ActionGroupDto,
    ActivityType,
    OutcomeDto,
    QuestionGroup,
    RiskActionDto,
    RiskActionGroup as RiskActionGroupDto,
    RiskAreaDto,
    ServiceDto,
    ServiceParametersField,
    ServiceTypeDto,
    TaskDefinitionEntry as TaskDefinitionEntryDto,
    TaskSettingName,
    TaskSettings
} from "./service-config-dto";
import {array, IdName, StringUtils} from "@eccosolutions/ecco-common";
import {EvidenceDef, EvidencePageType} from "./evidence-dto";
import {Messages} from "./messages/Messages";
import {ListDefinitionEntry, SessionData} from "./session-data/feature-config-domain";
import {isNotNull} from "@softwareventures/nullable";
import {PrefixType} from "./service-recipient-dto";

function notNull(it: any) { return it != null; }

const keyLastBy = array.keyLastBy;

/** An Outcome, ActionGroup or Action or ... */
export class ServiceTypeElement {
    constructor(public id: number, public name: string, public disabled: boolean) {}

    public getId() {
        return this.id;
    }

    public getName() {
        return this.name;
    }

    public isDisabled() {
        return this.disabled;
    }

    public tokenize(tokenize: (source: string) => string[]): string[] {
        return tokenize(this.getName()); // NOTE: Used to throw an exception
    }
}

export class TaskDefinitionEntry {
    private taskSettings: TaskSetting[] = [];

    constructor(
        private dto: TaskDefinitionEntryDto,
        private messages: Messages
    ) {
        const settings: TaskSettings = dto.settings;
        // Object.keys can be replaced by various means if 'too modern'
        const propertiesFound = Object.keys(settings) as TaskSettingName[];
        propertiesFound.forEach(prop => {
            this.taskSettings.push(new TaskSetting(prop, settings[prop]));
        });
    }

    getName() {
        return this.dto.name;
    }

    /** Display name for the task */
    getDisplayName() {
        const {messages} = this;
        if (this.getSetting("titleRaw")) {
            return this.getSetting("titleRaw");
        }
        // check from existing task list
        let messageKey = ("referralView." + this.getName()) as keyof Messages;
        if (messages[messageKey]) {
            return messages[messageKey];
        }
        // check from breadcrumb in the wizard
        messageKey = ("referralBreadcrumb." + this.getName()) as keyof Messages;
        if (messages[messageKey]) {
            return messages[messageKey];
        }
        // we would return friendlyName here, but we don't need another thing to deal with
        return StringUtils.camelToSpaces(this.getName());
    }

    isAllowNext() {
        return this.dto.allowNext;
    }

    getOrderBy() {
        return this.dto.orderby;
    }

    getTaskDueDateSchedule() {
        return this.dto.dueDateSchedule;
    }

    isTaskDueDateSchedulePerpetual() {
        return this.dto.dueDateSchedule ? this.dto.dueDateSchedule.indexOf("end") == -1 : false;
    }

    getSettings() {
        return this.taskSettings;
    }

    /** NOTE: Prefer using via ServiceType.getTaskDefinitionSetting() as that does null handling for you */
    getSetting(settingName: TaskSettingName) {
        return this.dto.settings[settingName];
    }

    /** NOTE: Prefer using via ServiceType.taskDefinitionSettingHasFlag() as that does null handling for you.
     * return true if the flag in the named setting (i.e. is it in the comma-sep list) */
    hasSetting(
            settingName: TaskSettingName,
            propertyName: string,
            outcomeId?: number | undefined) {
        const outcomeSetting =
            outcomeId &&
            this.dto.outcomeSettings &&
            this.dto.outcomeSettings[outcomeId] &&
            (this.dto.outcomeSettings[outcomeId][settingName] as string);
        const setting = outcomeSetting || (this.dto.settings[settingName] as string); // NOTE: Assumes this is a string setting
        return setting ? setting.split(",").indexOf(propertyName) >= 0 : false;
    }
}

export class TaskSetting {
    constructor(private name: string, private value: any) {
    }

    public getName() {
        return this.name;
    }
    public getValue() {
        return this.value;
    }
    setValue(value: string) {
        this.value = value;
    }
}

export class Services {
    private readonly services: Service[];

    private servicesById: Record<string, Service>;

    constructor(private serviceDtos: ServiceDto[]) {
        this.services = serviceDtos
            .sort( (a,b) => a.name.localeCompare(b.name) )
            .map( service => new Service(service) );

        this.servicesById = keyLastBy(this.services, service => service.getServiceId());
    }

    public getDtos() { return this.serviceDtos; }

    public getServiceDto(serviceId: number): ServiceDto {
        return this.servicesById[serviceId]!.getDto();
    }

    public getServicesFromServiceType(serviceTypeId: number): Service[] {
        return this.services.filter(s => s.getDto().serviceTypeId == serviceTypeId);
    }

}

export class Service {

    constructor(private dto: ServiceDto) {
    }

    getDto(): ServiceDto {
        return this.dto;
    }

    getServiceId() {
        return this.dto.id;
    }

    getParameterValue(param: ServiceParametersField) {
        return this.getDto().parameters && this.getDto().parameters[param]
    }
}

/**
 * Resolves the correct config to use. Currently based on ServiceType its designed to find the right service type for a
 * child referral (central processing). It could expand to return the correct outcomes where the referral aspect settings
 * indicate a reduced set. Also, other aspects such as comment type could be added.
 */
export interface ConfigResolver {

    /**
     * Used as a catch all, but each method should be intercepted by this class
     */
    getServiceType(): ServiceType;

    /**
    * Overload getOutcomes() so that we can determine which ones to apply
    * according to any central processing.
    */
    getOutcomes(): Array<Outcome>;

    /**
     * Get questionGroups listed for the taskName.
     */
    getQuestionGroupsFilteredForTask(taskName: string): Array<QuestionGroup>;

    /**
     * Get outcomes filtered by 'outcomesById' (or 'outcomes' - deprecated) when configured. If no setting, return
     * all outcomes for the service.
     */
    getOutcomesFilteredForTask(taskName: string): Array<Outcome>;

    /**
     * Get riskAreas filtered by 'outcomesById' (or 'outcomes' - deprecated) when configured. If no setting, return
     * all riskAreas for the service.
     */
    getRiskAreasFilteredForTask(taskName: string): Array<RiskArea>;

}

export class ConfigResolverDefault implements ConfigResolver {
    private filteredOutcomes: Array<Outcome> | null = null;

    public static fromLegacyServiceType(
        serviceType: ServiceType,
        sessionData: SessionData
    ): ConfigResolverDefault {
        return new ConfigResolverDefault(serviceType, sessionData);
    }

    public static fromServiceRecipient(
        sessionData: SessionData,
        serviceAllocationId: number,
        serviceTypeId: number | null
    ): ConfigResolverDefault {
        return serviceTypeId != null
            ? new ConfigResolverDefault(sessionData.getServiceTypeById(serviceTypeId), sessionData)
            : new ConfigResolverDefault(
                  sessionData.getServiceTypeByServiceCategorisationId(serviceAllocationId),
                  sessionData
              );
    }

    private constructor(
        private readonly serviceType: ServiceType,
        private sessionData: SessionData
    ) {}

    // replace the default ordering to order by the setting
    private getFilteredOutcomes(
        configResolver: ConfigResolver,
        taskName: string
    ): Outcome[] | null {
        const outcomeIds = this.serviceType.getTaskDefinitionSetting(taskName, "outcomesById");
        if (outcomeIds) {
            return configResolver
                .getServiceType()
                .getFilteredOutcomesFromIds(outcomeIds, this.sessionData);
        }

        let outcomeNames = this.serviceType.getTaskDefinitionSetting(taskName, "outcomes");
        if (outcomeNames) {
            return configResolver
                .getServiceType()
                .getFilteredOutcomesFromNames(outcomeNames, this.sessionData);
        }
        return null;
    }

    private getFilteredRiskAreas(
        configResolver: ConfigResolver,
        taskName: string
    ): Array<RiskArea | undefined> | null {
        const outcomesByIdToFilterStr = this.serviceType.getTaskDefinitionSetting(
            taskName,
            "outcomesById"
        );
        if (outcomesByIdToFilterStr) {
            return configResolver
                .getServiceType()
                .getFilteredRiskAreasFromIds(outcomesByIdToFilterStr);
        }

        const outcomesToFilterStr = this.serviceType.getTaskDefinitionSetting(taskName, "outcomes");
        if (outcomesToFilterStr) {
            return configResolver
                .getServiceType()
                .getRiskAreas()
                .filter(outcome => outcomesToFilterStr.indexOf(outcome.getName()) > -1);
        }
        return null;
    }

    public getRiskAreasFilteredForTask(taskName: string): RiskArea[] {
        // FIXME: The cast is not null safe!
        return (
            (this.getFilteredRiskAreas(this, taskName) as RiskArea[]) ||
            this.serviceType.getRiskAreas()
        );
    }

    public getOutcomesFilteredForTask(taskName: string): Outcome[] {
        return this.getFilteredOutcomes(this, taskName) || this.getOutcomes();
    }

    public getQuestionGroupsFilteredForTask(taskName: string): QuestionGroup[] {
        const questionGroupIds = this.serviceType.getTaskDefinitionSetting(
            taskName,
            "questionGroupsById"
        );
        if (questionGroupIds) {
            return this.getServiceType().getFilteredQuestionGroupsFromIds(
                questionGroupIds,
                this.sessionData
            );
        }

        let questionGroupNames = this.serviceType.getTaskDefinitionSetting(taskName, "questions");
        if (questionGroupNames) {
            return this.getServiceType().getFilteredQuestionGroupsFromNames(
                questionGroupNames,
                this.sessionData
            );
        }

        // else return nothing
        return [];
    }

    getOutcomes(): Array<Outcome> {
        if (this.filteredOutcomes) {
            return this.filteredOutcomes;
        }
        return this.serviceType
            .getOutcomes()
            .map(outcome => this.sessionData.getOutcomeById(outcome.id)!);
    }

    taskDefinitionSettingHasFlag(taskName: string, name: TaskSettingName, value: string): boolean {
        return this.serviceType.taskDefinitionSettingHasFlag(taskName, name, value) != null;
    }

    getServiceType() {
        return this.serviceType;
    }
}

/** The ServiceType, complete with methods to do various things with Outcomes etc */
export class ServiceType {
    /** the service type id. */
    id: number;

    /** Sparse array of actions by id */
    private actionsById: {[id: number]: Action} = {};

    private outcomesById: {[id: number]: IdName} = {};

    private riskActionsById: {[id: number]: RiskAction} = {};

    private riskAreasById: {[id: number]: RiskArea} = {};

    private questionGroupsById: {[id: number]: IdName | undefined} = {};

    private taskDefEntriesByName: {[name: string]: TaskDefinitionEntry} = {};

    private outcomes = new Array<Outcome>();

    /** The outcome terminology for a threat */
    private riskAreas = new Array<RiskArea>();

    private questionGroups = new Array<IdName>();

    constructor(
        private serviceTypeDto: ServiceTypeDto,
        private messages: Messages,
        outcomeLookup: (id: number) => OutcomeDto
    ) {
        if (!serviceTypeDto) {
            throw new TypeError("serviceTypeDto must not be null");
        }
        this.id = serviceTypeDto.id;

        // outcomes are ordered within (eg action groups, actions - see Outcome.java)
        // order of outcomes are at the config level - see 'outcomesById' in taskSettingTypes
        // which can be determined by ConfigResolverDefault
        // however we cannot rely on this for historical items
        // unless we left the service type alone, but could at least use within outcome-ordering
        // the ordering of hierarchy etc is within the smart step
        serviceTypeDto.supportOutcomes.forEach(dto => {
            const outcome = new Outcome(outcomeLookup(dto.id));
            this.outcomes.push(outcome);
            this.outcomesById[outcome.getId()] = outcome;
            this.addActionGroups(outcome.actionGroups);
        });

        serviceTypeDto.riskAreas.forEach(dto => {
            const riskArea = new RiskArea(dto);
            this.riskAreas.push(riskArea);
            this.riskAreasById[riskArea.getId()] = riskArea;
            // TODO we need the groups (BH?) but for now skip
            //this.addRiskGroups(riskArea.actionGroups);
            this.addRiskActionGroups(riskArea.riskActionGroups);
        });

        serviceTypeDto.questionGroups.forEach(dto => {
            this.questionGroups.push(dto);
            this.questionGroupsById[dto.id] = dto;
        });

        if (!serviceTypeDto.taskDefinitionEntries) {
            throw new Error(
                "ecco offline has been upgraded - you must go to 'offline menu' and synchronise before you can continue"
            );
        }
        serviceTypeDto.taskDefinitionEntries.forEach(taskDef => {
            this.taskDefEntriesByName[taskDef.name] = new TaskDefinitionEntry(
                taskDef,
                this.messages
            );
        });
    }

    /** Add these action groups to relevant internal objects */
    private addActionGroups(actionGroups: ActionGroup[]) {
        actionGroups.forEach(group => {
            this.addActions(group.actions);
        });
    }

    /** Add these actions to relevant internal objects */
    private addActions(actions: Action[]) {
        actions.forEach(action => {
            this.actionsById[action.getId()] = action;
        });
    }

    /** Add these risk action groups to relevant internal objects */
    private addRiskActionGroups(riskActionGroups: RiskActionGroup[]) {
        riskActionGroups.forEach(group => {
            this.addRiskActions(group.riskActions);
        });
    }

    /** Add these actions to relevant internal objects */
    private addRiskActions(riskActions: RiskAction[]) {
        riskActions.forEach(action => {
            this.riskActionsById[action.getId()] = action;
        });
    }

    isHideOnNew() {
        return this.serviceTypeDto.hideOnNew;
    }

    isHideOnList() {
        return this.serviceTypeDto.hideOnList;
    }

    /**
     * Return the Action object from the id, or null
     * @param actionId
     * @returns {Action} - this can be null, since actionsById's are populated from the outcomes -> groups -> actions
     */
    getActionById(actionId: number) {
        return this.actionsById[actionId];
    }

    /**
     * Return the ActionComponent - being either support or risk, or null
     * @param actionId
     * @returns {ActionComponent} - this can be null, since id's are populated from the outcomes -> groups -> actions
     */
    getAnyActionById(actionId: number): ActionComponent | null {
        const supportActionDef = this.actionsById[actionId];
        if (supportActionDef) {
            return supportActionDef;
        }
        return this.riskActionsById[actionId] ?? null;
    }

    /**
     * Get the outcome for the action requested. The return type can be null in cases where the actionId is from a
     * historical record which is no longer configured in the 'latest' outcomes. The current uses of this method
     * are valid not to expect null.
     */
    getOutcomeForActionId(actionId: number) {
        const a = this.actionsById[actionId];
        return a ? a.actionGroup.outcome : null;
    }

    /**
     * Get the outcome for the action requested. The return type can be null in cases where the actionId is from a
     * historical record which is no longer configured in the 'latest' outcomes. The current uses of this method
     * are valid not to expect null.
     */
    getRiskAreaForRiskActionId(actionId: number) {
        const a = this.riskActionsById[actionId];
        return a ? a.riskActionGroup.riskArea : null;
    }

    getOutcomes() {
        return this.outcomes;
    }

    getOutcomesForTaskName(taskName: string, sessionData: SessionData): Outcome[] {
        let outcomeIds = this.getTaskDefinitionSetting(taskName, "outcomesById");
        if (outcomeIds) {
            return this.getFilteredOutcomesFromIds(outcomeIds, sessionData);
        }

        let outcomeNames = this.getTaskDefinitionSetting(taskName, "outcomes");
        if (outcomeNames) {
            return this.getFilteredOutcomesFromNames(outcomeNames, sessionData);
        }
        return this.outcomes.map(outcome => sessionData.getOutcomeById(outcome.id)!);
    }

    getFilteredOutcomesFromIds(outcomeIds: string, sessionData: SessionData): Outcome[] {
        let ids = outcomeIds.split(",").map(str => parseInt(str));
        // NB order is preserved here
        return ids
            .map(id => {
                const outcome = this.outcomesById[id];
                if (!outcome) {
                    console.error(`Outcome: ${id} not found in %o`, this.outcomesById);
                }
                return outcome;
            })
            .filter(isNotNull)
            .map(outcome => sessionData.getOutcomeById(outcome.id)!);
    }

    getFilteredRiskAreasFromIds(outcomeIds: string): Array<RiskArea | undefined> {
        let ids = outcomeIds.split(",").map(str => parseInt(str));
        // NB order is preserved here
        // NB if the serviceTypeDto.riskAreas isn't providing the right outcomes, we pass on the error by mapping to 'undefined'
        // also see getFilteredRiskAreas (setting 'outcomesById')
        return ids.map(id => this.riskAreasById[id]);
    }

    getFilteredOutcomesFromNames(outcomeNames: string, sessionData: SessionData) {
        // see FlagMap.java for original which doesn't work here so we do the following
        let names = outcomeNames
            .replace(/\\,/g, "####")
            .split(",")
            .map(str => str.replace("####", ","));
        // NB specified order via 'orderby' column. SessionDataController -> sessionDataService.findOutcomes -> "FROM OutcomeSupport ORDER BY orderby,id"
        return this.outcomes
            .filter(outcome => names.indexOf(outcome.name) >= 0)
            .map(outcome => sessionData.getOutcomeById(outcome.id)!);
    }

    // NB could be cached
    getActionsForTaskName(taskName: string, sessionData: SessionData) {
        return this.getOutcomesForTaskName(taskName, sessionData)
            .map(o => o.actionGroups.map(ag => ag.actions).reduce((r, x) => r.concat(x), []))
            .reduce((r, x) => r.concat(x), []);
    }

    // TODO order by alpha? see below, b6498b70
    getFilteredQuestionGroupsFromIds(
        questionGroupIds: string,
        sessionData: SessionData
    ): QuestionGroup[] {
        let ids = questionGroupIds.split(",").map(str => parseInt(str));
        // NB order is preserved here
        return ids.map(id => {
            if (this.questionGroupsById[id] == null) {
                throw new TypeError(
                    `QuestionGroup: ${id} not found in ServiceType ${this.serviceTypeDto.name}`
                );
            } else {
                return sessionData.getQuestionGroupById(id)!;
            }
        });
    }

    getFilteredQuestionGroupsFromNames(
        questionGroupNames: string,
        sessionData: SessionData
    ): QuestionGroup[] {
        // see FlagMap.java for original which doesn't work here so we do the following
        const names = questionGroupNames
            .replace(/\\,/g, "####") // avoid splitting on purposefully escaped commas: '\,'
            .split(",")
            .map(str => str.replace("####", ","))
            .map(str => str.trim());
        // NB questiongroups are NOT ordered like getFilteredOutcomesFromNames
        const qgs = this.questionGroups.filter(
            questionGroupName => names.indexOf(questionGroupName.name) >= 0
        );
        return qgs
            .sort((a, b) => names.indexOf(a.name) - names.indexOf(b.name))
            .map(qg => sessionData.getQuestionGroupById(qg.id)!);
    }

    getAllQuestionGroups() {
        return this.questionGroups;
    }

    getRiskAreas(): RiskArea[] {
        return this.riskAreas;
    }

    getName() {
        return this.serviceTypeDto.name;
    }

    getId() {
        return this.serviceTypeDto.id;
    }

    getPrimaryRelationshipId(): number {
        return this.serviceTypeDto.primaryRelationshipId;
    }

    getWizardTasks() {
        return this.serviceTypeDto.wizardTasks;
    }

    getTaskDefinitionEntries() {
        return this.serviceTypeDto.taskDefinitionEntries.map(
            dto => new TaskDefinitionEntry(dto, this.messages)
        );
    }

    /** returns true if the named task forms part of the service workflow.
     *  At the back-end it means that there is an entry in servicetypes_referralaspects */
    hasTaskDefinitionEntry(taskName: string): boolean {
        return !!this.taskDefEntriesByName[taskName];
    }

    /** return the task definition or null if not found - NOTE: use methods below to avoid null */
    getTaskDefinitionEntry(taskName: string): TaskDefinitionEntry | null {
        return this.taskDefEntriesByName[taskName] ?? null;
    }

    lookupTaskName(taskName: string) {
        let entry = this.getTaskDefinitionEntry(taskName);
        return entry ? entry.getDisplayName() : StringUtils.camelToSpaces(taskName);
    }

    getTaskDefinitionSetting(taskName: string, settingName: TaskSettingName) {
        return (
            this.taskDefEntriesByName[taskName] &&
            this.taskDefEntriesByName[taskName].getSetting(settingName)
        );
    }
    getTaskDefinitionSettingAsNumericCsv(
        taskName: string,
        settingName: TaskSettingName
    ): number[] | undefined {
        const value = this.taskDefEntriesByName[taskName]?.getSetting(settingName);
        return value ? value.split(",").map(v => parseInt(v.trim())) : undefined;
    }
    // more consistent method name - deferring to the below
    getTaskDefinitionSettingHasFlag(
        taskName: string,
        settingName: TaskSettingName,
        propertyName: string,
        outcomeId?: number
    ) {
        return this.taskDefinitionSettingHasFlag(taskName, settingName, propertyName, outcomeId);
    }
    taskDefinitionSettingHasFlag(
        taskName: string,
        settingName: TaskSettingName,
        propertyName: string,
        outcomeId?: number | undefined
    ): boolean | undefined {
        return (
            this.taskDefEntriesByName[taskName] &&
            this.taskDefEntriesByName[taskName].hasSetting(settingName, propertyName, outcomeId)
        );
    }

    /** Get taskdef names where the setting matches - returns empty array if none */
    findTaskNamesWhereSettingHasFlag(
        settingName: TaskSettingName,
        propertyName: string,
        outcomeId?: number | undefined
    ): string[] {
        return Object.values(this.taskDefEntriesByName)
            .filter(taskDef =>
                this.taskDefinitionSettingHasFlag(
                    taskDef.getName(),
                    settingName,
                    propertyName,
                    outcomeId
                )
            )
            .map(taskDef => taskDef.getName());
    }

    getEvidenceActAs(taskName: string) {
        const assessment = this.taskDefinitionSettingHasFlag(taskName, "actAs", "assessment");
        const reduction = this.taskDefinitionSettingHasFlag(taskName, "actAs", "reduction");
        // commentOnly we don't actually have in the UI
        // review is only needed for hardcoded pages, and will be phased out in favour for 'tasks' incomplete/complete etc

        return assessment && reduction
            ? EvidencePageType.assessmentReduction
            : assessment
              ? EvidencePageType.assessment
              : reduction
                ? EvidencePageType.reduction
                : null;
    }

    getFirstRiskTaskName(sessionData: SessionData): string | undefined {
        const tasks = [
            "riskManagement",
            "riskManagementLone",
            "threatReduction",
            "threatAssessmentReduction",
            "threatAssessment"
        ];

        const legacyPriority = tasks.find(taskName => this.hasTaskDefinitionEntry(taskName));
        const firstRisk = this.getTaskDefinitionEntries()
            .filter(t => {
                return "EVIDENCE_RISK" == EvidenceDef.taskEvidenceType(sessionData, t.getName());
            })
            .pop();
        return legacyPriority ? legacyPriority : firstRisk?.getName();
    }

    getFirstSupportTaskName(sessionData: SessionData): string | undefined {
        const tasks = [
            "needsReduction",
            "needsAssessmentReduction",
            "rotaVisit",
            "needsAssessment",
            "needsAssessmentReductionReview",
            "needsReductionSP",
            "needsAssessmentHousehold",
            "engagementComments",
            "carePlan"
        ];

        const legacyPriority = tasks.find(taskName => this.hasTaskDefinitionEntry(taskName));
        const firstSupport = this.getTaskDefinitionEntries()
            .filter(t => {
                return "EVIDENCE_SUPPORT" == EvidenceDef.taskEvidenceType(sessionData, t.getName());
            })
            .pop();
        return legacyPriority ? legacyPriority : firstSupport?.getName();
    }

    getFlagsByListNameOrId(sessionData: SessionData, taskName: string) {
        let flags: ListDefinitionEntry[];
        const flagListName = this.getTaskDefinitionSetting(taskName, "flagListName");
        if (flagListName) {
            flags = sessionData.getListDefinitionEntriesByListName(flagListName);
        } else {
            const flagCsv = this.getTaskDefinitionSetting(taskName, "flagThreatsById");
            // FIXME: The cast is not null safe!
            flags = sessionData.getListDefinitionsFilteredByCsvIds(
                flagCsv || null
            ) as ListDefinitionEntry[];
        }
        // filtered disabled - will affect EvidenceCommentForm (good) and externalSyncHooks (maybe good)
        return flags.filter(f => !f.getDisabled());
    }

    getCommentTypesById(sessionData: SessionData, taskName: string) {
        // if there is a selected entry, we choose the selected set as the ones to show
        const typesCsvSpecific = this.getTaskDefinitionSetting(taskName, "commentTypesById");
        const typesCsvGeneral = this.getTaskDefinitionSetting("referralView", "commentTypesById");
        const typesCsv = typesCsvSpecific || typesCsvGeneral;
        return sessionData.getListDefinitionsFilteredByCsvIds(typesCsv || null);
    }
}


export interface OutcomeComponent {
    getId(): number;
    getName(): string;
    isDisabled(): boolean;
    getActionGroups(): ActionGroupComponent[];
}
export interface ActionGroupComponent {
    getId(): number;
    getName(): string;
    isDisabled(): boolean;
    getActions(): ActionComponent[];
    getOutcome(): OutcomeComponent;
}
export interface ActionComponent {
    getId(): number;
    getName(): string;
    isDisabled(): boolean;
    getInitialText(): string | null;
    getStatusChangeReasonListName(): string | null;
    getOutcome(): Outcome | RiskArea;
    getActivityTypes(): ActivityType[];
    getOrderby(): number;
    getActionGroupComponent(): ActionGroupComponent;
}

/** Outcome area - such as 'economic wellbeing' for an evidence screen such as support plan */
export class Outcome extends ServiceTypeElement implements OutcomeComponent {
    /** Total number of actions in all action groups - for spidergraph */
    private numActions = 0;

    /** a collection of action-headings, or groups of actions for this outcome */
    actionGroups: ActionGroup[] = [];

    uuid: string;

    constructor(outcomeDto: OutcomeDto) {
        // null-check is nice-to-have given super must be first statement
        super(outcomeDto.id, outcomeDto.name, outcomeDto.disabled);

        this.uuid = outcomeDto.uuid;

        outcomeDto.actionGroups.forEach(actionGroupDto => {
            this.actionGroups.push(new ActionGroup(actionGroupDto, this));
            this.numActions += actionGroupDto.actions.length;
        });
    }

    public getNumActions() {
        return this.numActions;
    }

    public getActionGroups() {
        return this.actionGroups;
    }

    public override tokenize(tokenize: (source: string) => string[]) {
        return tokenize(this.getName());
    }

    public asDto(): OutcomeDto {
        return {
            id: this.getId(),
            uuid: this.uuid,
            name: this.getName(),
            actionGroups: this.actionGroups.map(actionGroup => actionGroup.asDto())
        } as OutcomeDto;
    }
}

/** RiskArea - (aka ThreatOutcome .. maybe ..) */
export class RiskArea extends ServiceTypeElement implements OutcomeComponent {
    /** a collection of action-headings, or groups of actions for this outcome */
    riskActionGroups: RiskActionGroup[] = [];

    public getActionGroups() {
        return this.riskActionGroups;
    }

    constructor(private riskAreaDto: RiskAreaDto) {
        // null-check is nice-to-have given super must be first statement
        super(riskAreaDto.id, riskAreaDto.name, riskAreaDto.disabled);

        riskAreaDto.actionGroups.forEach(actionGroupDto => {
            this.riskActionGroups.push(new RiskActionGroup(actionGroupDto, this));
        });
    }

    public asDto(): RiskAreaDto {
        return this.riskAreaDto;
    }
}

/** RiskActionGroup - a collection of smart step Action's grouped under a heading. */
export class RiskActionGroup extends ServiceTypeElement implements ActionGroupComponent {

    /** a collection of actions (smart steps) for this group */
    riskActions: RiskAction[] = [];

    riskArea: RiskArea;

    constructor(actionGroupDto: RiskActionGroupDto, riskArea: RiskArea) {
        // null-check is nice-to-have given super must be first statement
        super(actionGroupDto.id, actionGroupDto.name, actionGroupDto.disabled);

        this.riskArea = riskArea;
        actionGroupDto.actions.forEach((actionDto) => {
            this.riskActions.push(new RiskAction(actionDto, this));
        })
    }

    public getActions() {
        return this.riskActions;
    }

    public getOutcome() {
        return this.riskArea;
    }

}

/** RiskAction - an individual action (or smart step) */
export class RiskAction extends ServiceTypeElement implements ActionComponent {
    initialText: string | null;
    orderby: number;
    /** the parent action group */
    riskActionGroup: RiskActionGroup;

    constructor(riskActionDto: RiskActionDto, riskActionGroup: RiskActionGroup) {
        // null-check is nice-to-have given super must be first statement
        super(riskActionDto.id, riskActionDto.name, riskActionDto.disabled);
        this.initialText = riskActionDto.initialText;
        this.orderby = riskActionDto.orderby;
        this.riskActionGroup = riskActionGroup;
    }

    public getInitialText() {
        return this.initialText;
    }

    public getStatusChangeReasonListName() {
        return null;
    }

    public getActivityTypes() {
        return [] as ActivityType[];
    }

    public getOutcome() {
        return this.riskActionGroup.getOutcome();
    }

    public getActionGroupComponent(): ActionGroupComponent {
        return this.riskActionGroup;
    }

    public getOrderby(): number {
        return this.orderby;
    }
}

/** ActionGroup - a collection of smart step Action's grouped under a heading. */
export class ActionGroup extends ServiceTypeElement implements ActionGroupComponent {
    /** a collection of actions (smart steps) for this group */
    actions: Action[] = [];

    /** the parent outcome */
    outcome: Outcome;

    uuid?: string | undefined;

    constructor(actionGroupDto: ActionGroupDto, outcome: Outcome) {
        // null-check is nice-to-have given super must be first statement
        super(actionGroupDto.id, actionGroupDto.name, actionGroupDto.disabled);

        this.outcome = outcome;
        this.uuid = actionGroupDto.uuid;
        actionGroupDto.actions.forEach(actionDto => {
            this.actions.push(new Action(actionDto, this));
        });
    }

    public override tokenize(tokenize: (source: string) => string[]) {
        return tokenize(this.getName() + " " + this.outcome.getName());
    }

    public getActions() {
        return this.actions;
    }

    public getOutcome() {
        return this.outcome;
    }

    public asDto(): ActionGroupDto {
        return {
            id: this.getId(),
            uuid: this.uuid,
            name: this.getName(),
            actions: this.actions.map(action => action.asDto())
        } as ActionGroupDto;
    }
}

/** Action - an individual action (or smart step) */
export class Action extends ServiceTypeElement implements ActionComponent {
    initialText: string | null;

    /** the parent action group */
    actionGroup: ActionGroup;

    uuid?: string | undefined;

    actionDto: ActionDto;

    /** Related activity types available for selection on this action */
    activityTypes: ActivityType[];

    constructor(actionDto: ActionDto, actionGroup: ActionGroup) {
        // null-check is nice-to-have given super must be first statement
        super(actionDto.id, actionDto.name, actionDto.disabled);

        this.initialText = actionDto.initialText;
        this.actionDto = actionDto;
        this.uuid = actionDto.uuid;
        this.actionGroup = actionGroup;
        this.activityTypes = actionDto.activityTypes;
    }

    public override tokenize(tokenize: (source: string) => string[]) {
        return tokenize(
            this.getName() +
                " " +
                this.actionGroup.getName() +
                " " +
                this.actionGroup.outcome.getName()
        );
    }

    public getStatusChangeReasonListName(): string | null {
        return this.actionDto.statusChangeReasonListName as string | null; // Hack the messy types for now
    }

    public getOutcome() {
        return this.actionGroup.getOutcome();
    }

    public getActivityTypes() {
        return this.activityTypes;
    }

    getInitialText() {
        return this.initialText;
    }

    getOrderby(): number {
        return this.actionDto.orderby;
    }

    public getActionGroupComponent(): ActionGroupComponent {
        return this.actionGroup;
    }

    public asDto(): ActionDto {
        return <ActionDto>{id: this.getId(), uuid: this.uuid, name: this.getName()};
    }
}
