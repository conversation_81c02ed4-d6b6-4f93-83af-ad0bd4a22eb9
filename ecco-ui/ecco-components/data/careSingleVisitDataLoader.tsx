import {generateLoneWorkerUpdate, ROTA_VISIT} from "../care/careVisitReducer";
import {useDirectTaskHandles, useVisitDetail, useWork} from "./entityLoadHooks";
import {CareTask, CareVisitProps, CareVisitState} from "../care/careVisitState";
import {EccoAPI} from "../EccoAPI";
import {useServicesContext} from "../ServicesContext";
import {useAdditionalStaff, AdditionalStaffData} from "../care/AdditionalStaff";
import {EccoDate, EccoDateTime, HateoasResource, LinkDto} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {GoalUpdateCommand, SupportCommentCommand} from "ecco-commands";
import {
    SessionData,
    SupportAction,
    EvidenceDef,
    EvidenceGroup,
    SmartStepStatus,
    SupportWork,
    Individual,
    EventResourceDto,
    ReferralSummaryDto,
    ReferralDto,
    EventAttendee,
    DemandScheduleDto,
    TaskNames,
    frequencyTypeDisplay,
    frequencyDaysDisplay
} from "ecco-dto";
import {useMemo} from "react";
import {DemandScheduleTaskHandleDto, ScheduleData} from "ecco-rota";
import {adjustHrefIfOffline, fromIso8601OrMoment, Link} from "./entityLoaders";

/**
 * Set up the task from the smart steps.
 * If a smart step targetDate is set, then use that, otherwise, if its directly linked to the schedule,
 * use the schedule visit date and time.
 * @param action
 * @param directTaskDue If this is a direct task to the schedule, its due when the schedule is
 * @param sessionData
 */
function actionNotRecordedToCareTask(
    action: SupportAction,
    directTaskDue: EccoDateTime | null,
    sessionData: SessionData
): CareTask {
    const taskTime = action.targetDateTime
        ? EccoDateTime.parseIso8601(action.targetDateTime)
        : directTaskDue;
    return {
        taskInstanceId: action.actionInstanceUuid,
        taskText: action.goalName || sessionData.getAnyActionById(action.actionId).getName(),
        taskDescription: action.goalPlan,
        taskTime,
        listDefId: null,
        actionDefId: action.actionId
    };
}

function actionRecordedToCareTask(
    action: SupportAction,
    directTaskDue: EccoDateTime | null,
    sessionData: SessionData
): CareTask {
    const taskTime = action.targetDateTime
        ? EccoDateTime.parseIso8601(action.targetDateTime)
        : directTaskDue;
    return {
        taskInstanceId: action.actionInstanceUuid,
        taskText: action.goalName || sessionData.getAnyActionById(action.actionId).getName(),
        taskDescription: action.goalPlan,
        taskTime,
        listDefId: action.statusChangeReasonId,
        actionDefId: action.actionId
    };
}

function filterNoTargetDateOrAppropriateForToday(a: SupportAction): boolean {
    const datetime = EccoDateTime.parseIso8601(a.targetDateTime);
    // no target date could be direct tasks (because the appointmentschedule does the scheduling)
    // NB PlannedDateTime is simply recorded in the audit, it doesn't change the smart step data
    if (!datetime) {
        return true;
    }
    const schedule = ScheduleData.fromTargetSchedule(datetime, a.targetSchedule);
    const notFuture =
        !schedule.getStart() || schedule.getStart()!.earlierThanOrEqual(EccoDate.todayLocalTime());
    const notEnded =
        !schedule.getEnd() || schedule.getEnd()!.laterThanOrEqual(EccoDate.todayLocalTime());
    return notFuture && notEnded;
}

interface SortAction {
    sort(a: SupportAction, b: SupportAction): number;
}

/** Sort according to outcomeId, then actionGroupId, then actionId */
class SortByOrderby implements SortAction {
    constructor(private sessionData: SessionData) {}

    public sort(a: SupportAction, b: SupportAction) {
        const outcome = b.outcomeId! - a.outcomeId!;
        if (outcome != 0) return outcome;

        const group = b.actionGroupId! - a.actionGroupId!;
        if (group != 0) return group;

        const bOrder = this.sessionData.getAnyActionById(b.actionId).getOrderby();
        const aOrder = this.sessionData.getAnyActionById(a.actionId).getOrderby();
        return bOrder && aOrder ? bOrder - aOrder : b.actionId - a.actionId;
    }
}

class SortedByDueThen implements SortAction {
    constructor(private thenSort: {sort: (a: SupportAction, b: SupportAction) => number}) {}

    // sortActionsByDueThenDefn
    public sort(a: SupportAction, b: SupportAction) {
        const datetimeA = EccoDateTime.parseIso8601(a.targetDateTime);
        const datetimeB = EccoDateTime.parseIso8601(b.targetDateTime);
        if (datetimeA || datetimeB) {
            return datetimeA && datetimeB ? datetimeA.compare(datetimeB) : datetimeA ? -1 : 1;
        }
        return this.thenSort.sort(a, b);
    }
}

export interface TargetScheduleData {
    schedule: SupportAction;
    referral: ReferralDto;
}

export function attendeeNotCurrentUser(sessionData: SessionData, event: EventResourceDto) {
    const attendeesNotMe = event.attendees?.filter(
        attendee =>
            attendee.calendarIdUserReferenceUri != sessionData.getDto().calendarIdUserReferenceUri
    );
    // return 'shift'
    return attendeesNotMe && attendeesNotMe[0];
}

export function attendeeNotSame(attendee1: EventAttendee, attendee2: EventAttendee) {
    return attendee1.calendarId != attendee2.calendarId;
}

/**
 * NB see attendeeOwner in ServiceRecipientRotaDecorator
 *
 * We need to find the person for the visit out of the attendees.
 *
 * This approach simply finds the owner of the event because events are pulled from the worker's calendar,
 * but the worker is never the owner:
 *      - workers are put on to clients, so the client is the owner
 *          - find the attendee who is the owner
 *      - workers are put on to runs, so the run is the owner
 *          - ignore - these don't come through to render here
 *      - runs are then loaded
 *          - runs are put on clients, so the client is the owner
 *          - so the event.requestedCalendarId is set with the run's calendarId to help identify it as a run
 *              - find the attendee who is the owner (if we have a requestedCalendarId)
 *
 * Combining the above, we can...
 *  - find the attendee who is the event owner (if not requestedCalendarId) or find the attendee who is not the owner (requestedCalendarId)
 *
 * NB Prior to this commit we have found the attendee who is not the worker.
 *   This relied on a viewingAsName parameter, which should come through as a calendarId.
 *   It also needed a tweak where the 'not worker' couldn't decide where a run+client
 *   made no mention of a worker.
 */
function attendeeOwner(_sessionData: SessionData, event: EventResourceDto) {
    //const runCalendarId = (event as EventResourceDtoWithUserCalendar).requestedCalendarId;
    // console.debug(`event: ${event.start} %o`, event as EventResourceDtoWithUserCalendar);
    // console.debug(`runCalendarId: ${runCalendarId}`);
    // console.debug(`viewingAsName: ${viewingAsName}`);

    // basically, return the owner's name
    return event.attendees
        ?.filter(attendee => {
            return attendee.calendarId == event.ownerCalendarId;
            /*
                const isOwner = attendee.calendarId == event.ownerCalendarId;
                const isAttendeeUser = viewingAsName == attendee.name;
                const isAttendeeMe = attendee.calendarId == sessionData.getDto().calendarId;
                console.debug(
                        `attendee: isAttendeeMe ${isAttendeeMe}, isAttendeeAsUser ${isAttendeeUser}, isOwner ${isOwner}, name ${attendee.name}] %o`,
                        attendee
                );
                return isOwner;
                */
        })
        .map(attendee => {
            //console.debug(`attendee eligible: ${attendee.name}`);
            return attendee.name;
        })[0];
}

// see compute for how these are determined
export class EventBackingData {
    public eventId: string | undefined;
    public serviceRecipientId: number | undefined;
    public serviceRecipientPrefix: string | undefined;
    public serviceAllocationId: number | undefined;
    public startTime: EccoDateTime | undefined;
    public eventStatusId: number | undefined;
    public durationMins: number | undefined;
    // public endTime: EccoDateTime;
    public appointmentLocation: string | undefined;
    public resources: HateoasResource | undefined;
    public links: Link[] = [];
    public evidenceWorkUuid: string | undefined;

    public displayName: string | undefined;
    public dateTimeAndDuration: string | undefined;

    // concatenation of the above properties
    public title: string | undefined;
    public subtitle: string | undefined;

    private constructor(public sessionData: SessionData) {}

    public static fromTargetSchedule(sessionData: SessionData, ts: TargetScheduleData) {
        const data = new EventBackingData(sessionData);

        // event specific, not required
        //data.startTime = null
        //data.endTime = null
        //data.eventId = null
        //data.dateTimeAndDuration = null

        data.serviceRecipientId = ts.referral.serviceRecipientId;
        data.serviceRecipientPrefix = "r";
        data.serviceAllocationId = ts.referral.serviceAllocationId;
        data.displayName = ts.referral.displayName;
        data.appointmentLocation = undefined; // TODO building / address
        data.resources = {links: []}; // TODO for risks
        data.links = [];
        data.evidenceWorkUuid = undefined; // TODO how can we know we've been here before? - today's work perhaps against a taskName OR just create an event for that sr?

        data.title = ""; // event.title or eventCategory name
        data.subtitle = data.dateTimeAndDuration; // we don't need a specific duration, but could calc the first targetDate instance

        return data;
    }

    /**
     * @param sessionData
     * @param event
     * @param viewingAsName The name we should be viewing the appointments as. Currently used by CalendarAppointmentsControl)
     * @param includeAllLinks The event's links are passed through - display ones and api ones
     * to show the worker's rota appointments - which is StaffView.tsx and RotaWorkerView.tsx.
     */
    public static fromEvent(
        sessionData: SessionData,
        event: EventResourceDto,
        viewingAsName?: string | undefined,
        includeAllLinks = false
    ) {
        const data = new EventBackingData(sessionData);
        data.compute(sessionData, event, viewingAsName, includeAllLinks);
        return data;
    }

    private compute(
        sessionData: SessionData,
        event: EventResourceDto,
        _viewingAsName?: string | undefined,
        includeAllLinks = false
    ) {
        this.eventId = event.uid;
        this.resources = event;
        this.serviceRecipientId = event.serviceRecipientId;
        this.serviceRecipientPrefix = event.serviceRecipientPrefix;
        this.serviceAllocationId = event.serviceAllocationId;
        this.evidenceWorkUuid = event.evidenceWorkUuid;
        this.eventStatusId = event.eventStatusId;

        this.startTime = fromIso8601OrMoment(event.start);
        const startTime = this.startTime;

        this.displayName = attendeeOwner(this.sessionData, event);

        const showDate = true; //startTime.toEccoDate().earlierThan(EccoDate.todayLocalTime());
        const showTime = !sessionData.isEnabled("rota.scheduler");
        const showDuration = !sessionData.isEnabled("rota.scheduler");

        let whenDescription = "";
        if (showDate && showTime) {
            whenDescription = event.allDay
                ? startTime.toEccoDate().formatShort()
                : startTime.formatShort();
        } else if (showDate && !showTime) {
            whenDescription = startTime.toEccoDate().formatShort();
        } else {
            whenDescription = startTime.formatHoursMinutes();
        }

        if (showDuration && event.end && !event.allDay) {
            const endTime = fromIso8601OrMoment(event.end);
            this.durationMins = endTime.subtractDateTime(startTime, true).inMinutes(); // FIXME: Allow negative here is to stop exception blowing up for all
            whenDescription = `${whenDescription} for ${this.durationMins.toString()} mins`;
        }

        this.dateTimeAndDuration = whenDescription;

        // location comes directly from EventResource
        this.appointmentLocation = event.location ? `${event.location}` : "";

        this.calculateUiLinks(event);
        if (includeAllLinks) {
            const apiLinks = event.links
                .filter(link => link.href.indexOf("/api/") > -1)
                .map(link => new Link(link.rel, link.href));
            this.links = this.links.concat(apiLinks);
        }

        const atLocation = this.appointmentLocation && ` at ${this.appointmentLocation}`;
        // team -> events (project calendar picks up on those with event category)
        if (event.eventCategoryId) {
            // the calendar report comes via 'calendarEventsFromReferrals' which sets the referral as 'parent'
            // so we can see if the object has a parent to just allow more display options
            // the calendar displays the title only, but we should display their name
            const parentExists = event.hasOwnProperty("parent");
            if (parentExists) {
                // @ts-ignore
                const referral = event["parent"].referral as ReferralSummaryDto;
                this.title = referral.displayName;
                const categoryName = this.sessionData
                    .getListDefinitionEntryById(event.eventCategoryId)
                    .getDisplayName();
                this.subtitle = `${categoryName}: ${event.title} at ${this.dateTimeAndDuration} ${atLocation}`;
            } else {
                this.title = this.sessionData
                    .getListDefinitionEntryById(event.eventCategoryId)
                    .getDisplayName();
                this.subtitle = `${this.dateTimeAndDuration} ${atLocation}`;
            }
        } else {
            // NB location box is used as the description on ad-hoc entries!
            this.title = event.title;
            this.subtitle =
                this.dateTimeAndDuration +
                (this.displayName ? ` with ${this.displayName} ${atLocation}` : "");
        }
    }

    private calculateUiLinks(event: EventResourceDto) {
        this.links = event.links
            .filter(link => link.href.indexOf("/api/") < 0) // For now filter out API items as we don't handle them
            .map(link => {
                // NB 'edit' seems to imply this.isReferral() - but could mean any svcRec?
                const linkText = link.rel == "edit" ? "open referral" : link.rel;
                const href = adjustHrefIfOffline(link.href);
                const url = new URL(href, location.href);
                url.searchParams.append("eventId", event.uid);
                return new Link(linkText, url.href);
            });
    }

    public isReferral() {
        return this.serviceRecipientPrefix == "r";
        //return this.links.some(link => link.text == "edit");
    }
}

/**
 * Converts data (EventBackingData) into a visit for an app. EventBackingData is generated from various scenarios:
 *  - events, including demand schedules (care/router (or WelcomeAppBar) -> CarePage -> CareOrEventSource -> EventListSource -> CareShiftEventCard (maybe) -> CareOrEventCard -> transformToCareVisit)
 *      NB the 'tasks' in a care visit can be from directTasks or targetSchedules (determined by "rota.directTasks" below)
 *  - targetSchedule smart steps (WelcomeAppBar -> DailyChecksPage -> TargetScheduleSource in targetScheduleDataLoader.tsx -> transformToCareVisit)
 *      NB the checks are determined from live referrals, loading all the targetSchedules
 * @param data common data from different sources to make into a 'visit'
 * @param hasExistingWorkUuid if a 'visit' has already had data recorded
 * @param workUuid the 'visit' uuid
 * @param work the 'visit' data so far
 * @param additionalStaff any additional staff as part of this 'visit'
 * @param directTasks a schedule can have tasks (actionInstanceUuid's) directly associated with them
 * @param startDateTimeStr start of the 'visit'
 * @param eccoAPI
 * @param supportWorker
 * @param schedule
 */
export function transformToCareVisit(
    data: EventBackingData,
    hasExistingWorkUuid: boolean,
    workUuid: string,
    work: SupportWork | null | undefined,
    supportWorker: Individual | null | undefined,
    schedule: DemandScheduleDto | undefined,
    additionalStaff: AdditionalStaffData | undefined,
    directTasks: DemandScheduleTaskHandleDto[] | undefined,
    startDateTimeStr: string,
    eccoAPI: EccoAPI
): CareVisitProps {
    const workLoad = (): Promise<SupportWork | null> =>
        hasExistingWorkUuid
            ? eccoAPI.supportWorkRepository.findOneSupportWorkByWorkUuid(
                  data.serviceRecipientId!,
                  EvidenceGroup.needs,
                  workUuid
              )
            : Promise.resolve<SupportWork | null>(null);

    const startDateTime = EccoDateTime.parseIso8601(startDateTimeStr);

    const sorterOrderby = new SortByOrderby(data.sessionData);
    const sorterDueFirst = new SortedByDueThen(sorterOrderby);

    // these 'tasks' aren't offline currently
    const tasksLoad = (): Promise<CareTask[]> => {
        const workItemQ = workLoad();
        const latestQ =
            eccoAPI.supportSmartStepsSnapshotRepository.findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroupAtTime(
                data.serviceRecipientId!,
                EvidenceGroup.needs.name,
                startDateTime
            );

        const onlyUseDirectTasks = data.sessionData.isEnabled("rota.directTasks");
        const sorter = onlyUseDirectTasks ? sorterOrderby : sorterDueFirst;

        return Promise.all([workItemQ, latestQ]).then(([workItem, latest]) => {
            // only include children (with a parent), or instances without children
            let latestActions = latest.latestActions
                .filter(
                    a =>
                        a.parentActionInstanceUuid ||
                        !latest.latestActions.find(
                            la => la.parentActionInstanceUuid == a.actionInstanceUuid
                        )
                )
                .sort((a, b) => sorter.sort(a, b))
                .filter(filterNoTargetDateOrAppropriateForToday);

            // if using directTasks then only use them for now - as we've got other evidence page smartsteps leaking through
            if (onlyUseDirectTasks) {
                const directTaskIds = directTasks?.map(t => t.taskInstanceId) || [];
                latestActions = latestActions.filter(a =>
                    directTaskIds.some(dt => dt == a.actionInstanceUuid)
                );
                // if not directTasks then remove unplanned tasks - so only include those with a targetDate or status reason
                // NB prior to 95b6ec7c (DEV-2277 Finish forces not-completed reasons/msg), filtering out unplanned smart steps
                // was a case of checking that a target date existed or status reason existed (the bit removed with this commit, introduced in 381f9d05 'only if not saved' test)
                // BUT should perhaps have been where status reason == null (not != null) since a status reason existing implied completed?
                //      NB since 95b6ec7c this would now be status reason != success
                // AND it's a little confusing in that if there was a targetSchedule then a targetDate would be set anyway (and therefore a taskTime?)
                // EXCEPT that a direct task could have a status reason with a blank targetSchedule, but still it would be reason == null for incomplete?
                // SO if we wanted the idea back of 'only if not saved', then we could filter the latestActions accordingly
                //      NB see what is saved via 'taskUpdateCmd' in this file - triggered in careVisitReducer
                //      NB careVisitReducer passes taskTime (visit time) into the audit as plannedDateTime, not as targetDate
            } else {
                const config = data.sessionData.getServiceTypeByServiceCategorisationId(
                    data.serviceAllocationId!
                );
                const careActionIds = config
                    .getActionsForTaskName(TaskNames.carePlan, data.sessionData)
                    .map(a => a.getId());

                latestActions = latestActions
                    .filter(a => careActionIds.indexOf(a.actionId) > -1)
                    // NB previous filter was for taskTime (targetDateTime) or some listDefId (statusChangeReasonId != null)
                    // but in current code, the idea of 'DEV-2279 Only show due/planned tasks' (see 381f9d05) only showing due tasks
                    // based on no targetDateTime but a statusChangeReasonId is not relevant when !onlyUseDirectTasks
                    //      .filter(t => onlyPlannedTasks ? (!!t.taskTime || t.listDefId != null) : true)
                    .filter(a => !!a.targetDateTime);
                // NB previous sort already actioned in 'sorter' above
                //      .sort(sortTasksByDueThenDefn)
            }

            const tasks: CareTask[] = workItem
                ? latestActions
                      // replace incomplete items with completed ones if we have work in progress already
                      .map(a => {
                          // find if this action is a direct task - in which case we set the due as the same time as the visit
                          const directTask =
                              directTasks?.filter(t => t.taskInstanceId == a.actionInstanceUuid)
                                  ?.length == 1;
                          const directTaskDateTime = directTask ? startDateTime : null;
                          let recorded = workItem.actions.find(
                              wa => wa.actionInstanceUuid == a.actionInstanceUuid
                          );
                          return recorded
                              ? actionRecordedToCareTask(
                                    recorded,
                                    directTaskDateTime,
                                    eccoAPI.sessionData
                                )
                              : actionNotRecordedToCareTask(
                                    a,
                                    directTaskDateTime,
                                    eccoAPI.sessionData
                                );
                      })
                : latestActions.map(it => {
                      const directTask =
                          directTasks?.filter(t => t.taskInstanceId == it.actionInstanceUuid)
                              ?.length == 1;
                      const directTaskDateTime = directTask ? startDateTime : null;
                      return actionNotRecordedToCareTask(
                          it,
                          directTaskDateTime,
                          eccoAPI.sessionData
                      );
                  });

            return tasks;
        });
    };

    // would be good to automate based on Partial changes - perhaps like UserForm by using a Reducer to update properties
    // perhaps some property: keyof CareTask and value: <type> via https://stackoverflow.com/questions/45894524/getting-type-of-a-property-of-a-typescript-class-using-keyof-operator
    const taskUpdateCmd = (
        task: CareTask,
        outcomeId: number | null,
        plannedDateTime: EccoDateTime | null
    ) => {
        return new GoalUpdateCommand(
            "update",
            Uuid.randomV4(),
            Uuid.parse(workUuid),
            data.serviceRecipientId!,
            ROTA_VISIT,
            task.actionDefId,
            EvidenceGroup.needs,
            Uuid.parse(task.taskInstanceId),
            undefined
        )
            .withEventId(data.eventId!)
            .changeStatus(null, SmartStepStatus.AchievedAndStillRelevant)
            .setForceStatusChange()
            .changeStatusChangeReason(task.listDefId, outcomeId)
            .setPlannedDateTime(plannedDateTime);
    };

    const commentUpdateCmd = (
        state: CareVisitState & {timerStartedAt: EccoDateTime; timerStoppedAt: EccoDateTime}
    ) => {
        const evidenceDef = EvidenceDef.fromTaskName(data.sessionData, null, ROTA_VISIT);
        return SupportCommentCommand.create(
            false,
            Uuid.parse(workUuid),
            data.serviceRecipientId!,
            evidenceDef.getEvidenceGroup(),
            ROTA_VISIT
        )
            .changeComment(null, state.commentForm.comment)
            .changeWorkDate(null, state.timerStartedAt.formatIso8601())
            .changeMinsSpent(
                0,
                Math.floor(state.timerStoppedAt.subtractDateTime(state.timerStartedAt).inMinutes())
            )
            .build();
    };

    const previousVisitHistoryLoad = (page: number) =>
        eccoAPI.serviceRecipientRepository.findServiceRecipientTaskCommandsByCreated(
            data.serviceRecipientId!,
            page,
            ROTA_VISIT
        );

    // could use Partial<CareVisitProps> and use a helper function to combine into CareVisitProps - see https://stackoverflow.com/questions/51275395/how-to-merge-2-partials-to-a-complete-object-in-typescript
    return {
        stopOptions: {},
        stopOptionsOn: false,
        displayName: data.displayName,
        address: data.appointmentLocation,
        // bizarrely, aptTypeId is the name of the categoryId (the appointment type) - see commit
        // see scheduleDataLoader aptTypeName
        visitType: schedule?.title,
        aptTypeId: schedule?.categoryId,
        adHoc: schedule?.adHoc,
        visitDateTime: data.dateTimeAndDuration,
        plannedDateTime: data.startTime,
        plannedMins: data.durationMins,
        commentForm: {},
        locationId: null,
        serviceRecipientId: data.serviceRecipientId!,

        supportWorker: supportWorker && `${supportWorker?.firstName} ${supportWorker?.lastName}`,
        // "every 2 weeks on Mon,Tues"
        scheduleInfo:
            schedule &&
            `${frequencyTypeDisplay(
                schedule.intervalType,
                schedule.intervalFrequency
            )} ${frequencyDaysDisplay(schedule.intervalType, schedule.calendarDays)}`,
        taskSummary: schedule?.parameters?.tasks || "",
        resources: data.resources,
        eventStatusId: data.eventStatusId,
        work: work as SupportWork | null,
        additionalStaff,
        tasks: null,
        workLoad,
        tasksLoad,
        previousVisitHistoryLoad,
        taskUpdateCmd,
        commentUpdateCmd,
        loneWorkerUpdate: () => Promise.reject()
    };
}

/**
 * REFERRAL (visit based): For showing the tasks to do today.
 * This is exactly the EventListSource data we're currently getting for existing rota visits/offline
 * but this provides the app-approach.
 */
interface CareEventProps {
    showDate?: boolean | undefined;
    event: EventResourceDto;
}

class CardAsHateoas implements HateoasResource {
    constructor(public links: LinkDto[]) {}
}

/**
 * Load the information for a rota appointment.
 * This includes loading:
 *      - associated work item
 *      - current eventStatus (lone worker for start/stop timer)
 *      - additionalStaff on the event
 *      - directTaskHandles for the tasks to show/complete on the visit
 *      - visitDetail for extra info such as key worker and schedule info
 * @param event The event to load the appointment details
 */
export function useCareOrEventCard(event: EventResourceDto, showEventAsVisit: boolean) {
    const eccoAPI = useServicesContext();
    const data = useMemo(
        () => EventBackingData.fromEvent(eccoAPI.sessionData, event, undefined, true),
        [event]
    );

    // as per EventCard - link.text == "rota visit" comes from ServiceRecipientEventDecorator
    const rotaVisit = data.links.some(link => link.href.indexOf(ROTA_VISIT) > -1);
    // if we are not a rota visit (and not an event that wants to be) then return no data
    if (!rotaVisit && !showEventAsVisit) {
        return {loaded: true, data: null};
    }

    // hook is after a condition, except the condition never changes
    return useCareCard(event!, data);
}

function useCareCard(event: EventResourceDto, data: EventBackingData) {
    const eccoAPI = useServicesContext();

    // workUuid is always available to generateLoneWorkerUpdate
    const workUuid = useMemo(
        () => (data.evidenceWorkUuid ? data.evidenceWorkUuid : Uuid.randomV4().toString()),
        [data]
    );
    const work = useWork(data.serviceRecipientId!, EvidenceGroup.needs, workUuid)?.work;
    const loneWorkerUpdate = useMemo(
        () =>
            generateLoneWorkerUpdate(
                eccoAPI.sessionData,
                data.serviceRecipientId!,
                data.eventId!,
                workUuid
            ),
        []
    );

    // get the additionalStaff, after getting the card's proper hateoas links
    const cardLinks = data.links.map(l => {
        const link: LinkDto<any> = {rel: l.text, href: l.href};
        return link;
    });
    const cardAsHateoas = new CardAsHateoas(cardLinks);
    const {additionalStaff, loading: additionalStaffLoading} = useAdditionalStaff(
        cardAsHateoas,
        data.eventId!
    );

    // load the task handles directly associated with the schedule
    // NB we could use this to load the task data themselves server-side (just be careful to include all unassigned also, for ad-hoc)
    const {directTaskHandles, loading: directTaskHandlesLoading} =
        useDirectTaskHandles(cardAsHateoas);

    // could be a feature toggle if required - careapp.extraDetail
    const skipDetail = false;
    const {visitDetail, loading: visitDetailLoading} = useVisitDetail(
        data.serviceRecipientPrefix!,
        data.serviceRecipientId!,
        cardAsHateoas,
        !skipDetail
    );

    if (
        (data.evidenceWorkUuid && !work) ||
        additionalStaffLoading ||
        directTaskHandlesLoading ||
        visitDetailLoading
    ) {
        return {loaded: false, data: null};
    }

    const initVisitProps = transformToCareVisit(
        data,
        !!data.evidenceWorkUuid,
        workUuid,
        work,
        visitDetail?.contact,
        visitDetail?.schedule,
        additionalStaff,
        directTaskHandles,
        event.start,
        eccoAPI
    );

    const visitProps: CareVisitProps = {
        ...initVisitProps,
        // we make the callbacks part of the props - they could have been explicit argument but just seems unnecessary plumbing
        // would have been nice perhaps as part of CareVisitSetupProps, but React's Provider/Reducer signatures mean it would need to be in the same object anyway
        loneWorkerUpdate
    };

    return {loaded: true, data: visitProps};
}
