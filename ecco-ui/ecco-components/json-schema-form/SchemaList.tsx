import {implementInterface, isJsonSchemaDto, JsonSchemaDto, ObjectSchemaDto, ResourceList} from "ecco-dto";
import * as React from "react";
import {createContext, FC, ReactNode, useContext} from "react";
import {HateoasResource, StringToObjectMap} from "@eccosolutions/ecco-common";
import {usePromise} from "../data/entityLoadHooks";
import {useServicesContext} from "../ServicesContext";
import {SchemaDataTable} from "./SchemaDataTable";
import {createMuiTheme, ThemeProvider} from "@eccosolutions/ecco-mui";
import {stringifyPossibleError} from "ecco-offline-data";
import {ReloadableContext} from "../data/serviceRecipientHooks";

export interface SchemaProps {
    title?: string | undefined;

    /** relative URL (including query string if a GET) of the request we want to show the results of */
    src: string;

    /** Method to use if not GET */
    method?: "POST" | undefined;

    /** Content to POST as J<PERSON><PERSON> in the request body */
    body?: object | undefined;

    /** Array of field names for columns to show by default (default: display all) */
    displayFields?: string[] | undefined;

    /** Field names to allow filter on (default: all) */
    filterFields?: string[] | undefined;

    /** Map of values to filters to apply for each field */
    filters: StringToObjectMap<string[]>;

    /** Callback for change to filtering */
    onFilter: (queryParams: StringToObjectMap<string[]>) => void;

    /** Which page to show */
    page: number;

    /** Load a different page of data */
    onPage: (page: number) => void;

    /** Handle search text box - called with null when dismissed */
    onSearch?: ((searchText: string | null) => void) | undefined;

    /** Pass (back) in searchText - i.e. controlled component */
    searchText?: string | null | undefined;

    /** Callback to turn links into element(s) for actions (e.g. menu or buttons) */
    actionRenderer?: ((dataRow: HateoasResource) => ReactNode) | undefined;

    /** Allow row click to be turned into an action based on a link (e.g. rel="edit" or rel="self") */
    onRowClick?: ((resource: HateoasResource) => void) | undefined;
}

type SchemaListResult = {schema: ObjectSchemaDto; data?: HateoasResource | undefined};

export const SchemaListContext = createContext<ReloadableContext<
    SchemaListResult | undefined
> | null>(null);

// See SchemaDrivenComponent.loadSrc
function useSchema(props: SchemaProps & {children?: React.ReactNode | undefined}) {
    const {apiClient} = useServicesContext();
    const dataRequest = () =>
        apiClient
            .get<JsonSchemaDto | ResourceList<JsonSchemaDto>>(props.src)
            .then(resource => {
                if (isJsonSchemaDto(resource)) {
                    return {schema: implementInterface(resource).asObjectSchema()};
                }
                const describedby = apiClient.fetchRelation<JsonSchemaDto>(resource, "describedby");
                return describedby.then(schema => ({
                    data: resource,
                    schema: implementInterface(schema).asObjectSchema()
                }));
            })
            .then(response => {
                // Only fulfill this promise when we've got the extended schemas
                return response.schema.loaded
                    ? response.schema.loaded.then(() => response)
                    : response;
            });
    return usePromise<SchemaListResult>(dataRequest, [props.src]);
}

/** Resolve SchemaListContext that has already been loaded by <SchemaList> */
export function useCurrentSchemaListContext() {
    // Force non-null as we should use within <SchemaList> as noted above
    const context = useContext(SchemaListContext);
    if (!context) throw new Error("Developer error: No SchemaListContext loaded. Please report");
    return context;
}

export const SchemaList: FC<SchemaProps> = props => {
    const {error, resolved, reload} = useSchema(props);
    return (
        <ThemeProvider theme={() => createMuiTheme()}>
            <SchemaListContext.Provider value={{resolved, reload}}>
                <SchemaDataTable
                    schema={resolved ? resolved.schema : null}
                    resourceList={
                        resolved ? (resolved.data! as ResourceList<HateoasResource>) : null
                    }
                    /*actionCols={}*/
                    {...props}
                />
            </SchemaListContext.Provider>
            {error && stringifyPossibleError(error)}
        </ThemeProvider>
    );
};

export default SchemaList;