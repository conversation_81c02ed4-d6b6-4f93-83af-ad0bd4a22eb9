import * as React from "react";
import {Dispatch, SetStateAction, useEffect, useState} from "react";
import {TaskDto} from "ecco-dto/workflow-dto";
import {useWorkflow, WorkflowActiveRecord, WorkflowReloadEvent} from "./WorkflowLoader";
import {CommandForm} from "../cmd-queue/CommandForm";
import {TaskList} from "./TaskList";
import {LoadingOrError} from "../Loading";
import {
    ServiceRecipientWithEntitiesContext,
    useServiceRecipientWithEntities
} from "../data/serviceRecipientHooks";
import {useServicesContext} from "../ServicesContext";
import {useServiceRecipientAuditAccess} from "../data/entityLoadHooks";
import {SourceAudit} from "ecco-dto";

export interface TasksControlProps {
    srId: number;
    limitToTaskNames?: string[] | undefined;
    initialActiveTaskName?: string | undefined;
}

export interface PropsNG extends TasksControlProps {}

function useAccessAuditWrapper(
    srContext: ServiceRecipientWithEntitiesContext | undefined,
    activeTask: TaskDto | null
) {
    // react-router useLocation requires <BrowserRouter>
    // but we don't need it - we're expecting an actual address bar url
    const query = window.location.search;
    //const location = useLocation();
    const source = new URLSearchParams(query).get("source") as SourceAudit;

    const accessAudit =
        activeTask && srContext
            ? srContext.serviceType
                  .getTaskDefinitionEntry(activeTask.taskName)
                  ?.hasSetting("accessAudit", "y")
            : null;

    useServiceRecipientAuditAccess(
        accessAudit ? srContext?.serviceRecipient.serviceRecipientId : undefined,
        activeTask?.taskName,
        source
    );

}


export function TasksControlNG(props: PropsNG) {
    const {tasksRepository} = useServicesContext();
    const {srId} = props;
    const {handleTaskClick, formForTask, registerIntegrationHooks} =
        useServicesContext().taskIntegrations;

    /** Which task is active for editing */
    const [activeTask, setActiveTask] = useState<TaskDto | null>(null);

    const {context: srContext, error, reload} = useServiceRecipientWithEntities(srId);

    const {workflow, error: error2} = useWorkflow(srId);

    useEffect(() => {
        if (props.initialActiveTaskName) {
            // when on welcome/myteam/tasks
            // when on main/sr2/tasks
            // but if not a pathway item then no activeTask is set
            // eg /tasks/emergencyDetails needs to be on config for this (can be hidden?, but config useful to tick audits etc)
            const task = workflow?.tasks.find(t => t.taskName === props.initialActiveTaskName);
            if (task) setActiveTask(task);
        }
    }, [workflow]);

    useAccessAuditWrapper(srContext, activeTask);

    function submit(
        _workflow: WorkflowActiveRecord,
        _activeTask: TaskDto,
        setActiveTask: Dispatch<SetStateAction<TaskDto | null>>
    ) {
        setActiveTask(null);
    }

    registerIntegrationHooks();
    if (!workflow || !srContext) return <LoadingOrError error={error || error2} />;

    return (
        <div key="tasks-control">
            {activeTask ? (
                <CommandForm
                    onCancel={() => setActiveTask(null)}
                    onFinished={() =>
                        submit(
                            new WorkflowActiveRecord(
                                workflow,
                                () => {
                                    WorkflowReloadEvent.bus.fire();
                                    reload();
                                },
                                tasksRepository
                            ),
                            activeTask,
                            setActiveTask
                        )
                    }
                >
                    {
                        // TODO: onCancelled too
                        formForTask(srContext!, activeTask, () => {
                            setActiveTask(null);
                            reload();
                        })
                    }
                </CommandForm>
            ) : (
                <TaskList
                    srId={props.srId}
                    workflow={workflow}
                    onClick={(task: TaskDto) =>
                        handleTaskClick(props.srId, task, srContext!, setActiveTask)
                    }
                    limitToTaskNames={props.limitToTaskNames}
                    srContext={srContext!}
                    taskRepository={tasksRepository}
                />
            )}
        </div>
    );
}
