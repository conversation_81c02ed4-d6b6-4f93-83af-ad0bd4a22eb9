import {EccoDate, EccoTime, Nullable} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    AppointmentScheduleDirectTaskCommandDto,
    CommandQueue,
    ServiceRecipientAppointmentScheduleCommand
} from "ecco-commands";
import {Agreement, DemandScheduleTaskDto} from "ecco-rota";
import * as React from "react";
import {FC, ReactElement} from "react";
import {CommandForm, CommandSubform, ModalCommandForm} from "../cmd-queue/CommandForm";
import {useServicesContext} from "../ServicesContext";
import {AppointmentSchedule} from "./AppointmentSchedule";
import {
    useAgreement,
    useDemandSchedule,
    useDirectTasks,
    useServiceRecipient
} from "../data/entityLoadHooks";
import {asStringChange, DemandScheduleDto, EvidenceGroup, RateCardDto, SessionData} from "ecco-dto";
import {AppointmentScheduleTasks, DemandScheduleTaskDtoDisable} from "./AppointmentScheduleTasks";
import {WorkerJobSelector} from "../rota/WorkerJobSelector";
import {ScheduleEvent} from "./AppointmentForm";

type LocalProps = {sessionData: SessionData; commandForm: CommandForm};

interface PropsEdit {
    serviceRecipientId: number;
    serviceAllocationId: number | null; // to determine the carers list
    agreement: Agreement;
    schedule?: DemandScheduleDto | undefined; // absent on new schedule
    tasksDirect?: DemandScheduleTaskDto[] | null | undefined;
    editingScheduleRateCards?: RateCardDto[] | undefined;
}

interface Props extends PropsEdit {
    readOnly: boolean;
}

interface State {
    schedule: DemandScheduleDto;
    tasks: DemandScheduleTaskDtoDisable[];
    /* 1 - 4ish*/
    numStaff: number;
    resourceSrId: number | null;
}

export class Schedule extends CommandSubform<Props & LocalProps, State> {
    public static showInModalEditSchedule(
        serviceRecipientId: number,
        agreementId: number,
        scheduleId: number
    ): ReactElement {
        return (
            <EditScheduleModalWrapper
                serviceRecipientId={serviceRecipientId}
                agreementId={agreementId}
                scheduleId={scheduleId}
                setShow={() => {}}
            />
        );
    }

    constructor(props: Props & LocalProps) {
        super(props);

        // should really call removePostSubmitHandler - like useCommandFormPostSubmitHandler
        props.commandForm.addPostSubmitHandler(() => ScheduleEvent.fire());

        const tasksCopyWithDelete = (props.tasksDirect ? [...props.tasksDirect] : []).map(t => {
            const tasksCopy: DemandScheduleTaskDtoDisable = {...t, delete: false};
            return tasksCopy;
        });
        this.state = {
            schedule: props.schedule || ({} as DemandScheduleDto),
            tasks: tasksCopyWithDelete,
            numStaff: Schedule.numStaffIn(props),
            resourceSrId: null
        };
    }

    private static numStaffIn(props: Props): number {
        return props.schedule ? props.schedule.childScheduleIds.length + 1 : 1;
    }

    getErrors(): string[] {
        const errors = [];
        const schedule = this.state.schedule;
        // NOTE: Done in order they appear on the form
        if (!schedule.categoryId) {
            errors.push("type is required");
        }
        if (!schedule.start) {
            // FIXME: start is set when editing a schedule, so we need to check applicable from when we have eventRef
            errors.push("start date is required");
        }
        if (schedule.eventRef && !schedule.applicableFrom && this.isSplit()) {
            errors.push("you must set 'applicable from' for all changes except end date");
        }
        if (!schedule.time) {
            errors.push("you must specify a time for the appointment");
        }
        if (!schedule.durationMins || schedule.durationMins < 5) {
            errors.push("duration should be at least 5 minutes");
        }
        if (
            (!schedule.intervalType || schedule.intervalType == "WK") &&
            !schedule.calendarDays?.length
        ) {
            // FIXME: Validate that at least one day is between start and end date (or applicableFrom and end)
            errors.push("you must select at least one day");
        }
        if (schedule.intervalFrequency < 1 || schedule.intervalFrequency > 4) {
            errors.push("every 1-4 weeks");
        }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const scheduleIn = this.props.schedule || ({} as DemandScheduleDto);
        const schedule = this.state.schedule;

        const eventRef = scheduleIn && scheduleIn.eventRef;
        const op = !eventRef ? "add" : this.isSplit() ? "split" : "update";
        const cmd = new ServiceRecipientAppointmentScheduleCommand(
            op,
            Uuid.randomV4(),
            this.props.serviceRecipientId,
            eventRef,
            this.props.agreement.getAgreementId()
        )
            // see CreateAppointmentForm and EditAppointmentForm
            .changeDurationMins(scheduleIn.durationMins, schedule.durationMins)
            .changeAppointmentTypeId(scheduleIn.categoryId, schedule.categoryId)
            .changeIntervalType(scheduleIn.intervalType, schedule.intervalType)
            .changeEndDate(
                EccoDate.parseIso8601(scheduleIn.end),
                EccoDate.parseIso8601(schedule.end)
            )
            .changeTime(
                EccoTime.parseIso8601(scheduleIn.time),
                EccoTime.parseIso8601(schedule.time)
            )
            .changeRateCardId(scheduleIn.rateCardId, schedule.rateCardId);

        if (schedule.intervalType == "WK") {
            cmd.changeIntervalFrequency(scheduleIn.intervalFrequency, schedule.intervalFrequency);
        }

        if (this.state.resourceSrId) {
            cmd.resourceSrId = this.state.resourceSrId;
        }

        if (op != "update") {
            // if not update (ie a new/split), allow staff change
            // NB this property means 'number of staff' really
            cmd.withAdditionalStaff(this.state.numStaff - 1);
        }
        if (op == "split") {
            cmd.withApplicableFromDate(EccoDate.parseIso8601(schedule.applicableFrom!));
        } else {
            cmd.changeStartDate(
                EccoDate.parseIso8601(scheduleIn.start),
                EccoDate.parseIso8601(schedule.start)
            );
        }
        const daysFrom = scheduleIn.calendarDays;
        const daysTo = schedule.calendarDays;
        cmd.changeDays(daysFrom, daysTo);
        cmd.changeTasks(scheduleIn.parameters?.tasks, schedule.parameters?.tasks);

        const tasksDirect = this.emitTaskCommands();
        if (tasksDirect) {
            cmd.setDirectTasks(tasksDirect);
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    private emitTaskCommands(): AppointmentScheduleDirectTaskCommandDto[] | null {
        const origTaskIds = (this.props.tasksDirect || []).map(t => t.taskInstanceId);
        const currentTaskIds = this.state.tasks.map(t => t.taskInstanceId);

        const tasksNew = currentTaskIds.filter(c => !origTaskIds.find(o => o == c));
        const tasksChangedMaybe = currentTaskIds.filter(c => origTaskIds.find(o => o == c));

        const cmds: AppointmentScheduleDirectTaskCommandDto[] = [];

        tasksNew.map(t => {
            const task = this.state.tasks.find(o => o.taskInstanceId == t)!;
            // don't add transient new/deleted
            if (!task.delete) {
                const cmd: AppointmentScheduleDirectTaskCommandDto = {
                    operation: "add",
                    taskDefId: task.taskDefId,
                    taskInstanceId: task.taskInstanceId,
                    taskText: asStringChange(null, task.taskText),
                    taskDescription: asStringChange(null, task.taskDescription)
                };
                cmds.push(cmd);
            }
        });

        if (tasksChangedMaybe) {
            tasksChangedMaybe.forEach(t => {
                const a = this.props.tasksDirect!.find(o => o.taskInstanceId == t)!;
                const b = this.state.tasks.find(o => o.taskInstanceId == t)!;

                if (b.delete) {
                    const cmd: AppointmentScheduleDirectTaskCommandDto = {
                        operation: "remove",
                        taskInstanceId: a.taskInstanceId
                    };
                    cmds.push(cmd);
                } else {
                    const diffText = a.taskText != b.taskText;
                    const diffDesc = a.taskDescription != b.taskDescription;
                    if (diffText || diffDesc) {
                        const cmd: AppointmentScheduleDirectTaskCommandDto = {
                            operation: "update",
                            taskInstanceId: a.taskInstanceId,
                            taskText: asStringChange(a?.taskText, b?.taskText),
                            taskDescription: asStringChange(a?.taskDescription, b?.taskDescription)
                        };
                        cmds.push(cmd);
                    }
                }
            });
        }

        return cmds.length == 0 ? null : cmds;
    }

    private updateSchedule = (schedule: Partial<Nullable<DemandScheduleDto>>) => {
        this.setState({schedule: schedule as DemandScheduleDto});
    };
    private updateStaff = (numStaff: number) => {
        this.setState({numStaff});
    };

    private isSplit() {
        // if we are allocated, it's not an update - as the schedule is already set
        // we could instead change the cmd to be a rota action allocate
        if (this.state.resourceSrId) {
            return true;
        }

        // 'type' (eg one-to-one) is categoryId
        const propsCausingSplit: (keyof DemandScheduleDto)[] = [
            "durationMins",
            "time",
            "intervalType",
            "intervalFrequency",
            "categoryId",
            "calendarDays",
            "rateCardId"
        ];
        if (this.props.schedule) {
            for (const k of propsCausingSplit) {
                if (this.props.schedule[k] != this.state.schedule[k]) {
                    return true;
                }
            }

            /*
            // 'tasks' description
            if (this.props.schedule.parameters?.tasks != this.state.schedule.parameters?.tasks) {
                return true;
            }

            // 'tasks' items
            const taskCommand = this.emitTaskCommands();
            if (taskCommand != null) {
                return true;
            }
            */

            // 'staff'
            if (Schedule.numStaffIn(this.props) != this.state.numStaff) {
                return true;
            }
        }
        return false;
    }

    private updateTask = (task: DemandScheduleTaskDtoDisable) => {
        const currTasks = this.state.tasks;
        let match = -1;
        currTasks.filter((t, i) => {
            if (t.taskInstanceId == task.taskInstanceId) {
                match = i;
            }
        });
        if (match > -1) {
            currTasks.splice(match, 1, task);
        } else {
            currTasks.push(task);
        }
        this.setState({...this.state, tasks: currTasks});
    };

    override render() {
        return (
            <>
                <AppointmentSchedule
                    agreement={this.props.agreement}
                    schedule={this.state.schedule}
                    numStaff={this.state.numStaff}
                    editingScheduleRateCards={this.props.editingScheduleRateCards}
                    isSplit={this.isSplit()}
                    onChangeSchedule={this.updateSchedule}
                    onChangeStaff={this.updateStaff}
                />
                {this.props.sessionData.isEnabled("rota.scheduler") && (
                    <WorkerJobSelector
                        labelName={"allocate"}
                        role={"ROLE_CARER"}
                        serviceIds={
                            this.props.serviceAllocationId
                                ? [
                                      this.props.sessionData.getServiceCategorisation(
                                          this.props.serviceAllocationId
                                      ).serviceId
                                  ]
                                : null
                        }
                        onChange={(
                            _contactId,
                            _contactName,
                            _calendarId,
                            _workerJobId,
                            serviceRecipientId
                        ) => this.setState({resourceSrId: serviceRecipientId})}
                        selectedSrId={this.state.resourceSrId}
                    />
                )}
                {this.props.sessionData.isEnabled("rota.directTasks") && (
                    <AppointmentScheduleTasks
                        serviceRecipientId={this.props.serviceRecipientId}
                        tasks={this.state.tasks}
                        taskUpdate={this.updateTask}
                    />
                )}
            </>
        );
    }
}

interface IdProps {
    serviceRecipientId: number;
    agreementId: number;
    scheduleId: number;
    editingScheduleRateCards?: RateCardDto[] | undefined;
}
interface ModalProps {
    setShow: (show: boolean) => void;
}

export function useScheduleData(
    serviceRecipientId: number,
    agreementId: number,
    scheduleId: number
) {
    const {agreement} = useAgreement(agreementId);
    const {schedule} = useDemandSchedule(agreementId, scheduleId);
    const {directTasks} = useDirectTasks(schedule, serviceRecipientId, EvidenceGroup.needs);
    const {serviceRecipient} = useServiceRecipient(serviceRecipientId);
    const serviceAllocationId = serviceRecipient?.serviceAllocationId;

    const loaded = agreement && schedule && directTasks && serviceRecipient !== undefined;
    return {loaded, agreement, schedule, directTasks, serviceAllocationId};
}

export const ScheduleFromIds: FC<IdProps & {form: CommandForm}> = props => {
    const {loaded, agreement, schedule, directTasks, serviceAllocationId} = useScheduleData(
        props.serviceRecipientId,
        props.agreementId,
        props.scheduleId
    );
    const {sessionData} = useServicesContext();
    return loaded ? (
        <Schedule
            serviceRecipientId={props.serviceRecipientId}
            serviceAllocationId={serviceAllocationId!}
            agreement={agreement!}
            schedule={schedule}
            tasksDirect={directTasks}
            readOnly={!sessionData.hasRoleReferralEdit()}
            sessionData={sessionData}
            commandForm={props.form}
        />
    ) : null;
};

export const EditScheduleModalWrapper: FC<IdProps & ModalProps> = props => {
    const {loaded, agreement, schedule, directTasks, serviceAllocationId} = useScheduleData(
        props.serviceRecipientId,
        props.agreementId,
        props.scheduleId
    );
    return loaded ? (
        <EditScheduleModal
            serviceRecipientId={props.serviceRecipientId}
            serviceAllocationId={serviceAllocationId!}
            agreement={agreement!}
            schedule={schedule}
            tasksDirect={directTasks}
            setShow={props.setShow}
        />
    ) : null;
};

export const EditScheduleModal: FC<PropsEdit & ModalProps> = props => {
    const eccoAPI = useServicesContext();

    return (
        <ModalCommandForm
            show={true} // i.e. we're a form that's modal when shown
            setShow={props.setShow}
            title={props.schedule ? "edit schedule" : "add new schedule"}
            action={props.schedule ? "update" : "save"}
            maxWidth="sm"
        >
            {form => (
                <Schedule
                    {...props}
                    readOnly={!eccoAPI.sessionData.hasRoleReferralEdit()}
                    sessionData={eccoAPI.sessionData}
                    commandForm={form}
                />
            )}
        </ModalCommandForm>
    );
};
