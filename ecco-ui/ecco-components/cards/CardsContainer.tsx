import * as React from "react"
import {FC, ReactNode, useEffect, useState} from "react"
import {CardData, CardSource, compareCards} from "../cards/cards";
import {useReloadHandler} from "../data/entityLoadHooks";
import {useAppendableArray} from "../hooks";
import {Box, Grid, Typography} from "@eccosolutions/ecco-mui";
import {LoadingSpinner} from "../Loading";

/**
 * A CardsContainer is a component to which cards can be added and sorted
 * It should be possible to add Cards or CardGroups to a CardContainer, and that a CardGroup could contain other groups.
 * e.g.
 * <pre>
 * <CardContainer searchInput={} >
 *     <CardGroup title="appointments">
 *         <CardGroup title="today">
 *             {eventsForToday}
 *         </CardGroup>
 *         <CardGroup title="tomorrow">
 *             {eventsForToday}
 *         </CardGroup>
 *         <CardGroup title="previous 7 days">
 *             {eventsForToday}
 *         </CardGroup>
 *     </CardContainer>
 *  </pre>
 */

interface Props {
    sources: CardSource[];
    componentFactory: (card: CardData) => ReactNode;
    width?: number | undefined;
}


export const CardsContainer: FC<Props> = props => {

    const [done, setDone] = useState(0)
    const [cards, appendCard, reset] = useAppendableArray<CardData>();

    const loadCards = () => {
        reset()
        setDone(0)
        props.sources.forEach(source => {
            source.getCards().forEach(appendCard).then(() => setDone(d => d + 1));
        });
    }
    useReloadHandler(loadCards); // Potentially we should be using a hook for fetching our data so we can reload as needed e.g. useCardSources()

    useEffect(() => {
        loadCards();
    }, [props.sources]);

    return (
        <>
            {cards.length > 0 ? (
                <Grid container>
                    {cards.sort(compareCards).map(card => props.componentFactory(card))}
                </Grid>
            ) : done >= props.sources.length ? (
                props.children || (
                    <Box m={2}>
                        <Typography variant="h6">
                            Hmm. It seems there's nothing to see or do.
                        </Typography>
                    </Box>
                )
            ) : (
                <LoadingSpinner />
            )}
        </>
    );
}
CardsContainer.displayName = "CardsContainer"