import {Activity, DemandResource} from "ecco-rota";
import * as React from "react";
import {FC, ReactElement, ReactNode, useState, useEffect} from "react";
import {keyBy, keyFirstBy, uniqueByIdentity} from "@softwareventures/array";
import {EccoDate, EccoDateTime} from "@eccosolutions/ecco-common";
import {useServicesContext} from "../ServicesContext";
import {usePromise, useRota} from "../data/entityLoadHooks";
import {LoadingSpinner} from "../Loading";
import {PrintIconLazy} from "../layout/printing";
import {Button} from "@eccosolutions/ecco-mui";
import Print from "@material-ui/icons/Print";
import {
    BUILDINGS_SERVICE_ID,
    DemandScheduleDto,
    EventObject,
    EvidenceGroup,
    FormEvidence,
    SessionData
} from "ecco-dto";
import {
    AdditionalStaffData,
    additionalStaffPromise,
    AdditionalStaffText
} from "../care/AdditionalStaff";
import {CustomFormFields, SchemaFormRendererProps} from "../json-schema-form/SchemaForm";
import {useFormDataContext} from "../data/FormDataLoader";
import {isNotNull} from "@softwareventures/nullable";

const schedulesFactory = (dr: DemandResource | null) => () =>
    dr
        ? dr.getSchedulesForAppts().then(schedules => keyFirstBy(schedules, it => it!.scheduleId))
        : Promise.reject();

export const ActivityItem: FC<{
    event: EventObject;
    /** Allow event.title to be overridden */
    additionalStaff?: AdditionalStaffData | undefined;
    title?: string | undefined;
    location: string | undefined;
    flags: ReactNode;
    sessionData: SessionData;
    schedule: DemandScheduleDto;
}> = props => {
    const start = EccoDateTime.parseIso8601(props.event.start)!;
    const end = EccoDateTime.parseIso8601(props.event.end)!;

    return (
        <li className="rotaEvent">
            <header>
                {props.event.allDay ? (
                    <span className="rotaAllDay">{start.formatDateShort()}</span>
                ) : (
                    <span className="rotaPeriod">
                        <span className="rotaTime">{start.toEccoTime().formatHoursMinutes()}</span>{" "}
                        &#8211;{" "}
                        <span className="rotaTime">{end.toEccoTime().formatHoursMinutes()}</span>
                    </span>
                )}
                <span className="rotaTitle">{props.title || props.event.title}</span>
            </header>
            <dl className="clearfix">
                {props.additionalStaff && (
                    <>
                        <dt>&nbsp;</dt>
                        <dd style={{fontWeight: "bold"}}>
                            <AdditionalStaffText additionalStaff={props.additionalStaff} />
                        </dd>
                    </>
                )}
                {props.flags}
                <dt>location</dt>
                <dd>{props.location ?? "-"}</dd>
                <dt>tasks</dt>
                <dd>{props.schedule.parameters?.tasks ?? "-"}</dd>
            </dl>
            <div className="clearfix" />
        </li>
    );
};

export function getShiftGuidanceElement(
    sessionData: SessionData,
    CustomFormReadOnlyRenderer:
        | ((props: SchemaFormRendererProps<FormEvidence<CustomFormFields>>) => React.ReactElement)
        | undefined,
    serviceRecipientId: number
) {
    const formDefinitionUuid =
        sessionData &&
        sessionData
            .getServiceTypeById(BUILDINGS_SERVICE_ID)
            .getTaskDefinitionSetting("customForm1", "formDefinition");

    let guidanceElement: ReactElement | null = null;
    let formDataLoaded: boolean = true;
    if (formDefinitionUuid && CustomFormReadOnlyRenderer) {
        const formDefinition = sessionData.findFormDefinition(formDefinitionUuid);
        const {formEvidence, loaded} = useFormDataContext(
            serviceRecipientId,
            EvidenceGroup.fromSameTaskName("customForm1")
        );
        guidanceElement = formEvidence ? (
            <CustomFormReadOnlyRenderer
                formDefinition={formDefinition}
                formData={formEvidence.current.getDto()}
            />
        ) : null;
        formDataLoaded = loaded;
    }
    return {formDefinitionUuid, guidanceElement, formDataLoaded};
}


function alignByRunsMethod(resourceIn: DemandResource | null) {
    const [resourceWithEnoughAppts, setResourceWithEnoughAppts] = useState<DemandResource | null>(
        null
    );
    const [schedulesById, setSchedulesById] = useState<
        Record<number, DemandScheduleDto> | undefined
    >(undefined);
    const [appointments, setAppointments] = useState<Activity[] | undefined>(undefined);

    if (!resourceIn) {
        return null;
    }

    const resource = resourceIn;
    const resourceFilter = resource.getRota().getResourceFilter();
    const resourceFilterPart = resourceFilter.substring(0, resourceFilter.indexOf(":"));
    const resourceSpecificSrId = `${resourceFilterPart}:${resource.getServiceRecipientId()}`;
    let endDateAlignByRuns = EccoDate.parseIso8601(resource.getRota().getDto().endDate).addDays(1);

    const {rota: rotaWithExtraDay} = useRota(
        resource.getRota().getDate(),
        endDateAlignByRuns,
        resourceSpecificSrId,
        resource.getRota().getDemandFilter(),
        true,
        false
    );

    useEffect(() => {
        if (rotaWithExtraDay) {
            setResourceWithEnoughAppts(
                rotaWithExtraDay
                    .getResources()
                    .find(it => it.getResourceId() == resource.getResourceId())!
            );
        }
    }, [rotaWithExtraDay]);

    useEffect(() => {
        if (resourceWithEnoughAppts) {
            const p = schedulesFactory(resourceWithEnoughAppts)();
            p.then(data => {
                setSchedulesById(data);
            });
        }
    }, [resourceWithEnoughAppts]);

    useEffect(() => {
        if (rotaWithExtraDay && schedulesById) {
            const startOfInterest = resource
                .getAvailability()
                .startingAtOrAfter(resource.getRota().getDate().toDateTimeMidnight());
            // Interested in appts that overlap into our earliest shift of the day and start within the latest shift of the day
            if (startOfInterest) {
                // End is same time on end date
                const endOfInterest = endDateAlignByRuns.toDateTime(
                    startOfInterest.start.toEccoTime()
                );
                setAppointments(
                    resourceWithEnoughAppts!.getAppointments().filter(appt => {
                        // If no end date, then we want appts that start within our range
                        const end = appt.getEnd();
                        if (end === null) {
                            return (
                                appt.getStart().laterThanOrEqual(startOfInterest.start) &&
                                appt.getStart().earlierThan(endOfInterest)
                            );
                        } else {
                            return (
                                end.laterThan(startOfInterest.start) &&
                                appt.getStart().earlierThan(endOfInterest)
                            );
                        }
                    })
                );
            } else {
                console.info(
                    `Falling back to using all appts: (startOfInterest: ${startOfInterest}. avail = %o`,
                    resourceWithEnoughAppts?.getAvailability()
                );
                setAppointments(resource.getAppointments());
            } // If we can't establish the range, just print one
        }
    }, [rotaWithExtraDay, schedulesById]);

    return {resourceWithEnoughAppts, schedulesById, appointments};
}

export const PrintableResourceLine = ({
    resource,
    alignByRuns,
    onRendered,
    CustomFormReadOnlyRenderer
}: {
    resource: DemandResource;
    /** Default is true. Specify as false to align midnight to midnight */
    alignByRuns?: false | undefined;
    /** Callback (usually resolve from new Promise()) to notify that we've fully rendered */
    onRendered?: (() => void) | undefined;
    /** Additional component to show within the run this has to be implemented as a hook which returns null until data is resolved */
    CustomFormReadOnlyRenderer?:
        | ((props: SchemaFormRendererProps<object>) => ReactElement)
        | undefined;
}) => {
    const {sessionData, riskWorkRepository, apiClient} = useServicesContext();

    /** Resource with enough appts to cover shifts that run to next day */
    const [resourceWithEnoughAppts, setResourceWithEnoughAppts] = useState<DemandResource | null>(
        null
    );
    const [schedulesById, setSchedulesById] = useState<
        Record<number, DemandScheduleDto> | undefined
    >(undefined);
    const [appointments, setAppointments] = useState<Activity[] | undefined>(undefined);

    const flagsFactory = (resource: DemandResource | null) => () =>
        resource
            ? Promise.all(
                  uniqueByIdentity(
                      resource.getAppointments().map(appt => appt.getServiceRecipientId())
                  )
                      .filter(isNotNull)
                      .map(srId =>
                          riskWorkRepository.findRiskFlagsSnapshotByServiceRecipientIdAndEvidenceGroup(
                              srId
                          )
                      )
              ) // Also avail as rel="risk-flags"
                  .then(flags => keyFirstBy(flags, flag => flag.serviceRecipientId))
            : Promise.reject();

    const additionalStaffFactory = (appointments: Activity[] | undefined) => () =>
        appointments
            ? Promise.all(
                  appointments.map(appt =>
                      additionalStaffPromise(sessionData, apiClient, appt, appt.getRef())
                  )
              )
            : Promise.reject();


    const alignResult = alignByRunsMethod(alignByRuns !== false ? resource : null);

    useEffect(() => {
        if (alignResult != null) {
            setResourceWithEnoughAppts(alignResult.resourceWithEnoughAppts);
            setSchedulesById(alignResult.schedulesById);
            setAppointments(alignResult.appointments);
        } else {
            setResourceWithEnoughAppts(resource);
            setAppointments(resource.getAppointments());
        }
    }, [alignResult]);

    const {resolved: flagsBySrId} = usePromise(flagsFactory(resourceWithEnoughAppts), [
        resourceWithEnoughAppts
    ]); // resource null here sometimes, quite naturally

    const {resolved: additionalStaff} = usePromise(
        additionalStaffFactory(resourceWithEnoughAppts?.getAppointments()),
        [resourceWithEnoughAppts]
    );
    const {guidanceElement, formDataLoaded} = getShiftGuidanceElement(
        sessionData,
        CustomFormReadOnlyRenderer,
        resource.getServiceRecipientId()
    );

    if (
        !resourceWithEnoughAppts ||
        !schedulesById ||
        !flagsBySrId ||
        additionalStaff === undefined ||
        appointments === undefined ||
        !formDataLoaded
    ) {
        return <LoadingSpinner />;
    }

    if (onRendered) {
        onRendered();
    }

    const apptsByDay = keyBy(appointments!, appt => appt.getStart().toEccoDate().formatIso8601());
    let subjectDisplayName = resource.getName();
    return (
        <>
            <div className="rotaReport" style={{background: "white !important"}}>
                <h2>Appointments for {subjectDisplayName}</h2>
                {Object.keys(apptsByDay)
                    .sort()
                    .map(date => (
                        <div key={date} className="rotaDay">
                            <h3>{EccoDate.parseIso8601(date).formatPretty()}</h3>
                            {guidanceElement}
                            <ol>
                                {apptsByDay
                                    .get(date)
                                    ?.sort(Activity.sortStartAsc)
                                    .map(event => {
                                        const srId = event.getServiceRecipientId();
                                        if (!srId) return null;
                                        // ideally use RiskFlagsAsTextList
                                        const flags = flagsBySrId
                                            .get(srId)
                                            ?.latestFlags
                                                .filter(flag => flag.value) // filter for true flags
                                                .map(f =>
                                                    sessionData
                                                        .getListDefinitionEntryById(f.flagId)
                                                        .getDisplayName()
                                            )
                                            .join(", ");
                                        return (
                                            <ActivityItem
                                                key={event.getRef()}
                                                event={event.getDto()}
                                                additionalStaff={additionalStaff
                                                    ?.filter(
                                                        addSt =>
                                                            addSt &&
                                                            addSt.activityRef == event.getRef()
                                                    )
                                                    .pop()}
                                                title={`${event.getServiceRecipientName()} (${event.getTitle()})`}
                                                location={event.getLocation()}
                                                flags={
                                                    flags && (
                                                        <>
                                                            <dt>&nbsp;</dt>
                                                            <dd style={{fontWeight: "bold"}}>
                                                                {flags}
                                                            </dd>
                                                        </>
                                                    )
                                                }
                                                schedule={
                                                    schedulesById?.get(event.getScheduleId())!
                                                }
                                                sessionData={sessionData}
                                            />
                                        );
                                    })}
                            </ol>
                        </div>
                    ))}
            </div>
        </>
    );
};

export const PrintResourceLineIcon: FC<{
    resource: DemandResource;
    title: string;
    /** Additional component to show within the run this has to be implemented as a hook which returns null until data is resolved */
    CustomFormReadOnlyRenderer?:
        | ((props: SchemaFormRendererProps<object>) => ReactElement)
        | undefined;
}> = ({resource, title, CustomFormReadOnlyRenderer}) => {
    const generateContent = (onRendered: () => void) => {
        return Promise.resolve(
            <PrintableResourceLine
                key={resource.uniqueProps()}
                resource={resource}
                onRendered={() => onRendered()}
                CustomFormReadOnlyRenderer={CustomFormReadOnlyRenderer}
            />
        );
    };
    return (
        <PrintIconLazy
            getContent={generateContent}
            title={title}
            button={
                <Button variant="contained" color="primary" startIcon={<Print />}>
                    Print Appointments
                </Button>
            }
        />
    );
};
