// @ts-ignore
import SignaturePad from "signature_pad";
import {
    FC, MutableRefObject,
    useEffect,
    useRef,
    useState
} from "react";
import * as React from "react";
import "./ScribbleBackgroundCanvas.css";
import {mapNullable} from "@softwareventures/nullable";
import {useResizeCounter} from "../hooks/useResizeCounter";
import {ScribbleCanvas} from "./ScribbleCanvas";

export interface Props {
    readonly padRef?: MutableRefObject<SignaturePad> | undefined;
    readonly width?: number | undefined;
    readonly height?: number | undefined;
    /** The content to populate the canvas with when the component is mounted */
    readonly content?: string | undefined;
    /** A callback that is called when the component is unmounted */
    readonly setContent?: ((content: string) => void) | undefined;
}

export const ScribbleBackgroundCanvas: FC<Props> = (props) => {
    const container = useRef<HTMLDivElement | null>(null);
    const background = useRef<HTMLDivElement | null>(null);
    const backgroundResize = useResizeCounter(background.current);
    const [width, setWidth] = useState(props.width ?? 0);
    const [height, setHeight] = useState(props.height ?? 0);

    useEffect(() => {
        setWidth(props.width ?? background.current?.clientWidth ?? 0);
        setHeight(props.height ?? background.current?.clientHeight ?? 0);
    }, [background.current, backgroundResize, props.width, props.height]);

    useEffect(() => {
        if (container.current != null) {
            container.current.style.width = mapNullable(width, width => `${width}px`) ?? "";
            container.current.style.height = mapNullable(height, height => `${height}px`) ?? "";
        }
    }, [container.current, width, height]);

    return (
        <div className="ScribbleBackgroundCanvas">
            <div ref={container} className="container">
                {mapNullable(props.children, children => (
                    <div ref={background} className="background">
                        {children}
                    </div>
                ))}
                <ScribbleCanvas
                    padRef={props?.padRef}
                    width={width}
                    height={height}
                    content={props.content}
                    setContent={props.setContent}
                />
            </div>
        </div>
    );
};
