import {ResizeEvent} from "@eccosolutions/ecco-common";
import {
    AppBar,
    createStyles,
    Divider,
    IconButton,
    makeStyles,
    SwipeableDrawer,
    Theme,
    ThemeProvider,
    Toolbar,
    Typography,
    useMediaQuery
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {
    createContext,
    DependencyList,
    FC,
    ReactElement,
    ReactNode,
    useCallback,
    useContext,
    useEffect,
    useState
} from "react";
import {Route, useHistory, useLocation, useRouteMatch} from "react-router";
import {ErrorBoundary} from "ecco-components-core";
import {darkTheme} from "ecco-components-core";
import {Notifications} from "ecco-components-core";
import {Navigation, NavigationContext} from "./withNavigation";
import whiteLogo from "./images/logo_white.png";
import {ChevronLeft} from "@eccosolutions/ecco-mui-controls";
import {LoadingSpinnerDialogWithMessage} from "./Loading";
import {useAjaxStartStopMessage} from "./hooks/useAjaxStartStopMessage";
import * as bowser from "bowser"
import {useLocalStorage} from "ecco-components-core";
import {FontIconListItem} from "./FontIconListItem";
import {isOffline} from "ecco-dto";
import "./AppBarBase.css"

// Firefox has supported events on network connection since v41
export const canDetectOnline = navigator.onLine !== undefined && (bowser.chrome || bowser.chromium || bowser.firefox);

export const RouteFallbackWithDiagnostics = () => {
    const match = useRouteMatch();
    const location = useLocation();
    return <Route path="**">
        <h3>incorrect wiring - should never get here, or just put a '/' on the end?</h3>
        <p>{JSON.stringify(match)}</p>
        <p>{JSON.stringify(location)}</p>
    </Route>;
};

interface AppBarProps {
    /** The text title (or potentially components that are on the left) in a flex:grow element */
    title: string | ReactNode | ReactNode[];
    /** Item(s), usually a person menu that form a menu at the top right */
    right?: ReactNode | ReactNode[] | undefined;
    drawerContent: ReactNode;
    /** Menu is not docked and default state is hidden */
    wideMode?: boolean | undefined;
    /** The colour of the app bar, defaults to blue */
    appColour: string | undefined;
    onLogoClick?: (() => void) | undefined;
}

const ReactRouterClickNavigation: FC = (props) => {
    const history = useHistory();
    const reactRouterNavigation: Navigation = {
        asCallback: url => () => history.push(url),
        asHref: () => undefined
    };
    return <NavigationContext.Provider value={reactRouterNavigation} children={props.children}/>
};

const headerStyles = makeStyles((theme: Theme) =>
    createStyles({
        root: {
            flexGrow: 1,
        },
        menuButton: {
            marginRight: theme.spacing(2),
        },
        title: {
            flexGrow: 1,
        },
        logoButton: {
            padding: 0
        }
    }),
);

const LeftIcon = (props: {onClick: () => void}) => {
    const classes = headerStyles();
    return <IconButton className={`fa fa-bars ${classes.menuButton}`} onClick={props.onClick}/>;
};

function Titlebar(props: {
    wide: boolean,
    leftIcon: any,
    title: string | React.ReactNode | React.ReactNode[],
    // headerRef: Ref<HTMLSpanElement>,
    right: React.ReactNode | React.ReactNode[] | undefined
}) {
    const classes = headerStyles();
    return <>
        {!props.wide && props.leftIcon}
        <span className={classes.title}>
            {typeof props.title === "string"
                ? <Typography>{props.title}</Typography>
                : props.title
            }
        </span>
        {/*<span ref={props.headerRef}/>*/}
        {props.right}
    </>;
}

const updateOnlineStatus = () => {
    const message = navigator.onLine ? "connectivity restored" : "data connectivity lost";
    Notifications.add("network", message);
};

type AppBarContextProps = {
    header: string | ReactNode;
    setHeader: (header: string | ReactNode) => void;
    appColour: string | undefined;
    setAppColour: (colour: string | undefined) => void;
    menus: null | ReactElement[];
    setMenus: (newMenus: null | ReactElement[]) => void;
    extraMenuItems: null | ReactElement | ReactElement[];
    setExtraMenuItems: (menus: null | ReactElement | ReactElement[]) => void;
};
export const AppBarContext = createContext<AppBarContextProps | undefined>(undefined);
export const useAppBarContext = () => useContext(AppBarContext)!; // It's a dev error if we haven't initialised before use

export const AppBarContextProvider: FC = props => {
    const [header, setHeader] = useState<ReactNode>(null);
    const [appColour, setAppColour] = useState<string | undefined>(undefined);
    const [extraMenuItems, setExtraMenuItems] = useState<null | ReactElement | ReactElement[]>(
        null
    );
    const [menus, setMenus] = useState<null | ReactElement[]>(null);

    const opts: AppBarContextProps = {
        header,
        setHeader,
        appColour,
        setAppColour,
        menus,
        setMenus,
        extraMenuItems,
        setExtraMenuItems
    };
    return <AppBarContext.Provider value={opts}>{props.children}</AppBarContext.Provider>;
};

const blueAppBar = '#0d83ca';
const purpleAppBar = '#482880';
const greenAppBar = "#36720A";
const orangeAppBar = "#A47C13FF"; // also change incident.tsx

export function useAppBarOptions(
    header: ReactNode | string,
    deps: DependencyList = [],
    discriminator?: string | undefined,
    menus?: ReactElement[] | undefined
) {
    const appBarContext = useAppBarContext();
    if (!appBarContext) {
        return;
    }
    const {setAppColour, setHeader, setMenus} = appBarContext;
    useEffect(() => {
        switch (discriminator) {
            case "r":
                setAppColour(blueAppBar);
                break;
            case "w":
                setAppColour(purpleAppBar);
                break;
            case "b":
            case "m":
                setAppColour(greenAppBar);
                break;
            case "i":
                setAppColour(orangeAppBar);
                break;
            default:
                setAppColour(blueAppBar);
        }
        setHeader(header);
        menus && setMenus(menus);
        return () => {
            setHeader(null);
            setMenus(null);
            setAppColour(undefined);
        };
    }, deps);
}

export function WithHeader(props: {
    header: JSX.Element;
    discriminator?: string | undefined;
    deps?: DependencyList | undefined;
    children?: ReactNode | undefined;
}) {
    useAppBarOptions(props.header, props.deps, props.discriminator);
    return <>{props.children}</>;
}

export const useIsOffline = () => {
    const [offline, setOffline] = useState(isOffline());
    useOnlineStatusNotification(useCallback(() => setOffline(isOffline()), [setOffline]));
    return offline
}

export const useOnlineStatusNotification = (callback: () => void) => {
    useEffect(() => {
        window.addEventListener('online', callback);
        window.addEventListener('offline', callback);
        return () => {
            window.removeEventListener('online', callback);
            window.removeEventListener('offline', callback);
        }
    }, [callback]);
}


export const AutoOfflineToggle = () => {
    const [isAutoOfflineEnabled, setAutoOfflineEnabled] = useLocalStorage<string|undefined>('allowAutoOffline', undefined);
    return <FontIconListItem
        text="enable auto-offline"
        iconClasses={isAutoOfflineEnabled == "y" ? "fa fa-check-square-o" : "fa fa-square-o"}
        secondaryText={"because we access localhost with wifi disabled"}
        onClick={() => setAutoOfflineEnabled(isAutoOfflineEnabled == "y" ? 'n' : 'y')}
    />
}

/* for the see Sr2View AppHeader, and Sr2AppBarProvider */
export const AppBarBase: FC<AppBarProps> = props => {
    const ctx = useAppBarContext();
    const [dockedMenu, setDockedMenu] = useState(!props.wideMode);

    /** Hide menu by default on narrow screens (was 600px when we used "xs" breakpoint) */
    const canDock = useMediaQuery("(min-width: 769px)"); // Note default props for noSsr in theme.ts

    const dockMenu = dockedMenu && canDock;

    const [navOpen, setNavOpen] = useState(false);

    useOnlineStatusNotification(updateOnlineStatus);

    //const [colourOverride] = useStorage("localStorage", 'colourOverride', "");
    //const bkgColor = colourOverride || undefined;//"#f8f8f8"; // differs slightly to rgb(243, 246, 249) as per mui card demo page

    const loadingMessage = useAjaxStartStopMessage();
    const handleMenuToggle = () => setNavOpen(!navOpen);
    const classes = headerStyles();

    return (
        <ReactRouterClickNavigation>
            {props.title ? (
                <AppBar position="fixed">
                    <ThemeProvider theme={darkTheme}>
                        <Toolbar
                            style={{
                                marginLeft: dockMenu ? 256 : 0,
                                backgroundColor: props.appColour || blueAppBar
                            }}
                        >
                            <Titlebar
                                wide={dockMenu}
                                leftIcon={<LeftIcon onClick={handleMenuToggle} />}
                                title={ctx.header || props.title}
                                right={props.right}
                            />
                        </Toolbar>
                    </ThemeProvider>
                </AppBar>
            ) : null}
            <SwipeableDrawer
                style={{width: 256}}
                PaperProps={{elevation: 4}}
                variant={dockMenu ? "persistent" : "temporary"}
                onOpen={handleMenuToggle}
                onClose={handleMenuToggle}
                open={navOpen || dockMenu}
            >
                <div
                    style={{
                        backgroundColor: props.appColour || blueAppBar,
                        color: "white",
                        lineHeight: "64px",
                        padding: "0 16px"
                    }}
                >
                    <IconButton className={classes.logoButton} onClick={props.onLogoClick}>
                        <img
                            alt="logo"
                            src={whiteLogo}
                            height="48"
                            style={{verticalAlign: "middle"}}
                        />
                    </IconButton>
                    {canDock && (
                        <IconButton
                            onClick={() => {
                                if (dockedMenu) {
                                    setNavOpen(false); // close nav when undocking
                                }
                                setDockedMenu(!dockedMenu);
                                setTimeout(ResizeEvent.bus.fire, 30);
                            }}
                            style={{position: "absolute", right: 8, color: "white", marginTop: 8}}
                        >
                            <ChevronLeft
                                style={dockedMenu ? undefined : {transform: "rotate(180deg)"}}
                            />
                        </IconButton>
                    )}
                </div>
                {props.drawerContent}
                <Divider />
            </SwipeableDrawer>
            <div
                className="appbar-content" /* defined in AppBaseBase.css */
                style={{
                    /* a background colour here only covers the content, not the whole page background */
                    marginLeft: dockMenu ? 256 : 0
                }}
            >
                <ErrorBoundary>
                    {Notifications.component}
                    {props.children}
                    <LoadingSpinnerDialogWithMessage
                        show={loadingMessage != null}
                        message={loadingMessage}
                    />
                </ErrorBoundary>
            </div>
        </ReactRouterClickNavigation>
    );
};
AppBarBase.displayName = "== == AppBarBase == ==";
