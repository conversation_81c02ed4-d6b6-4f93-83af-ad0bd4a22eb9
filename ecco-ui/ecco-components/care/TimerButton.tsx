import * as React from "react";
import {FC, useEffect, useState} from "react";
import {Duration, EccoDateTime} from "@eccosolutions/ecco-common";
import {Button, Typography} from "@eccosolutions/ecco-mui";

export const TimerButton: FC<{
    startedTime?: EccoDateTime | undefined;
    onChange?: ((startedAt: EccoDateTime, stoppedAt?: EccoDateTime) => void) | undefined;
}> = props => {
    const [startTime, setStartTime] = useState<EccoDateTime | null>(props.startedTime!!);
    const [stopTime, setStopTime] = useState<EccoDateTime | null>();
    const [duration, setDuration] = useState<Duration | null>();

    const startTimer = () => {
        setStartTime(EccoDateTime.nowLocalTime());
    };
    const stopTimer = () => {
        const stoppedAt = EccoDateTime.nowLocalTime();
        setStopTime(stoppedAt);
    };

    useEffect(
        () => {
            let timer: number | null = null;
            const tick = () => {
                startTime &&
                    setDuration(EccoDateTime.nowLocalTime().subtractDateTime(startTime, true));
            };

            if (startTime) {
                timer = window.setInterval(tick, 1000);
                if (!stopTime) {
                    props.onChange && props.onChange(startTime);
                }
            }
            if (timer && startTime && stopTime) {
                props.onChange && props.onChange(startTime, stopTime);
                clearInterval(timer);
            }
            // Return action will clear Timeout when component unmount like in willComponentUnmount
            return () => {
                clearInterval(timer!!);
            };
        },
        [startTime, stopTime] // useEffect will run only one time (or when a vars in the array change)
    );

    if (!startTime) {
        return (
            <Button variant="contained" color="primary" onClick={startTimer}>
                start visit
            </Button>
        );
    }
    return (
        <span>
            <Button
                variant="contained"
                color="secondary"
                disabled={stopTime !== undefined}
                onClick={stopTimer}
            >
                stop visit
            </Button>
            <Typography>
                started: {startTime && startTime.toEccoTime().formatHoursMinutes()} duration:{" "}
                {duration ? Math.floor(duration.inSeconds()) : 0}
            </Typography>
        </span>
    );
};
TimerButton.displayName = "TimerButton"