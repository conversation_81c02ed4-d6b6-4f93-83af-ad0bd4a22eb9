import * as React from "react";
import { Select, MenuItem } from "@eccosolutions/ecco-mui";
import {eventValueAsInt} from "../buildings/BuildingSelector";
import {useServicesContext} from "../ServicesContext";

// NB 'care-checks' was created with: insert into cfg_list_definitions select id+79,0, 'care-checks', name, disabled, isDefault, parentId, metadata, businesskey+79 from cfg_list_definitions where listname='default-checks';

export const CareVisitItemOutcome = (props: {
    nullLabel?: string | undefined; // Omits null entry if no label provided - and provides "- select one -" option if id is null
    choiceId: number | null;
    onChange: (choiceId: number | null) => void;
}) => {
    const {sessionData} = useServicesContext();
    const list =
        sessionData &&
        sessionData.getListDefinitionEntriesByListName("care-checks", true).map(l => (
            <MenuItem key={l.getId()} value={l.getId()}>
                {l.getName()}
            </MenuItem>
        ));
    return (
        <>
            {sessionData && (
                <Select // using Select because we don't want the label etc wrapper here
                    value={props.choiceId == null ? "null" : props.choiceId}
                    onChange={e => props.onChange(eventValueAsInt(e))}
                >
                    {(props.nullLabel || props.choiceId == null) && (
                        <MenuItem key="null" value="null">
                            {props.nullLabel || "- select one -"}
                        </MenuItem>
                    )}
                    {list}
                </Select>
            )}
        </>
    );
};
