// eslint-disable-next-line no-restricted-imports
import {AppBarProps} from "@material-ui/core/AppBar";

import {AppBarBase, useAppBarContext} from "../AppBarBase";
import {LoadingOrError} from "../Loading";
import {SidebarMenuBuilder} from "../SidebarMenuBuilder";
import {UserMenu} from "../user/UserMenu";
import {
    ServiceRecipientWithEntitiesContext,
    useServiceRecipientWithEntities
} from "../data/serviceRecipientHooks";
import {useServicesContext} from "../ServicesContext";
import * as React from "react";
import {FC} from "react";
import {isOffline, SessionData, TaskNames} from "ecco-dto";
import {Box} from "@eccosolutions/ecco-mui";
import {StringUtils} from "@eccosolutions/ecco-common";


/* USED FOR newer mobile referral file */
/* AND offline - but offline is modal and doesn't see the menu - see ReferralFrame */

interface Props extends AppBarProps {
    allowLinkOut: boolean;
    basepath: string;
    srId: number;
    preview?: boolean | undefined;
}

function addMenuItem(
    menu: string,
    builder: SidebarMenuBuilder,
    sessionData: SessionData,
    context: ServiceRecipientWithEntitiesContext
) {
    switch (menu) {
        case "overview": {
            builder.addOwnRoute(
                "overview",
                "fa fa-eye",
                ``, // fa-info or fa-info-circle ?? and use fa-eye for audits
                sessionData.hasRoleFilePermissionFor("referralView.tabs.overview")
            );
            break;
        }

        case "hmsAccount": {
            builder.addOwnRoute(
                "account",
                "fa fa-gbp",
                `hms/`, // fa-info or fa-info-circle ?? and use fa-eye for audits
                sessionData.isEnabled("referralView.hms")
            );
            break;
        }

        case "account": {
            builder.addOwnRoute(
                "account",
                "fa fa-gbp",
                `finance/`, // fa-info or fa-info-circle ?? and use fa-eye for audits
                sessionData.isEnabled("referralView.charges")
            );
            break;
        }

        case "tasks":
        case "pathway": {
            builder.addOwnRoute(
                "pathway",
                "fa fa-check-circle-o",
                `tasks/`,
                sessionData.hasRoleFilePermissionForTasks()
            );
            break;
        }

        // 'forward plan' shows as part of support tab
        case "supportFwd":
        case "forward plan": {
            builder.addOwnRoute(
                sessionData.fileForwardPlanLabel(context.serviceType),
                "fa fa-list-ol",
                `forward-plan/`,
                sessionData.hasSupportForwardPlan(context.serviceType)
            );
            break;
        }

        // 'appointments' shows ServiceRecipientAppointmentsControl
        case "appointments": {
            builder.addOwnRoute(
                "appointments",
                "fa fa-calendar",
                `appointments/`,
                !context.serviceType.taskDefinitionSettingHasFlag(
                    TaskNames.referralView,
                    "hiddenMenus",
                    "appointments"
                ) && sessionData.hasRoleFilePermissionForAppointments()
            );
            break;
        }

        // a forward plan that shows as part of risk tab
        case "riskFwd":
        case "risk forward plan": {
            builder.addOwnRoute(
                sessionData.fileForwardRiskPlanLabel(context.serviceType),
                "fa fa-list-ol",
                `risk-forward-plan/`,
                sessionData.hasRiskForwardPlan(context.serviceType)
            );
            break;
        }

        // support shows:
        // - tasks configured with 'showOnSupportTab'
        // - visual link shows via feature referralOverview.supportPlan.graph and overwrites style as visual, but can be ignored given the evidence setting showStyleAs.visual
        // - dailyRoutines button to override style as 'checklist', but this should be ignored given evidence page options
        // - 'activity interest' calls ActivityInterestForm' if referralView.supportTabComponents has activityInterest, but can be ignored
        // - radar chart if not referralView.supportTabComponents.!radarChart which then opens spider/SupportRadarChart
        // - forward plan shows button of getFirstSupportTaskName with forward plan underneath (GoalsControl)
        // .addOwnRoute(sessionData.fileSupportLabel(context.serviceType), "fa fa-edit", `${params.srId}/task/needsReduction/`,
        //     sessionData.hasRoleFilePermissionForSupport(context.serviceType))

        // risk shows:
        // - tasks configured with 'showOnRiskTab'
        // - risk-chart tag
        // .addOwnRoute(sessionData.fileRiskLabel(context.serviceType), "fa fa-list-ol", `${params.srId}/task/threatAssessmentReduction/`,
        //     sessionData.hasRoleFilePermissionForRisk(context.serviceType))

        // 'support history' shows SupportHistoryListControl for needsReduction
        case "supportHistory":
        case "support history": {
            builder.addOwnRoute(
                sessionData.fileSupportHistoryLabel(context.serviceType),
                "fa fa-history",
                `support-history/`,
                sessionData.hasRoleFilePermissionForSupportHistory(context.serviceType)
            );
            break;
        }

        // 'visit history' shows CareVisitHistoryController - see offline/router
        case "careVisitHistory":
        case "visit history": {
            builder.addOwnRoute(
                sessionData.fileVisitHistoryLabel(context.serviceType),
                "fa fa-history",
                `visit-history/`,
                sessionData.hasRoleFilePermissionForVisitHistory(context.serviceType)
            );
            break;
        }

        // 'risk history' shows RiskHistoryListControl for threatAssessmentReduction
        case "riskHistory":
        case "risk history": {
            builder.addOwnRoute(
                sessionData.fileRiskHistoryLabel(context.serviceType),
                "fa fa-history",
                `risk-history/`,
                sessionData.hasRoleFilePermissionForRiskHistory(context.serviceType)
            );
            break;
        }

        // 'communication' shows communicationControls
        case "communication": {
            builder.addOwnRoute(
                "communication",
                "fa fa-comments-o",
                `communication/`,
                sessionData.hasRoleFilePermissionForCommunication(context.serviceRecipient.prefix)
            );
            break;
        }

        // 'checklist history' shows command-history-control tag for 'checklist' evidence group
        case "checklistHistory":
        case "checklist history": {
            builder.addOwnRoute(
                sessionData.fileChecklistHistoryLabel(context.serviceType),
                "fa fa-list-ol",
                `checklist-history/`,
                sessionData.hasRoleFilePermissionForChecklistHistory(context.serviceType)
            );
            break;
        }

        // 'form history' shows CustomFormHistoryListControl for null evidence page/group
        case "customFormHistory":
        case "form history": {
            builder.addOwnRoute(
                sessionData.fileFormHistoryLabel(context.serviceType),
                "fa fa-list-ol",
                `customform-history/`,
                sessionData.hasRoleFilePermissionForFormHistory(context.serviceType, sessionData)
            );
            break;
        }

        // 'attachments' shows AttachmentsControl
        case "attachments": {
            builder.addOwnRoute(
                "attachments",
                "fa fa-files-o",
                `attachments/`,
                sessionData.hasRoleFilePermissionForAttachments()
            );
            break;
        }

        // 'calendar' shows - see router.tsx
        case "calendar": {
            builder.addOwnRoute(
                "calendar",
                "fa fa-calendar",
                `calendar/`,
                !context.serviceType.taskDefinitionSettingHasFlag(
                    TaskNames.referralView,
                    "hiddenMenus",
                    "calendar"
                ) && sessionData.hasRoleFilePermissionForCalendar()
            );
            break;
        }

        case "contacts": {
            builder.addOwnRoute(
                "contacts",
                "fa fa-user",
                `contacts/`,
                !context.serviceType.taskDefinitionSettingHasFlag(
                    TaskNames.referralView,
                    "hiddenMenus",
                    "contacts"
                ) && sessionData.hasRoleFilePermissionForContacts()
            );
            break;
        }

        case "relationships": {
            builder.addOwnRoute(
                "relationships",
                "fa fa-users",
                `relationships/`,
                sessionData.hasRoleFilePermissionForRelationshipStar(
                    context.referral && context.referral.primaryReferralId,
                    context.serviceType
                )
            );
            break;
        }

        // 'services' shows a list of referrals, see referral-all-referrals-control tag
        // this is way to show auxiliary services WITH A LINK TO a client (ie cause for concern / incident etc)
        // also see offline/router.tsx ServicesWrapper
        case "services": {
            builder.addOwnRoute(
                "services",
                "fa fa-list-ol",
                `services/`,
                context.serviceRecipient.prefix == "r" &&
                    !context.serviceType.taskDefinitionSettingHasFlag(
                        TaskNames.referralView,
                        "hiddenMenus",
                        "services"
                    ) &&
                    sessionData.hasRoleFilePermissionForServices()
            );
            break;
        }

        // 'incidents' shows a list of referrals, see referral-all-referrals-control tag
        // this is way to show auxiliary services WITHOUT A LINK to a client file (ie cause for concern / incident etc)
        // also see offline/router.tsx ServicesWrapper
        case "incidents": {
            builder.addOwnRoute(
                "incidents",
                "fa fa-medkit",
                `incidents/`,
                sessionData.isEnabled("menu.incidents") &&
                    sessionData.hasRoleIncidents() &&
                    context.serviceRecipient.prefix == "r" &&
                    !context.serviceType.taskDefinitionSettingHasFlag(
                        TaskNames.referralView,
                        "hiddenMenus",
                        "incidents"
                    ) &&
                    sessionData.hasRoleFilePermissionForIncidents()
            );
            break;
        }

        // could show a list of workerJobs, if implemented
        /*.addOwnRoute("jobs",
        "fa fa-list-ol", `${params.srId}/workerJobs/`,
        context.serviceRecipient.prefix == "w" && sessionData.hasRoleFilePermissionForServices())*/

        // 'reports' shows one report: 05800000-0000-babe-babe-dadafee1600d
        case "reporting":
        case "reports": {
            builder.addOwnRoute(
                "reports",
                "fa fa-pie-chart",
                `reports/`,
                sessionData.hasRoleFilePermissionForDashboard()
            );
            break;
        }

        case "audit":
        case "audits": {
            builder.addOwnRoute(
                "audits",
                "fa fa-history",
                `audits/`,
                sessionData.hasRoleFilePermissionForAudits()
            );
            break;
        }

        // 'search' shows CommandHistoryListControl with just srId supplied
        case "search": {
            builder.addOwnRoute(
                "search",
                "fa fa-search",
                `search/`,
                sessionData.hasRoleFilePermissionForAuditSearch()
            );
            break;
        }

        case "access": {
            builder.addOwnRoute(
                "access",
                "fa fa-user",
                `access/`,
                context.serviceRecipient.prefix == "w" && sessionData.hasRoleUserAdmin()
            );
            break;
        }

        case "units": {
            builder.addOwnRoute(
                "units",
                "fa fa-home",
                `units/`,
                context.serviceRecipient.prefix == "b"
            );
            break;
        }

        case "staff": {
            builder.addOwnRoute(
                "staff",
                "fa fa-user",
                `staff/`,
                context.serviceRecipient.prefix == "b"
            );
            break;
        }

        case "residents": {
            builder.addOwnRoute(
                "residents",
                "fa fa-user",
                `residents/`,
                context.serviceRecipient.prefix == "b"
            );
            break;
        }

        case "repairs": {
            builder.addOwnRoute(
                "repairs",
                "fa fa-wrench",
                `repairs/`,
                sessionData.hasRoleRepairs() &&
                    context.serviceRecipient.prefix == "b" &&
                    !context.serviceType.taskDefinitionSettingHasFlag(
                        TaskNames.referralView,
                        "hiddenMenus",
                        "repairs"
                    ) &&
                    sessionData.hasRoleFilePermissionForRepairs()
            );
            break;
        }

        case "checksDue": {
            builder.addOwnRoute(
                "checks due",
                "fa fa-check",
                `checksDue/`,
                context.serviceRecipient.prefix == "b"
            );
            break;
        }

        case "checksHistory": {
            builder.addOwnRoute(
                "checks history",
                "fa fa-history",
                `checksHistory/`,
                context.serviceRecipient.prefix == "b"
            );
            break;
        }
    }
}

// Sr2AppContents is in offline/router.tsx which points to SvcRecPageRouter
const Sr2MenuItems: FC<{basepath: string; srId: number; allowLinkOut: boolean}> = ({
    basepath,
    srId
}) => {
    const {sessionData} = useServicesContext();

    const {context, error} = useServiceRecipientWithEntities(srId);

    if (!context) return <LoadingOrError error={error} />;

    // NB not re-introduced 'referralView.extraTabs.tabsToAdd' since probably best not as a new menu, but put on the 'support' area, now that we can
    // NB not re-introducing 'invoices' menu item as isn't converted to react anyway (have brought permission)
    // NB not re-introducing legacy 'services'

    const builder = new SidebarMenuBuilder(basepath);
    const menuItemNames =
        "overview," +
        "hmsAccount,account," +
        "tasks," +
        "units,staff,residents,repairs,checksDue,checksHistory," +
        "calendar," +
        "support,extraTabs,risk," + // TODO
        "supportFwd,riskFwd,supportHistory,careVisitHistory,checklistHistory,customFormHistory,riskHistory,search,relationships,appointments,contacts,attachments," +
        "childServices,services,incidents,reporting,communication,audit,access";
    let menuItemNamesCfg = context.serviceType.getTaskDefinitionSetting(
        TaskNames.referralView,
        "tabOrder"
    );
    if (sessionData.hasRoleFileLimiting()) {
        menuItemNamesCfg = context.serviceType.getTaskDefinitionSetting(
            TaskNames.referralView,
            "limitedTabOrder"
        );
    }
    const menuItemNamesArr = StringUtils.csvToArray(menuItemNamesCfg || menuItemNames);
    menuItemNamesArr.forEach(menu => addMenuItem(menu, builder, sessionData, context));
    builder
        .addDivider("1")
        .addExternalRoute(
            "back to menu",
            "fa fa-arrow-left",
            "nav/secure/welcome.html",
            true,
            isOffline() ? "currently offline" : undefined,
            isOffline()
        );
    return builder.build();
};

export const Sr2AppBar: FC<Props> = props => {
    const basepath = props.basepath;
    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return (
        <AppBarBase
            appColour={ctx.appColour}
            title={
                ctx.header
            } /* header must be set to show the <Titlebar>, see AppBarBase {props.title ? */
            right={<UserMenu extraMenuItems={ctx.extraMenuItems} />}
            drawerContent={
                <Sr2MenuItems
                    basepath={basepath}
                    srId={props.srId}
                    allowLinkOut={props.allowLinkOut}
                />
            }
        >
            <Box m={2}>
                {/* not required due to the definition of SrAppBar? in scripts/offline/router.tsx */}
                {props.children}
            </Box>
        </AppBarBase>
    );
};

Sr2AppBar.displayName = "Sr2AppBar";
