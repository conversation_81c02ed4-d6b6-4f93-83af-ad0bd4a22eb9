import * as React from "react";
import {EvidenceGroup} from "ecco-dto/evidence-dto";
import {HateoasResource, StringToObjectMap} from "@eccosolutions/ecco-common";
import {FC, ReactNode, useEffect, useMemo, useState} from "react";
import {useServicesContext} from "../ServicesContext";
import {DomElementContainer} from "ecco-components-core";
import {BaseServiceRecipientCommandDto, CommandDto, ReportCriteriaDto, SessionData} from "ecco-dto";
import {Button, Grid, Typography} from "@eccosolutions/ecco-mui";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {AuditHistoryIntegrations, AuditHistoryItemControl, getGlobalEccoAPI} from "../EccoAPI";
import {AuditHistoryFilterBar, SearchCriteria} from "./AuditHistoryFilterBar";
import {usePromise} from "../data/entityLoadHooks";
import {LoadingSpinner} from "../Loading";

export interface AuditHistoryProps {
    // NB provide sessionData - though could have used
    // const {sessionData} = getGlobalEccoAPI() or in 'componentDidMount' SessionDataService.getFeatures().then
    sessionData: SessionData;
    elementId?: string | undefined;
    resource?: HateoasResource | undefined;
    serviceRecipientId?: number | undefined; // TODO: Migrate to resource
    uuid?: string | undefined;
    evidenceGroup?: EvidenceGroup | undefined;
    taskName?: string | undefined;
    showWithoutSearch?: boolean | undefined;
}

// NB shows general audits by AuditsWrapper,AuditsSearchWrapper - used in config audits, sr-audits
function useAuditLoad(
    triggerLoad: boolean,
    reportCriteriaChanged: boolean,
    showWithoutSearch: boolean,
    reportCriteria: Partial<ReportCriteriaDto> | undefined,
    page: number
) {
    const {serviceRecipientRepository, sessionData} = useServicesContext();
    const [fullyLoaded, setFullyLoaded] = useState(false);

    const {resolved, error, loading, reload} = usePromise(() => {
        const load = triggerLoad && (showWithoutSearch ? true : reportCriteriaChanged);
        if (!load) {
            return Promise.resolve([]);
        }
        // show all audits for managers up
        const unlimitedAuditType = sessionData.hasTopLevelGroup("manager", true);
        const adjustForScope = unlimitedAuditType
            ? reportCriteria
            : {...reportCriteria, commandNameArr: ["comment"]};
        return serviceRecipientRepository
            .findServiceRecipientCommandsBySearch(adjustForScope as ReportCriteriaDto, page)
            .then(results => {
                if (results.length == 0) setFullyLoaded(true);
                return results;
            });
    }, [triggerLoad]);
    return {audits: resolved, fullyLoaded: fullyLoaded, error, loading, reload};
}

export const AuditHistoryController: FC<
    AuditHistoryProps & {serviceRecipientId: number}
> = props => {
    const {auditHistoryIntegrations, sessionData} = useServicesContext();

    const initialReportCriteria: () => Partial<ReportCriteriaDto> = () => {
        return {serviceRecipientFilter: `:${props.serviceRecipientId}`};
    };
    const initialReportCriteriaJson = JSON.stringify(initialReportCriteria());
    const [reportCriteria, setReportCriteria] = useState<Partial<ReportCriteriaDto>>();
    const [page, setPage] = useState(0);
    const [currentPageHistory, setCurrentPageHistory] = useState<
        BaseServiceRecipientCommandDto[] | null
    >(null);
    const [triggerLoad, setTriggerLoad] = useState(false);

    const [controls, setControls] = useState<ReactNode[]>([]);
    useMemo(() => {
        const nextControls = constructHistoryControls(
            currentPageHistory,
            auditHistoryIntegrations,
            sessionData
        );
        if (nextControls) {
            setControls(controls.concat(nextControls));
        }
    }, [currentPageHistory]);

    // TODO load any kind of audit
    /*const onLoad = () => {
        const query = props.resource
            ? CommandHistoryListControl.queryFromHateoasResource(props.resource)
            : props.uuid
                ? CommandHistoryListControl.queryWithOneId(props.uuid)
                : CommandHistoryListControl.queryWithIds(props.serviceRecipientId, props.evidenceGroup, props.taskName);
        query.then(historyPage => {
            setPage(page + 1);
            setHistory(history ? history.concat(historyPage) : historyPage);
            if (history.length == 0) setFullyLoaded(true);
        });
    }*/

    const reportCriteriaChanged = initialReportCriteriaJson != JSON.stringify(reportCriteria);
    const {audits, fullyLoaded, loading} = useAuditLoad(
        triggerLoad,
        reportCriteriaChanged,
        props.showWithoutSearch === undefined ? true : props.showWithoutSearch,
        reportCriteria,
        page
    );
    useEffect(() => {
        if (triggerLoad && audits) {
            setCurrentPageHistory(audits);
            setTriggerLoad(false);
        }
    }, [audits]);

    // NB INITIAL load triggered from here
    const onCriteria = (searchCriteria: SearchCriteria) => {
        let tmpCriteria = initialReportCriteria();
        // TODO using entityStatus instead of a new property for now, to avoid refactoring ecco-reports whilst maintaining branches
        if (searchCriteria.searchText) {
            tmpCriteria = {...tmpCriteria, entityStatus: searchCriteria.searchText};
        }
        if (searchCriteria.dateFrom) {
            tmpCriteria = {...tmpCriteria, from: searchCriteria.dateFrom.formatIso8601()};
        }
        if (searchCriteria.dateTo) {
            tmpCriteria = {...tmpCriteria, to: searchCriteria.dateTo.formatIso8601()};
        }
        setReportCriteria(tmpCriteria as ReportCriteriaDto);
    };

    useEffect(() => {
        if (reportCriteria) {
            setCurrentPageHistory([]);
            setControls([]);
            setPage(0);
            // NB triggerLoad used as need page and reportCriteria to be updated beforehand
            setTriggerLoad(true);
        }
    }, [reportCriteria]);

    return (
        <AuditHistoryPagedLayout
            controls={controls}
            onPageLoad={() => {
                setPage(page + 1);
                setTriggerLoad(true);
            }}
            onCriteria={onCriteria}
            showWithoutSearch={props.showWithoutSearch}
            hasCriteria={reportCriteriaChanged}
            loading={loading}
            allLoaded={fullyLoaded}
        />
    );
};

const AuditHistoryPagedLayout: FC<{
    controls: ReactNode[];
    onPageLoad: () => void;
    onCriteria: (criteria: SearchCriteria) => void;
    hasCriteria: boolean;
    showWithoutSearch?: boolean | undefined;
    loading: boolean;
    allLoaded: boolean;
}> = ({controls, onPageLoad, onCriteria, loading, allLoaded}) => {
    return (
        <Grid container spacing={2} alignItems={"center"} justify={"center"}>
            <Grid item xs={12}>
                <AuditHistoryFilterBar onChange={onCriteria} />
            </Grid>
            <Grid item xs={10} style={{textAlign: "center"}}>
                <Typography variant="subtitle1" style={{fontSize: "small"}}>
                    NB % is the search wildcard
                </Typography>
                <Typography variant="subtitle1" style={{fontSize: "small"}}>
                    NB the search is currently limited to this file only
                </Typography>
            </Grid>
            <Grid item xs={12}>
                <ul
                    className="entry-list evidence-history list-unstyled"
                    style={{listStyle: "none"}}
                >
                    {controls}
                </ul>
            </Grid>
            <Grid item xs={12} style={{textAlign: "center"}}>
                {loading ? (
                    <LoadingSpinner />
                ) : (
                    <>
                        {allLoaded ? (
                            <Typography>no more history</Typography>
                        ) : (
                            controls.length > 0 && (
                                <Button
                                    variant="outlined"
                                    color="primary"
                                    onClick={() => onPageLoad()}
                                >
                                    {controls.length == 0 ? "load" : "more"}
                                </Button>
                            )
                        )}
                    </>
                )}
            </Grid>
        </Grid>
    );
};

export interface CommandViewHandler<T extends CommandDto> {
    getSameTimeUuid?(): string | null;
    getCommand(): T;
    getTitleForCommand(): string;
    render(header: HTMLElement, child: HTMLElement): void;
}

function constructHistoryControls(
    commands: BaseServiceRecipientCommandDto[] | null,
    auditHistoryIntegrations: AuditHistoryIntegrations,
    sessionData: SessionData
): ReactNode[] | null {
    if (commands == null) {
        return null;
    }

    const controlPerSameTimeUuid: StringToObjectMap<AuditHistoryItemControl> = {};

    const result: ReactNode[] = [];
    /*
    // NB this could mean a GroupedHistory misses out some commands on the next page boundary
    commandsBySameTimeUuid(commands).forEach((uiGrp, _) =>
        result.push(<GroupedHistory uiGrp={uiGrp}/>)
    );
    return result;
    */

    const getOrCreateControl = (command: BaseServiceRecipientCommandDto) => {
        let handler = auditHistoryIntegrations.handleCommand(command, sessionData);
        const sameTimeUuid = handler.getSameTimeUuid ? handler.getSameTimeUuid() : null;
        const sameTimeControl = sameTimeUuid ? controlPerSameTimeUuid[sameTimeUuid] : null;
        var ctlForCmd = sameTimeControl
            ? sameTimeControl
            : auditHistoryIntegrations.componentFactory(sessionData);
        ctlForCmd.processHandler(handler);
        // return null to be filtered later - its rendered already in sameTimeControl
        if (sameTimeControl) {
            return null;
        }
        if (!sameTimeControl && sameTimeUuid) {
            controlPerSameTimeUuid[sameTimeUuid] = ctlForCmd;
        }
        return ctlForCmd;
    };

    commands
        .map(command => getOrCreateControl(command))
        .filter(isNonNull)
        //.reverse() // reverse after map as we rely on order for latest title
        .forEach(c => {
            result.push(
                <DomElementContainer
                    key={`key-${Uuid.randomV4()}`}
                    content={c.domElement()}
                    useLi={true}
                />
            );
        });
    return result;
}

function isNonNull<T>(it: T | null): it is T {
    return it != null;
}
