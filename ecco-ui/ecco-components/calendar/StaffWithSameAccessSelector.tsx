import * as React from "react";
import {ChangeEvent, FC} from "react";
import {Autocomplete, Chip, Grid, MenuItem, Select, TextField} from "@eccosolutions/ecco-mui";
import {useWorkersWithSameAccess} from "../data/entityLoadHooks";

function eventValueAsInt(e: ChangeEvent<{name?: string | undefined; value: unknown}>) {
    const str = e.target.value as string;
    return str == "null" ? null : parseInt(str);
}

export const StaffWithSameAccessSelector = (props: {
    nullLabel?: string | undefined;
    onChange: (workerId: number | null) => void;
}) => {
    const {staffWithSameAccess} = useWorkersWithSameAccess();

    return (
        <>
            {staffWithSameAccess && (
                <Select // using Select because we don't want the label etc wrapper here
                    onChange={e => props.onChange(eventValueAsInt(e))}
                >
                    <MenuItem key="null" value="null">
                        {props.nullLabel || "- overlay with -"}
                    </MenuItem>
                    {staffWithSameAccess.map(w => (
                        <MenuItem key={w.id} value={w.id}>
                            {w.name}
                        </MenuItem>
                    ))}
                </Select>
            )}
        </>
    );
};
interface CalendarContacts {
    contactId: number;
    contactName: string;
    calendarId: string;
}
export const StaffWithSameAccessSearch: FC<{
    labelName: string;
    onChange: (contactId: number, contactName: string, calendarId: string) => void;
    calendarContacts: CalendarContacts[];
}> = props => {
    const {staffWithSameAccess} = useWorkersWithSameAccess(false);
    if (!staffWithSameAccess) {
        return null;
    }

    const contactIds = props.calendarContacts.map(c => c.contactId);
    const list = staffWithSameAccess.filter(s => contactIds.indexOf(s.individualId) == -1);

    function onChange(value: number | null) {
        const selected = staffWithSameAccess!.filter(s => s.individualId == value).pop();
        if (selected) {
            props.onChange(selected.individualId, selected.displayName, selected.calendarId);
        }
    }

    const staffAvailable = (
        <Autocomplete
            id={"eccotest-staffAvail"}
            renderInput={params => (
                <TextField {...params} label={props.labelName} variant="outlined" />
            )}
            getOptionLabel={option => option.displayName}
            getOptionSelected={(a, b) => a.id == b.id}
            options={list}
            value={undefined}
            onChange={(_event, obj) => {
                onChange(obj?.individualId || null);
            }}
            /*style={{width: 270}}*/
        />
    );

    const contactsChosen = props.calendarContacts.map((s, i) => (
        <Chip key={s.contactId} label={s.contactName} className={`calendar${i + 1}`} />
    ));

    return (
        <Grid container alignItems="baseline">
            <Grid item xs={12}>
                <div style={{width: 260, height: 38, marginLeft: "auto", marginRight: "auto"}}>
                    {staffAvailable}
                </div>
            </Grid>
            <Grid item xs={12}>
                {contactsChosen}
            </Grid>
        </Grid>
    );
};
