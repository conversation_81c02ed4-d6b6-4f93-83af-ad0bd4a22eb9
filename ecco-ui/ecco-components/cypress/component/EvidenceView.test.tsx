import {mount} from "cypress/react";
import * as React from "react";
import EvidenceView from "../../billing/components/EvidenceView";
import {sessionData} from "../../__tests__/testUtils";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {EccoDate} from "@eccosolutions/ecco-common";
import {
    BuildingAjaxRepository,
    BuildingRepository,
    CalendarAjaxRepository,
    CalendarRepository,
    DemandScheduleDto,
    EventResourceDto,
    EvidenceGroup,
    Individual,
    InvoiceLineDto,
    InvoicesAjaxRepository,
    InvoicesRepository,
    ReferralAjaxRepository,
    ReferralRepository,
    SupportWork,
    SupportWorkAjaxRepository,
    SupportWorkRepository
} from "ecco-dto";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {mockReferralDtoPromise} from "../../test-support/mockReferrals";
import {buildingTestData} from "./BuildingEditor.test";

const invoiceRepository = getFailAllMethodsMock<InvoicesRepository>(InvoicesAjaxRepository);
invoiceRepository.findAllUninvoicedLines = (
    startDate: EccoDate,
    endDate: EccoDate,
    buildingId: number | null
) =>
    new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve(uninvoiced);
        }, 1000);
    });
const supportWorkRepository =
    getFailAllMethodsMock<SupportWorkRepository>(SupportWorkAjaxRepository);
supportWorkRepository.findOneSupportWorkByWorkUuid = (
    serviceRecipientId: number,
    evidenceGroup: EvidenceGroup,
    workUuid: string
) => {
    if (workUuid != "f3a81a07-67c6-4726-7dc7-d578c0f72151") {
        throw new Error("invalid workUuid - is that right?");
    }
    return Promise.resolve(supportWork);
};
const calendarRepository = getFailAllMethodsMock<CalendarRepository>(CalendarAjaxRepository);
calendarRepository.fetchEventsById = (ids: string[]) => Promise.resolve(eventsById);

const referralRepository = getFailAllMethodsMock<ReferralRepository>(ReferralAjaxRepository);
referralRepository.findOneReferralSummaryByServiceRecipientIdUsingDto = mockReferralDtoPromise;

const buildingRepository = getFailAllMethodsMock<BuildingRepository>(BuildingAjaxRepository);
buildingRepository.findAllBuildingsForUser = (
    query?:
        | {
              resourceType?: string | undefined;
              showChildren?: "true" | "false" | undefined;
          }
        | undefined
) => Promise.resolve(buildingTestData);

const overrides = {
    invoicesRepository: invoiceRepository,
    supportWorkRepository: supportWorkRepository,
    calendarRepository: calendarRepository,
    referralRepository: () => referralRepository,
    getBuildingRepository: () => buildingRepository,
    sessionData: sessionData
} as EccoAPI;

describe("EvidenceView tests", () => {
    it("render page", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <EvidenceView />
            </TestServicesContextProvider>
        );
        cy.viewport(1250, 750);
    });
});

// VISIT EXAMPLE COMMAND
// of interest is minsSpent, workDate and the command body only has plannedMinsSpent, plannedWorkDateTime
// {"operation":"update","commandName":"comment","uuid":"c0a10c3f-b6ba-46bb-7dfe-cdfb35a9c818","timestamp":"2022-07-06T12:42:12.881Z","workUuid":"d96f87d7-ee4c-4f21-7faa-f42d67f2c4ec","serviceRecipientId":200022,"evidenceGroup":"needs","taskName":"rotaVisit","comment":{"from":null,"to":"testset"},"workDate":{"from":null,"to":"2022-07-06T13:39:12.696"},"plannedWorkDateTime":"2022-07-06T13:00:00.000","plannedMinsSpent":30,"minsSpent":{"from":0,"to":3},"commandUri":"service-recipients/200022/evidence/needs/rotaVisit/comments/"}


// data from domcare
export const uninvoiced: InvoiceLineDto[] = [
    // visit
    {
        serviceRecipientId: 200023,
        lineUuid: "224a21fc-a08c-447e-a207-ba8a5d7c10c3",
        workUuid: "f3a81a07-67c6-4726-7dc7-d578c0f72151",
        eventId: "1224e190-1a20-40a0-8893-e99dcde93421:20220704T130000",
        workDate: "2022-07-04T16:45:02Z",
        workMinutesSpent: 12,
        plannedDate: "2022-07-04T16:45:00Z",
        plannedMinutes: 15,
        plannedResource: "sysadmin",
        type: null,
        description: "0 minutes on 2022-07-04T16:45:02Z[UTC]",
        amount: 0,
        taxRate: null,
        invoiceId: null,
        locked: false,
        links: []
    },
    // run
    {
        serviceRecipientId: 200033,
        lineUuid: "63df1487-0e9b-4919-9d0c-d18676983d1f",
        workUuid: null,
        eventId: "9df3fe73-40b5-4756-9cb6-adf231eee4d1:20220708T060000",
        workDate: null,
        workMinutesSpent: 0,
        plannedDate: "2022-07-08T06:00:00Z",
        plannedMinutes: 480,
        plannedResource: "Jo Eat",
        type: null,
        description: "480 minutes at 2022-07-08T06:00",
        netAmount: 0,
        taxRate: null,
        invoiceId: null,
        locked: false,
        links: []
    }
] as any as InvoiceLineDto[];

const supportWork: SupportWork = {
    id: "f3a81a07-67c6-4726-7dc7-d578c0f72151",
    serviceRecipientId: 200023,
    serviceAllocationId: 1074,
    requestedDelete: false,
    parentCode: null,
    parentPrefix: null,
    authorDisplayName: "sysadmin",
    comment: null,
    commentTypeId: null,
    eventId: "1224e190-1a20-40a0-8893-e99dcde93421:20220704T130000",
    minsSpent: 0,
    signatureId: null,
    workDate: "2022-07-04T16:45:02.000",
    createdDate: "2022-07-04T15:45:02.000",
    taskName: "rotaVisit",
    attachments: [],
    flags: [],
    locationId: null,
    meetingStatusId: null,
    mileageTo: null,
    mileageDuring: null,
    minsTravel: null,
    associatedActions: [],
    riskManagementRequired: false,
    riskManagementHandled: null,
    clientStatusId: null,
    actions: [],
    eventSnapshot: {
        eventUid: "1224e190-1a20-40a0-8893-e99dcde93421:20220704T130000",
        serviceAllocationId: 9,
        demandScheduleId: 99,
        demandScheduleDto: {} as DemandScheduleDto,
        resourceContact: {} as Individual,
        demandContact: {
            contactId: 1,
            discriminator: "individual",
            email: "<EMAIL>",
            firstName: "sysadmin",
            mobileNumber: "07",
            calendarId: "e37aede8-9f45-4bef-b6a5-d7ad27ca7841",
            isUser: true,
            userLastLoggedIn: "2022-07-04T15:42:14.000Z"
        } as Individual,
        plannedStartInstant: "2022-07-04T15:45:00Z",
        plannedEndInstant: "2022-07-04T15:55:00Z",
        startInstant: "2022-07-04T15:45:02Z",
        endInstant: "2022-07-04T15:45:07Z",
        serviceRecipientId: null,
        serviceAllocationId: null,
        workUuid: "f3a81a07-67c6-4726-7dc7-d578c0f72151",
        location: null
    }
};

export const eventsById: EventResourceDto[] = [
    {
        recurrence: true,
        recurringEntry: false,
        updatedByUri: "entity://CommentCommandViewModel/096c7998-fb78-4b40-736e-bc84d99800e4",
        serviceRecipientId: 200023,
        contactId: null,
        calendarIdUserReferenceUri: "entity://HibUser/100097",
        ownerCalendarId: "9d7802ea-0c74-4d45-bb5b-6feeebec6d15",
        title: "lunch",
        location: "21 Hebden Avenue, SK1 1SK",
        attendees: [
            {
                calendarIdUserReferenceUri: "entity://HibUser/100648",
                calendarId: "1a9ccf26-8d24-4b58-a976-e4e83ea4ea33",
                name: "AM Run 4",
                email: "<EMAIL>",
                required: false,
                status: "ACCEPTED"
            },
            {
                calendarIdUserReferenceUri: "entity://HibUser/100097",
                calendarId: "9d7802ea-0c74-4d45-bb5b-6feeebec6d15",
                name: "Michael Jordan",
                email: "<EMAIL>",
                required: false,
                status: "ACCEPTED"
            }
        ],
        allDay: false,
        start: "2022-07-04T13:00:00.000",
        end: "2022-07-04T13:30:00.000",
        classNames: null,
        eventType: "Other",
        eventCategoryId: null,
        eventStatusId: null,
        eventStatusRateId: null,
        evidenceWorkUuid: "f3a81a07-67c6-4726-7dc7-d578c0f72151",
        links: [
            {rel: "demand-schedule", href: "/domcare/api/rota/agreements/schedules/100317"},
            {
                rel: "edit",
                href: "https://please.set.ecco.websiteUrl/nav/referrals/100007"
            },
            {rel: "rota visit", href: "/domcare/nav/referrals/100007/task/rotaVisit"},
            {
                rel: "calendar-events",
                href: "/domcare/api/calendar/event/search?calendarId=9d7802ea-0c74-4d45-bb5b-6feeebec6d15&startTime=2022-07-04T13:00:00.000&endTime=2022-07-04T13:30:00.000"
            },
            {
                rel: "risk flags",
                href: "https://please.set.ecco.websiteUrl/api/service-recipients/200023/evidence/threat/snapshots/latest"
            }
        ],
        uid: "1224e190-1a20-40a0-8893-e99dcde93421:20220704T130000"
    }
];
