import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {ServicesProjectsBuildingsSelector} from "../../servicesprojects/ServicesProjectsBuildingsSelector";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {BuildingAjaxRepository} from "ecco-dto";
import {buildingTestData} from "./BuildingEditor.test";

const buildingRepository = getFailAllMethodsMock(BuildingAjaxRepository);
buildingRepository.findAllBuildingsForUser = (
    query?:
        | {
              resourceType?: string | undefined;
              showChildren?: "true" | "false" | undefined;
          }
        | undefined
) => {
    return Promise.resolve(buildingTestData);
};

const overrides = {
    sessionData: sessionData,
    getBuildingRepository: () => buildingRepository
} as any as EccoAPI;

/*
 * 'WINDOW' object undefined, so using param 'applicationRootPath'.
 * We set properties on 'window.applicationProperties =' to make life easier (eg servlet path).
 * However, cypress uses an iframe, so it's undefined. We can therefore use cypress to set the data
 * on the right window, but these don't work as we set applicationProperties at the import level.
 * The cypress suggestion is to set:
 *      - something in support/index.js to apply using 'window:before:load' or on('test:before:run')
 *          NB window:before:load only for end-to-end (not react component) https://github.com/cypress-io/cypress/issues/17707
 *      - then wait in the test to check for its presence using cy.window().its('applicationProperties');
 * This code pulls in EccoAPI, which pulls in global.ts, which fails undefined on: const {applicationRootPath, remoteHost} = window.applicationProperties
 * However, we can refactor to not have window code executed on the module import, although this does then fail on other imports also.
 * Ultimately, it would be better to swap out the EccoAPI for a test one (context?), such as those when searching 'window.applicationProperties ='
 * but it was simpler here to note progress and use a param for the path.
 */

describe("ServicesProjectsBuildingsSelector tests", () => {
    it("it mounts", () => {
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <ServicesProjectsBuildingsSelector applicationRootPath={"/context-path/"} />
            </TestServicesContextProvider>
        );
    });
});
/*
describe("ServicesProjectsBuildingsForm tests", () => {
    it("it mounts", () => {
        const delayClearOnFlushFinish = 500;
        mount(
            <TestServicesContextProvider overrides={overrides}>
                <Playground />
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    <ServicesProjectsBuildingsSelectorSubform />
                </CommandFormTest>
            </TestServicesContextProvider>
        );
    });
});

export const Playground = () => {
    const [show, setShow] = useState(false);

    return (
        <>
            <Button onClick={() => setShow(true)}>rota</Button>
            {show && <ServicesProjectsBuildingsSelectorForm setShow={setShow} show={show} />}
        </>
    );
};
*/
