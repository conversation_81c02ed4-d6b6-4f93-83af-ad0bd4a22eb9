import {mount} from "cypress/react";
import * as React from "react";
import {EccoAPI} from "../../EccoAPI";
import {TestServicesContextProvider} from "../../test-support/TestServicesContextProvider";
import {sessionData} from "../../__tests__/testUtils";
import {BuildingEditor} from "../../buildings/BuildingForm";
import {Command} from "ecco-commands";
import {CommandFormTest, CommandFormTestOutput} from "../../cmd-queue/testUtils";
import {CommandForm} from "../../cmd-queue/CommandForm";
import {getFailAllMethodsMock} from "../../test-support/mock-utils";
import {Address, AddressedLocationAjaxRepository, Building, BuildingAjaxRepository} from "ecco-dto";

const buildingRepository = getFailAllMethodsMock(BuildingAjaxRepository);
buildingRepository.findAllBuildingsForUser = (
    query?:
        | {
              resourceType?: string | undefined;
              showChildren?: "true" | "false" | undefined;
          }
        | undefined
) => {
    return Promise.resolve(buildingTestData);
};
const addressRepository = getFailAllMethodsMock(AddressedLocationAjaxRepository);
addressRepository.findOneAddress = (addressId: number): Promise<Address> => {
    return Promise.resolve({
        addressId: 1082,
        address: ["Extra care service", null, null],
        town: null,
        county: null,
        postcode: "SK2 2SK",
        disabled: false
    });
};

const overrides = {
    sessionData: sessionData,
    getBuildingRepository: () => buildingRepository,
    getAddressRepository: () => addressRepository
} as any as EccoAPI;

describe("BuildingEditor tests", () => {
    /*it("it mounts", () => {
        // DEBUG - set this high or null to see the cmd output for longer/forever
        // currently needs to clear to show a command wasn't emitted - see "delete new one" below
        const delayClearOnFlushFinish = 2500;

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <>
                            <BuildingParentSelector
                                commandForm={form}
                                building={undefined}
                                newParentBuildingIds={[1065, 1069]}
                                newResourceTypeId={CARERUN_RESOURCE_ID}
                            />
                            <CommandFormTestOutput cmdEmitted={cmdEmitted} />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
    });*/

    it("building new", () => {
        // DEBUG - set this high or null to see the cmd output for longer/forever
        // currently needs to clear to show a command wasn't emitted - see "delete new one" below
        const delayClearOnFlushFinish = 2500;

        const resourceTypeId = 132; // BUILDING_RESOURCE_ID;

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <>
                            <BuildingEditor serviceRecipientId={undefined} modal={false} />
                            <CommandFormTestOutput cmdEmitted={cmdEmitted} />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
    });

    /*it("room new", () => {
        // DEBUG - set this high or null to see the cmd output for longer/forever
        // currently needs to clear to show a command wasn't emitted - see "delete new one" below
        const delayClearOnFlushFinish = 2500;

        const resourceTypeId = 134; // ROOM_RESOURCE_ID;

        mount(
            <TestServicesContextProvider overrides={overrides}>
                <CommandFormTest delayClearOnFinishFlush={delayClearOnFlushFinish}>
                    {(form: CommandForm, cmdEmitted: Command[]) => (
                        <>
                            <BuildingEditor
                                serviceRecipientId={undefined}
                                modal={false}
                                newParentBuildingId={1065}
                            />
                            <CommandFormTestOutput cmdEmitted={cmdEmitted} />
                        </>
                    )}
                </CommandFormTest>
            </TestServicesContextProvider>
        );
    });*/
});

// buildings
export const buildingTestData: Building[] = [
    {
        buildingId: 1065,
        name: "Hebden Avenue",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200002,
        calendarId: "dd00006c-0467-4c4e-a0d2-8e079c3b44cf",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1082,
        address: {
            addressId: 1082,
            address: ["Extra care service", null, null],
            town: null,
            county: null,
            postcode: "SK2 2SK",
            disabled: false
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1065&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200002/"
            }
        ]
    },
    {
        buildingId: 1067,
        name: "Far Dene",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200003,
        calendarId: "da5c0da1-7698-4e51-a1eb-aef960930eac",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1067&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200003/"
            }
        ]
    },
    {
        buildingId: 1069,
        name: "Cherry Tree House",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200004,
        calendarId: "94006d96-960b-4dd2-afa3-8951789c35a2",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: 1077,
        address: {
            addressId: 1077,
            address: ["11 Cherry Tree Walk", null, null],
            town: "Stockport",
            county: null,
            postcode: "SK3 3SK",
            disabled: false
        },
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1069&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200004/"
            }
        ]
    },
    {
        buildingId: 1071,
        name: "Miry Lane",
        disabled: false,
        externalRef: null,
        resourceTypeId: 135,
        resourceTypeName: "leased property",
        serviceRecipientId: 200005,
        calendarId: "2bd381b4-3ada-466a-908a-cfb480cb9b4f",
        parentId: null,
        parentName: null,
        serviceAllocationId: -100,
        locationId: null,
        address: null,
        textMap: {},
        choicesMap: {},
        links: [
            {
                rel: "rota",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/workers:all/view?demandFilter=buildings:1071&loadResource=true&loadDemand=true{&startDate,endDate}"
            },
            {
                rel: "agreements",
                href: "https://demo.eccosolutions.co.uk/domcare/api/rota/agreements/serviceRecipient/200005/"
            }
        ]
    }
];