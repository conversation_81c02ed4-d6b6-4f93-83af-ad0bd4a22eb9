import "./wdyr";

import Box from "@material-ui/core/Box";
import {Calendar} from "ecco-calendar";

import {
    AppBarBase,
    AppBarContextProvider,
    AppBilling,
    AsyncSessionData,
    CommandForm,
    QrCodeScan
} from "ecco-components";
import {EccoTheme, ErrorBoundary} from "ecco-components-core";
import {handleLazy} from "ecco-components/Loading";
import {SidebarMenuBuilder} from "ecco-components/SidebarMenuBuilder";
import * as React from "react";
import {FC, useState} from "react";
import * as ReactDOM from "react-dom";
import {Route, Switch, useHistory, useParams, useRouteMatch} from "react-router";
import "./index.css";
import {BrowserRouter} from "react-router-dom";
import AgreementsTest from "./AgreementsTest";
import BuildingSelectorTest from "./BuildingSelectorTest";
import ContractsTest from "./contracts/ContractsTest";
import {ExampleEmbeddedCommandForm, ExampleModalCommandForm} from "./forms/CommandForms";
import {ModalFormsPage} from "./forms/ModalForms";
import DatePickersTest from "./inputs/DatePickersTest";
import {EditableInputsTest} from "./inputs/EditableInputsTest";
import {client, sessionDataFn} from "./services";
import {ServicesContextProvider} from "./ServicesContextProvider";
import * as serviceWorker from "./serviceWorker";
import RateCardsTest from "./RateCardsTest";
import RateCardsView from "ecco-components/contracts/RateCardsView";
import FormDefinitionsTest from "./evidence/FormDefinitionsTest";
import {LoginDialog} from "ecco-components/inputs/LoginDialog";
import SchemaDrivenTest from "./SchemaDrivenTest";
import {SelectListTest} from "./inputs/SelectListTest";
import AddressLocationTest from "./AddressLocationTest";
import PrintingTest from "./utils/PrintingTest";
import {LinkTest} from "./LinkTest";
import PromiseTest from "./PromiseTest";
import {ToggleMenuItem} from "ecco-components/menu/ToggleMenuItem";
import {UserMenu} from "ecco-components/user/UserMenu";
import {DashboardDemo} from "./DashboardDemo";
import CareVisitItemOutcomeTest from "./CareVisitItemOutcomeTest";
import {CareVisitSummaryTest} from "./CareVisitSummaryTest";
import {TaskTest} from "./TaskTest";
import {
    AutoOfflineToggle,
    RouteFallbackWithDiagnostics,
    useAppBarContext,
    useIsOffline
} from "ecco-components/AppBarBase";
import {LoggedOut} from "ecco-components/user/LoggedOut";
import {SignatureTest} from "./inputs/SignatureTest";
import {SignatureTestLandscape} from "./inputs/SignatureTestLandscape";
import {QuestionAnswerTest} from "./evidence/QuestionAnswerTest";
import {CommentEntryTest} from "./evidence/CommentEntryTest";
import {ReactScrollTest} from "./ReactScrollTest";
import {SmartStepTest} from "./evidence/SmartStepTest";
import {EvidencePageCommandFormTest} from "./evidence/EvidencePageTest";
import {TableTest} from "./TableTest";
import {Button} from "@eccosolutions/ecco-mui";
import {TaskDefinitionCreate} from "ecco-admin";
import {Command} from "ecco-commands";
import {CommandFormTest} from "ecco-components/cmd-queue/testUtils";
import {AvatarImageTest} from "./inputs/AvatarImageTest";
import {CarePagePrintableAppointmentsTest, CarePageSummaryTest} from "./CarePageTest";
import {GuidanceTest} from "./GuidanceTest";
import {SchedulerSchemaList} from "ecco-components/rota/SchedulerSchemaList";
import {IncidentSchemaList} from "ecco-incidents";

const SearchTest = React.lazy(() => import("./SearchTest"));

const RotaMenuItems: FC<{path: string}> = props => {
    return (
        new SidebarMenuBuilder(props.path)
            .addSubHeader("rota overview")
            .addOwnRoute(
                "billing",
                "fa fa-money",
                "billing",
                undefined,
                "verify, invoices & payroll"
            )
            .addOwnRoute("contracts", "fa fa-money", "contracts", undefined, "finance stuff")
            .addOwnRoute("rate cards", "fa fa-money", "rateCards", undefined, "finance stuff")
            .addOwnRoute(
                "agreements and schedules",
                "fa fa-calendar",
                "agreements",
                undefined,
                "rota stuff"
            )
            .addOwnRoute("care-page-summary", "fa fa-calendar", "carepagesummary", undefined)
            .addOwnRoute("care-page-printable", "fa fa-calendar", "carepageprintable", undefined)
            .addOwnRoute("care-visitsummary", "fa fa-calendar", "carevisitsummary", undefined)
            .addOwnRoute("care-visitdetail", "fa fa-calendar", "carevisitdetail", undefined)
            .addOwnRoute("scheduler-schema", "fa fa-calendar", "scheduler-schema", undefined)
            .addOwnRoute("incident-schema", "fa fa-medkit", "incident-schema", undefined)
            .addOwnRoute("qr-scan", "fa fa-qrcode", "qr-scan", undefined)
            /*.addMenu("back", "fa fa-arrow-left", () => {
            const parts = window.location.href.split('/')
            parts.pop();
            window.location.href = parts.join('/');
            }, true)*/
            .build()
    );
};

const RotaAppBar: FC = () => {
    return (
        <AppBarContextProvider>
            <RotaAppBarBase />
        </AppBarContextProvider>
    );
};

const RotaAppBarBase: FC = () => {
    const {path} = useRouteMatch();
    const history = useHistory();

    const Contract: FC = () => {
        const {contractId} = useParams<{contractId: string}>();
        // useEffect(() => {
        //     setTitle(<span>contract: {contractId}</span>);
        //     return () => {
        //         setTitle(null);
        //     };
        // },        [contractId]);
        return <RateCardsView contractId={parseInt(contractId)} />;
    };

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }

    return (
        <AppBarBase
            appColour={ctx.appColour}
            title={ctx.header || "rota"}
            right={<UserMenu extraMenuItems={ctx.extraMenuItems} />}
            onLogoClick={() => history.push(`${path}`)}
            drawerContent={<RotaMenuItems path={"/rota/"} />}
        >
            <Box m={1}>
                <Switch>
                    <Route path={`${path}/billing`}>
                        <AppBilling />
                    </Route>
                    <Route path={`${path}/rateCards`}>
                        <RateCardsTest />
                    </Route>
                    <Route exact path={`${path}/contracts`}>
                        <ContractsTest />
                    </Route>
                    <Route exact path={`${path}/contracts/:contractId`}>
                        <Contract />
                    </Route>
                    <Route path={`${path}/agreements`}>
                        <AgreementsTest />
                    </Route>

                    {/* a CarePage shows CareVisitSummaryCard's */}
                    <Route path={`${path}/carepagesummary`}>
                        <CarePageSummaryTest />
                    </Route>
                    {/* a CarePage shows printable appts */}
                    <Route path={`${path}/carepageprintable`}>
                        <CarePagePrintableAppointmentsTest />
                    </Route>

                    {/* individual summary */}
                    <Route path={`${path}/carevisitsummary`}>
                        <CareVisitSummaryTest />
                    </Route>
                    {/* individual detail */}
                    <Route path={`${path}/carevisitdetail`}>
                        {/* NB visits are more difficult from here, as most code in /offline */}
                        {
                            <div>
                                CareVisitDetail is currently in ecco-offline-app, and change
                                public/index.html from ql to testdomcare ecco_jo / Demo OR use
                                ecco-staff-app and change index.tsx from App to CareApp
                            </div>
                        }
                    </Route>
                    <Route path={`${path}/scheduler-schema`}>
                        <SchedulerSchemaList />
                    </Route>
                    <Route path={`${path}/incident-schema`}>
                        {/* see also SchemaDrivenTest and SchedulerView.test.tsx */}
                        <IncidentSchemaList />
                    </Route>
                    <Route path={`${path}/qr-scan`}>
                        <QrCodeScan />
                    </Route>
                    <Route exact path={`${path}`}>
                        <div />
                    </Route>
                </Switch>
            </Box>
        </AppBarBase>
    );
};

const EvidenceComponentMenuItems: FC<{path: string}> = props => {
    return new SidebarMenuBuilder(props.path)
        .addSubHeader("evidence")
        .addOwnRoute("comment", "fa fa-comment", "comment", undefined, "comment control")
        .addOwnRoute("smartstep", "fa fa-star", "smartstep", undefined, "smartstep control")
        .addOwnRoute(
            "questionanswer",
            "fa fa-question",
            "questionanswer",
            undefined,
            "questionanswer control"
        )
        .addOwnRoute("support", "fa fa-puzzle-file", "supportpage", undefined, "evidence page")
        .addOwnRoute(
            "questionnaire",
            "fa fa-puzzle-file",
            "questionnairepage",
            undefined,
            "evidence page"
        )
        .build();
};

const EvidenceComponentAppBar: FC = () => {
    return (
        <AppBarContextProvider>
            <EvidenceComponentAppBarBase />
        </AppBarContextProvider>
    );
};

const EvidenceComponentAppBarBase: FC = () => {
    const {path} = useRouteMatch();
    const history = useHistory();

    const EvidencePageWithParams: FC = () => {
        const {srId, taskName} = useParams<{srId: string; taskName: string}>();
        return <EvidencePageCommandFormTest srId={parseInt(srId)} taskName={taskName} />;
    };

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }

    return (
        <AppBarBase
            appColour={ctx.appColour}
            title={ctx.header || "evidence"}
            right={<UserMenu extraMenuItems={ctx.extraMenuItems} />}
            onLogoClick={() => history.push(`${path}`)}
            drawerContent={<EvidenceComponentMenuItems path={"/evidence/"} />}
        >
            <Box m={1}>
                <Switch>
                    <Route path={`${path}/comment`}>
                        <CommentEntryTest />
                    </Route>
                    <Route path={`${path}/smartstep`}>
                        <SmartStepTest />
                    </Route>
                    <Route path={`${path}/questionanswer`}>
                        <QuestionAnswerTest />
                    </Route>
                    <Route path={`${path}/supportpage`}>
                        {/*NB 301253 for UAT in index.html, or 200013 for the default testdomcare */}
                        <EvidencePageCommandFormTest srId={200013} taskName={"needsAssessment"} />
                    </Route>
                    <Route path={`${path}/questionnairepage`}>
                        {/*NB we configured a questionnaire on this client in testdomcare, its not base data*/}
                        <EvidencePageCommandFormTest
                            srId={200013}
                            taskName={"feedbackQuestionnaire"}
                        />
                    </Route>
                    <Route exact path={`${path}/evidencepage/:srId/:taskName`}>
                        <EvidencePageWithParams />
                    </Route>
                    <Route exact path={`${path}`}>
                        <div />
                    </Route>
                </Switch>
            </Box>
        </AppBarBase>
    );
};

const AdminComponentMenuItems: FC<{path: string}> = props => {
    return new SidebarMenuBuilder(props.path)
        .addSubHeader("admin")
        .addOwnRoute(
            "create definition",
            "fa fa-list",
            "createDefinition",
            undefined,
            "create new task defn"
        )
        .build();
};

const CreateDefinitionPage: FC = () => {
    const [show, setShow] = useState(false);

    return (
        <CommandFormTest>
            {(form: CommandForm, cmdEmitted: Command[]) => (
                <>
                    <Button onClick={() => setShow(true)}>create new task definition</Button>
                    {show && <TaskDefinitionCreate />}
                </>
            )}
        </CommandFormTest>
    );
};

const AdminComponentAppBar: FC = () => {
    return (
        <AppBarContextProvider>
            <AdminComponentAppBarBase />
        </AppBarContextProvider>
    );
};

const AdminComponentAppBarBase: FC = () => {
    const {path} = useRouteMatch();
    const history = useHistory();

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }

    return (
        <AppBarBase
            appColour={ctx.appColour}
            title={"admin"}
            right={<UserMenu extraMenuItems={ctx.extraMenuItems} />}
            onLogoClick={() => history.push(`${path}`)}
            drawerContent={<AdminComponentMenuItems path={"/admin/"} />}
        >
            <Box m={1}>
                <Switch>
                    <Route path={`${path}/createDefinition`}>
                        <CreateDefinitionPage />
                    </Route>
                    <Route exact path={`${path}servicetypes/:id/:taskName`}>
                        <div />
                        {/*<TaskDefinitionEntrySettingsListControl>*/}
                    </Route>
                    <Route exact path={`${path}`}>
                        <div />
                    </Route>
                </Switch>
            </Box>
        </AppBarBase>
    );
};

const ComponentElementsMenuItems: FC<{path: string}> = props => {
    return (
        new SidebarMenuBuilder(props.path)
            .addSubHeader("components - low-level")
            .addOwnRoute("date pickers", "fa fa-calendar", "date-pickers", undefined, "use these")
            .addOwnRoute("inputs-editable", "fa fa-file", "inputs-editable", undefined)
            .addOwnRoute("inputs-select", "fa fa-list", "inputs-select", undefined)
            .addOwnRoute("menu-items", "fa fa-check-square", "menu-items", undefined)
            .addOwnRoute("select", "fa fa-list", "select-list", undefined, "dropdown example")
            .addOwnRoute(
                "select-hierarchical",
                "fa fa-list",
                "select-list-hierarchical",
                undefined,
                "hierarchical"
            )
            .addOwnRoute("contact avatar", "fa fa-user", "contact-avatar", undefined)
            .addOwnRoute("signatures", "fa fa-pencil", "signatures", undefined)
            .addOwnRoute("signatures-landscape", "fa fa-pencil", "signatures-landscape", undefined)
            .addOwnRoute("link", "fa fa-external-link-alt", "link", undefined)
            .addOwnRoute("guidance", "fa fa-question-circle", "guidance", undefined)
            .addOwnRoute(
                "react-scroll",
                "fa fa-scroll",
                "react-scroll",
                undefined,
                "native react scroll"
            )
            /*.addMenu("back", "fa fa-arrow-left", () => {
            const parts = window.location.href.split('/')
            parts.pop();
            window.location.href = parts.join('/');
            }, true)*/
            .build()
    );
};

const MenuItemsTest: FC = () => (
    <>
        <ToggleMenuItem
            storageType="localStorage"
            storageKey="test-local"
            variant="fa-cog"
            title="local storage"
            onChange={enabled => console.log(`changed to: ${enabled}`)}
        />
        <ToggleMenuItem
            storageType="sessionStorage"
            storageKey="test-session"
            variant="checkbox"
            title="session storage"
            onChange={enabled => console.log(`changed to: ${enabled}`)}
        />
    </>
);

const ComponentElementsAppBar: FC = () => {
    return (
        <AppBarContextProvider>
            <ComponentElementsAppBarBase />
        </AppBarContextProvider>
    );
};

const ComponentElementsAppBarBase: FC = () => {
    const {path} = useRouteMatch();
    const history = useHistory();

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return (
        <AppBarBase
            appColour={ctx.appColour}
            title={"components"}
            right={<UserMenu extraMenuItems={ctx.extraMenuItems} />}
            onLogoClick={() => history.push(`${path}`)}
            drawerContent={<ComponentElementsMenuItems path={"/components/"} />}
        >
            <Box m={1}>
                <Switch>
                    <Route path={`${path}/select-list`}>
                        <BuildingSelectorTest />
                    </Route>
                    <Route path={`${path}/select-list-hierarchical`}>
                        <CareVisitItemOutcomeTest />
                    </Route>
                    <Route path={`${path}/inputs-editable`}>
                        <EditableInputsTest />
                    </Route>
                    <Route path={`${path}/inputs-select`}>
                        <SelectListTest />
                    </Route>
                    <Route path={`${path}/contact-avatar`}>
                        <AvatarImageTest />
                    </Route>
                    <Route path={`${path}/signatures`}>
                        <SignatureTest />
                    </Route>
                    <Route path={`${path}/signatures-landscape`}>
                        <SignatureTestLandscape />
                    </Route>
                    <Route path={`${path}/date-pickers`}>
                        <DatePickersTest />
                    </Route>
                    <Route path={`${path}/menu-items`}>
                        <MenuItemsTest />
                    </Route>
                    <Route path={`${path}/link`}>
                        <LinkTest />
                    </Route>
                    <Route path={`${path}/guidance`}>
                        <GuidanceTest />
                    </Route>
                    <Route path={`${path}/react-scroll`}>
                        <ReactScrollTest />
                    </Route>
                    <Route exact path={`${path}`}>
                        <div />
                    </Route>
                </Switch>
            </Box>
        </AppBarBase>
    );
};

const Menu: FC<{path: string}> = props => {
    const offline = useIsOffline();

    return new SidebarMenuBuilder(props.path)
        .addOwnRoute("components", "fa fa-puzzle-piece", "components", undefined, "low-level stuff")
        .addDivider("1")
        .addOwnRoute("evidence", "fa fa-star", "evidence", undefined, "evidence stuff")
        .addOwnRoute("rota", "fa fa-calendar", "rota", undefined, "rota stuff")
        .addOwnRoute("admin", "fa fa-list", "admin", undefined, "admin stuff")
        .addDivider("1")
        .addOwnRoute("calendar", "fa fa-calendar", "cal")
        .addOwnRoute("dashboards", "fa fa-file", "dashboards")
        .addOwnRoute("search", "fa fa-search", "search", undefined, "search example")
        .addOwnRoute("address lookups", "fa fa-address-book", "address-lookups")
        .addOwnRoute("tasks", "fa fa-check-circle-o", "tasks", undefined, "task stuff")
        .addOwnRoute(
            "schema driven lists",
            "fa fa-file",
            "schema-driven",
            undefined,
            "and forms, actions"
        )
        .addOwnRoute("table", "fa fa-table", "table", undefined)
        .addOwnRoute(
            "form definitions",
            "fa fa-file",
            "formDefinitions",
            undefined,
            "custom form stuff"
        )
        .addOwnRoute("cmd forms", "fa fa-file", "forms", undefined, "with commands")
        .addOwnRoute("modal forms", "fa fa-file", "modal-forms", undefined, "(i.e. not commands)")
        .addOwnRoute("printing", "fa fa-print", "printing")
        .addOwnRoute("login", "fa fa-user", "login")
        .addOwnRoute("loggedOut", "fa fa-user", "loggedOut")
        .addOwnRoute("error handling", "fa fa-warning", "errors")
        .addOwnRoute("promises", "fa fa-clock-o", "promises")
        .addMenu(
            offline ? "we're offline" : "we're online",
            offline ? "fa fa-times-circle-o" : "fa fa-check-circle-o",
            () => {}
        )
        .addComponent(() => <AutoOfflineToggle />, window.location.hostname === "localhost")
        .build();
};

const App: FC = () => {
    return (
        <AppBarContextProvider>
            <AppBase />
        </AppBarContextProvider>
    );
};

function AppBase() {
    // const [title, setTitle] = useState<ReactElement|null>(null);

    const history = useHistory();

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return (
        <AppBarBase
            right={<UserMenu />}
            // title={title || "home"}
            appColour={ctx.appColour}
            title="home"
            drawerContent={<Menu path="/" />}
            onLogoClick={() => history.push("/")}
        >
            <Box m={1}>
                <Switch>
                    <Route path="/cal">
                        <Calendar />
                    </Route>
                    <Route path="/dashboards">
                        <DashboardDemo />
                    </Route>
                    <Route path="/search">
                        <SearchTest />
                    </Route>
                    <Route path="/schema-driven">
                        <SchemaDrivenTest />
                    </Route>
                    <Route path="/table">
                        <TableTest />
                    </Route>
                    <Route path="/forms">
                        <ExampleModalCommandForm />
                        <ExampleEmbeddedCommandForm />
                    </Route>
                    <Route path="/modal-forms">
                        <ModalFormsPage />
                    </Route>
                    <Route path="/formDefinitions">
                        <FormDefinitionsTest />
                    </Route>
                    <Route path="/address-lookups">
                        <AddressLocationTest />
                    </Route>
                    <Route path="/tasks">
                        <TaskTest />
                    </Route>
                    <Route path="/printing">
                        <PrintingTest />
                    </Route>
                    <Route path="/login">
                        <LoginDialog onSubmit={creds => console.log(creds)} />
                    </Route>
                    <Route path="/loggedOut">
                        <LoggedOut />
                    </Route>
                    <Route path="/errors">
                        <ErrorBoundary>
                            <Box>{() => new Error("died here")}</Box>
                        </ErrorBoundary>
                    </Route>
                    <Route path="/promises">
                        <PromiseTest />
                    </Route>
                    <Route exact path="/">
                        <div>select something</div>
                    </Route>
                    <RouteFallbackWithDiagnostics />
                </Switch>
            </Box>
        </AppBarBase>
    );
}

interface PageProps {
    readonly appPath: string;
}

const Page = ({appPath}: PageProps) => {
    console.debug(`BrowserRouter basename=${appPath}`);

    return handleLazy(
        <BrowserRouter basename={appPath}>
            <Switch>
                {/* Pick up routes that are context relative (e.g. buildings) */}
                <Route path={["/components"]}>
                    <ComponentElementsAppBar />
                </Route>
                <Route path={["/evidence"]}>
                    <EvidenceComponentAppBar />
                </Route>
                <Route path={["/rota"]}>
                    <RotaAppBar />
                </Route>
                <Route path={["/admin"]}>
                    <AdminComponentAppBar />
                </Route>
                <Route path={"/"}>
                    <App />
                </Route>
            </Switch>
        </BrowserRouter>
    );
};

export interface AppOptions {
    readonly appPath: string;
}

export function start({appPath}: AppOptions): void {
    ReactDOM.render(
        <AsyncSessionData promiseFn={sessionDataFn}>
            <ServicesContextProvider client={client}>
                <EccoTheme prefix="app">
                    <Page appPath={appPath} />
                </EccoTheme>
            </ServicesContextProvider>
        </AsyncSessionData>,
        document.getElementById("root")!
    );

    // If you want your app to work offline and load faster, you can change
    // unregister() to register() below. Note this comes with some pitfalls.
    // Learn more about service workers: https://create-react-app.dev/docs/making-a-progressive-web-app/
    serviceWorker.unregister(); // We don't want offline for test app
}
