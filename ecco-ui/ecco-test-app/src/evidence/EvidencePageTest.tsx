import * as React from "react";
import {FC} from "react";
import {Command} from "ecco-commands";
import {configResolver, serviceType, sessionData} from "./testUtils";
import {
    EvidencePage,
    EvidencePageSetup,
    evidencePageReducer,
    EvidencePageLoaderForCommandForm,
    EvidenceDef,
    EvidencePageData
} from "ecco-evidence";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {CommandForm} from "ecco-components";
import {CommandFormTest, CommandFormTestOutput} from "ecco-components/cmd-queue/testUtils";


const EvidencePageTestLayout: FC<{cmdEmitted?: Command[] | undefined}> = props => {
    return (
        <>
            <EvidencePage />
            <CommandFormTestOutput cmdEmitted={props.cmdEmitted || []} />
        </>
    );
};

export const EvidencePageTest = (srId: number, taskName: string) => {
    return (
        <EvidencePageSetup
            initData={testEvidencePageData(srId, taskName)}
            actions={evidencePageReducer}
        >
            <EvidencePageTestLayout />
        </EvidencePageSetup>
    );
};

/**
 * Test component using commands and optional modal
 * modal gives us the ability to have sticky save/cancel buttons
 */
export const EvidencePageCommandFormTest: FC<{srId: number; taskName: string}> = props => {
    return (
        <CommandFormTest>
            {(form: CommandForm, cmdEmitted: Command[]) => (
                <EvidencePageLoaderForCommandForm srId={props.srId} taskName={props.taskName}>
                    <EvidencePageTestLayout cmdEmitted={cmdEmitted} />
                </EvidencePageLoaderForCommandForm>
                /*
                    -- Use this for hard-coded data --
                    <EvidencePageSetupForCommandForm
                        initData={testEvidencePageData(props.srId, props.taskName)}
                        actions={evidencePageReducer}>
                        <EvidencePageTestLayout cmdEmitted={cmdEmitted} />
                    </EvidencePageSetupForCommandForm>
                    */
            )}
        </CommandFormTest>
    );
};

/**
 * Default init data.
 * Unlike SmartStepTest, where the test creates/holds the props, the EvidencePageContextProvider
 * creates/holds the props, so unless we use the provider directly, we create an EvidencePageData
 * NB We could use a real API
 * NB We could also mock the actions by replacing evidencePageReducer above
 */
function testEvidencePageData(srId: number, taskName: string): EvidencePageData {
    return {
        sessionData: sessionData,
        serviceId: 1,
        taskName: taskName,
        evidenceDef: EvidenceDef.fromTaskName(sessionData, serviceType, taskName), // refactor out
        configResolver: configResolver(), // refactor out
        workUuid: Uuid.randomV4(),
        workUuid2: Uuid.randomV4(),
        serviceRecipientId: srId,
        readOnly: false,
        comment: undefined,
        workDate: undefined,
        commentTypeId: undefined,
        commentTypes: [
            {id: 1, name: "item 1"},
            {id: 2, name: "item 2"}
        ],
        clientStatusId: undefined,
        meetingStatusId: undefined,
        supportActions: [],
        questionAnswers: []
    };
}
