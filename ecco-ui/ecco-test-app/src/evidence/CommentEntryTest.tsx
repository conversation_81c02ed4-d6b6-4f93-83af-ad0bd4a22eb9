import * as React from "react";
import {FC, useState} from "react";
import {Paper} from "@eccosolutions/ecco-mui";
import {
    SessionData,
    SessionDataDto,
    FeatureSetDto,
    EvidencePageType,
    EvidenceGroup
} from "ecco-dto";
import {Command} from "ecco-commands";
import {
    CommentEntry,
    CommentEntryData,
    CommentEntryInit,
    CommentEntryState,
    EvidenceDef
} from "ecco-evidence";
import {configResolver} from "./testUtils";
import {Uuid} from "@eccosolutions/ecco-crypto";

/**
 * Layout of the QuestionAnswer with debug info
 */
const CommentEntryTestLayout: FC<{cmdEmitted?: Command | undefined}> = props => {
    // create/hold the props
    const init: CommentEntryInit = testCommentEntryInit();
    const [state, setState] = useState<CommentEntryState>(init.initState);
    const stateSetter = (update: Partial<CommentEntryState>) => {
        setState({...state, ...update});
    };

    return (
        <>
            <Paper elevation={2}>
                <CommentEntry init={init} state={state} stateSetter={stateSetter} />
            </Paper>
            <Paper elevation={2}>
                <p>state: {JSON.stringify(state)}</p>
                <p>emitted cmd (if using CommandForm): {JSON.stringify(props.cmdEmitted)}</p>
            </Paper>
        </>
    );
};

export const CommentEntryTest: FC = () => {
    return (
        <>
            <CommentEntryTestLayout />
        </>
    );
};

export const testCommentEntryInit = () => {
    const sessionDataDto: SessionDataDto = {} as SessionDataDto;
    sessionDataDto.featureSets = {global: {} as FeatureSetDto};
    sessionDataDto.listDefinitions = {};

    const srId = 99;

    const evidenceDef = new EvidenceDef(
        "needsAssessment",
        EvidencePageType.assessment,
        EvidenceGroup.needs,
        undefined
    );

    const initData: CommentEntryData = {
        sessionData: new SessionData(sessionDataDto),
        comment: null,
        workDate: null,
        commentTypeId: null,
        commentTypes: [
            {id: 1, name: "item 1"},
            {id: 2, name: "item 2"}
        ],
        serviceRecipientId: srId,
        clientStatusId: null,
        meetingStatusId: null,
        evidenceDef: evidenceDef,
        configResolver: configResolver(),
        getWorkUuid: () => Uuid.randomV4()
    };

    const initState: CommentEntryState = {
        comment: undefined,
        workDate: undefined,
        commentTypeId: undefined,
        clientStatusId: undefined,
        meetingStatusId: undefined
    };

    const ref: CommentEntryInit = {
        initData: initData,
        initState: initState
    };
    return ref;
};
