{"name": "ecco-test-app", "version": "0.1.0", "private": true, "dependencies": {"@eccosolutions/ecco-common": "2.0.0", "@eccosolutions/ecco-crypto": "1.0.2", "@eccosolutions/ecco-mui": "^0.0.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^7.1.2", "@types/node": "^14.18.12", "@types/react": "^16.9.56", "@types/react-dom": "^16.9.24", "@types/react-router": "^5.1.5", "@types/react-router-dom": "^5.1.3", "application-properties": "0.0.0", "ecco-admin": "^0.0.0", "ecco-calendar": "^0.0.0", "ecco-commands": "^0.0.0", "ecco-components-core": "0.0.0", "ecco-components": "^0.0.0", "ecco-dto": "^0.0.0", "ecco-evidence": "^0.0.0", "ecco-finance": "^0.0.0", "ecco-incidents": "^0.0.0", "ecco-repairs": "^0.0.0", "ecco-forms": "^0.0.0", "ecco-spa-global": "0.0.0", "font-awesome": "^4.3.0", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0"}, "devDependencies": {"@jest/globals": "29.7.0", "@testing-library/react": "^12.1.4", "@types/json-schema": "^7.0.11", "babel-eslint": "^10.1.0", "ecco-webpack-config": "0.0.0", "eslint": "^7.11.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "enzyme": "^3.6.0", "enzyme-adapter-react-16": "^1.5.0", "enzyme-to-json": "^3.3.4", "fake-indexeddb": "^3.1.7", "rimraf": "5.0.10", "typescript": "5.8.3"}, "scripts": {"analyze": "ecco-webpack --env visualize", "build": "ecco-webpack", "clean": "<PERSON><PERSON><PERSON> build", "emit": "ecco-webpack", "fix": "tsc -p src -p tests && eslint --fix src tests", "lint": "tsc -p src -p tests && eslint src tests", "lint-strict": "tsc -p src -p tests && eslint --max-warnings 0 src tests", "start": "ecco-webpack serve --env publicUrl=/ --open", "test": "yarn test-parallel-safe && yarn test-sequential", "test-parallel-safe": "echo Should be ecco-jest but not working", "test-sequential": "echo Nothing to do"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}