import {
    button,
    checkBox,
    datePickerInput,
    dropdownList,
    EccoV3Modal,
    textInput,
    timePicker
} from "ecco-components-core";
import {
    CommandForm,
    CommandSubform,
    CommandSubformProps,
    useCommandForm,
    useEvents,
    useServicesContext,
    useWorkersWithSameAccess
} from "ecco-components";
import * as React from "react";
import {ClassAttributes} from "react";
import {BaseUpdateCommandTransitioning, CommandQueue, CommandSource} from "ecco-commands";
import {bus, EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";
import {listDefToIdName, SessionData} from "ecco-dto";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {EventAttendee, EventResourceDto} from "ecco-dto";
import {
    CalendarEntryCommand,
    ContactCalendarEntryCommand,
    ServiceRecipientCalendarEntryCommand
} from "ecco-commands";
import {PersonUserSummary} from "ecco-dto";
import {Grid} from "@eccosolutions/ecco-mui";
import {differenceBy} from "lodash";

// NB reload is handles by closeEvent below
export const CalendarEvent = bus<void>();

/**
 * LOAD FORM - jsx
 * This top level function assumes the caller has loaded the session data and srId - otherwise you'll get nothing.
 */
export const CalendarEntryEditor = (props: {
    contactId?: number | undefined;
    serviceRecipientId?: number | undefined;
    eventUuid?: string | undefined;
    newEntryDateTime?: EccoDateTime | undefined;
    newEntryAllDay?: boolean | undefined;
    formRef?: ((e: CalendarEntry) => void) | undefined;
}) => {
    const commandForm = useCommandForm();
    return (
        <EccoV3Modal
            title={"calendar entry"}
            saveEnabled={true}
            show={true}
            action={"save"}
            onCancel={() => commandForm.cancelForm()}
            onSave={() => commandForm.submitForm()}
        >
            <CalendarEntrySubform
                eventUuid={props.eventUuid || null}
                formRef={props.formRef || null}
                commandForm={commandForm}
                contactId={props.contactId}
                serviceRecipientId={props.serviceRecipientId}
                newEntryDateTime={props.newEntryDateTime}
                newEntryAllDay={props.newEntryAllDay}
            />
        </EccoV3Modal>
    );
};

/**
 * This is concerned about rendering the form - with all the data is required
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
const CalendarEntrySubform = (props: {
    eventUuid: string | null;
    formRef: ((c: CalendarEntry) => void) | null;
    commandForm: CommandForm;
    contactId?: number | undefined;
    serviceRecipientId?: number | undefined;
    newEntryDateTime?: EccoDateTime | undefined;
    newEntryAllDay?: boolean | undefined;
}) => {
    const {
        eventUuid,
        formRef,
        commandForm,
        serviceRecipientId,
        contactId,
        newEntryDateTime,
        newEntryAllDay
    } = props;
    const {sessionData} = useServicesContext();
    const {appointments} = useEvents(eventUuid ? [eventUuid] : null);
    const {staffWithSameAccess} = useWorkersWithSameAccess(false);

    const loaded = sessionData != null && appointments != null && staffWithSameAccess != null;
    const appointment = appointments && appointments[0];
    // this may as well be false always
    const readOnly = newEntryDateTime
        ? !newEntryDateTime
        : appointment
          ? appointment.evidenceWorkUuid != null
          : false; // new entry as no date yet
    return loaded ? (
        <CalendarEntry
            ref={formRef}
            serviceRecipientId={serviceRecipientId}
            contactId={contactId}
            eventResource={appointment || null}
            newEntryDateTime={newEntryDateTime}
            newEntryAllDay={newEntryAllDay}
            workersWithAccessEnabled={staffWithSameAccess || []}
            readOnly={readOnly}
            sessionData={sessionData}
            commandForm={commandForm}
            onDelete={() => commandForm.submitForm()}
        />
    ) : null;
};

/**
 * FORM (independent of any data loading)
 */
interface Props extends ClassAttributes<CalendarEntry> {
    readOnly: boolean;
    eventResource: EventResourceDto | null;
    workersWithAccessEnabled: PersonUserSummary[];
    contactId?: number | undefined;
    serviceRecipientId?: number | undefined;
    newEntryDateTime?: EccoDateTime | undefined;
    newEntryAllDay?: boolean | undefined;
    sessionData: SessionData;
    onDelete?: (() => void) | undefined;
}

type EventResourceField = keyof EventResourceDto;

interface State {
    title: string | null;
    location: string | null;
    eventCategoryId: number | null;
    allDay: boolean | null;
    start: EccoDate | null;
    startTime: string | null;
    end: EccoDate | null;
    endTime: string | null;
    repeatEveryMon: boolean;
    repeatEveryTues: boolean;
    repeatEveryWed: boolean;
    repeatEveryThurs: boolean;
    repeatEveryFri: boolean;
    repeatEverySat: boolean;
    repeatEverySun: boolean;
    repeatEveryWeeks: number | null;
    repeatEnd: EccoDate | null;
    repeatDisable: boolean;
    requiredFields: EventResourceField[];
    optionalFields: EventResourceField[];
    delete: boolean;
    attendees: EventAttendee[] | null;
    attendeeNew: number | null;
    workersWithAccessEnabled: PersonUserSummary[];
}

class CalendarEntry extends CommandSubform<Props, State> implements CommandSource {
    private cmdUuid = Uuid.randomV4();

    constructor(props: Readonly<Props & CommandSubformProps>) {
        super(props);

        const e = this.props.eventResource;
        const initStartDate: undefined | EccoDate =
            e && e.start
                ? EccoDateTime.parseIso8601(e.start as string).toEccoDate()
                : this.props.newEntryDateTime && this.props.newEntryDateTime.toEccoDate();
        const allDay = e
            ? e.allDay
            : this.props.newEntryDateTime
            ? this.props.newEntryAllDay
            : true;
        const initStartTime: EccoTime | undefined = allDay
            ? undefined
            : e && e.start
            ? EccoDateTime.parseIso8601(e.start as string).toEccoTime()
            : this.props.newEntryDateTime?.toEccoTime();
        const eEnd: EccoDateTime | null =
            (e && e.end && EccoDateTime.parseIso8601(e.end as string)) || null;
        this.state = {
            title: e && e.title,
            location: (e && e.location) || null,
            eventCategoryId: e && e.eventCategoryId,
            allDay: allDay || null,
            start: initStartDate || null, // this is iso when from server (see Timespan)
            startTime: initStartTime ? initStartTime.formatHoursMinutes() : null,
            end: eEnd && eEnd.toEccoDate(), // this is iso when from server (see Timespan)
            endTime: eEnd && eEnd.toEccoTime().formatHoursMinutes(), // this is iso when from server (see Timespan)
            attendees: e && e.attendees && e.attendees.length > 0 ? e.attendees : [],
            workersWithAccessEnabled: props.workersWithAccessEnabled,
            attendeeNew: null,
            repeatDisable: false,
            repeatEveryMon: false,
            repeatEveryTues: false,
            repeatEveryWed: false,
            repeatEveryThurs: false,
            repeatEveryFri: false,
            repeatEverySat: false,
            repeatEverySun: false,
            repeatEveryWeeks: null,
            repeatEnd: null,
            requiredFields: ["start"],
            optionalFields: [],
            delete: false
        };
    }

    override componentDidUpdate(prevProps: Props, prevState: State) {
        if (this.state.delete) {
            this.props.onDelete && this.props.onDelete();
        }
        // Typical usage (don't forget to compare props):
        if (this.state.attendeeNew) {
            const convertToAttendee = (id: number) => {
                const p = this.props.workersWithAccessEnabled.filter(p => p.id == id)[0];
                const a: EventAttendee = {
                    name: p.name,
                    email: null,
                    required: false,
                    status: null, // AttendeeStatus
                    calendarIdUserReferenceUri: p.calendarIdUserReferenceUri,
                    calendarId: p.calendarId
                };
                return a;
            };
            const attendeesNext = prevState.attendees
                ? [...prevState.attendees, convertToAttendee(this.state.attendeeNew)]
                : [convertToAttendee(this.state.attendeeNew)];
            this.setState({
                attendees: attendeesNext,
                attendeeNew: null,
                workersWithAccessEnabled: differenceBy(
                    this.props.workersWithAccessEnabled,
                    attendeesNext,
                    "calendarIdUserReferenceUri"
                )
            });
        }
    }

    emitChangesTo(commandQueue: CommandQueue) {
        this.queueCalendarEntryCommand(commandQueue);
    }

    getErrors(): string[] {
        const err = [];
        if (!this.state.title && !this.state.eventCategoryId) {
            if (this.props.serviceRecipientId) {
                err.push("title or category required");
            } else {
                err.push("title required");
            }
        }
        if (!this.state.start) {
            err.push("start date required");
        }
        if (!this.state.allDay) {
            if (!this.state.startTime) {
                err.push("start time required");
            }
            if (!this.state.endTime) {
                err.push("end time required");
            }
        }

        if (!this.state.allDay && err.length == 0) {
            const startTime = EccoTime.parseIso8601(this.state.startTime!);
            const start = startTime && this.state.start!.toDateTime(startTime);
            const endTime = EccoTime.parseIso8601(this.state.endTime!);
            const end = this.state.end
                ? this.state.end.toDateTime(endTime)
                : this.state.start!.toDateTime(endTime);
            if (start.laterThan(end)) {
                err.push("start is after end");
            }
        }

        if (this.state.repeatEnd != null) {
            if (this.state.repeatEveryWeeks == null) {
                err.push("every week(s) is required");
            }
            if (this.state.end != null && this.state.end.laterThan(this.state.repeatEnd)) {
                err.push("ending is before end");
            }
            if (this.state.start != null && this.state.start.laterThan(this.state.repeatEnd)) {
                err.push("ending is before start");
            }
        }

        return err;
    }

    protected queueCalendarEntryCommand(commandQueue: CommandQueue) {
        this.state.delete
            ? this.queueDeleteCalendarEntryCommand(commandQueue)
            : this.queueUpdateCalendarEntryCommand(commandQueue);
    }

    private generateCommandWithParent(commandQueue: CommandQueue, cmdEntry: CalendarEntryCommand) {
        let cmd: BaseUpdateCommandTransitioning | null = null;
        if (this.props.serviceRecipientId) {
            cmd = new ServiceRecipientCalendarEntryCommand(
                this.cmdUuid,
                this.props.serviceRecipientId,
                cmdEntry
            );
        }
        if (this.props.contactId) {
            cmd = new ContactCalendarEntryCommand(this.cmdUuid, this.props.contactId, cmdEntry);
        }
        if (cmd && cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    private queueDeleteCalendarEntryCommand(commandQueue: CommandQueue) {
        let cmdEntry = new CalendarEntryCommand("remove", this.props.eventResource!.uid);
        this.generateCommandWithParent(commandQueue, cmdEntry);
    }

    private queueUpdateCalendarEntryCommand(commandQueue: CommandQueue) {
        const eIn = this.props.eventResource;
        const inStart =
            eIn && eIn.start ? EccoDateTime.parseIso8601(eIn.start as string).toEccoDate() : null;
        const inStartTime =
            eIn && eIn.start ? EccoDateTime.parseIso8601(eIn.start as string).toEccoTime() : null;
        const inEnd =
            eIn && !eIn.allDay ? EccoDateTime.parseIso8601(eIn.end as string).toEccoDate() : null;
        const inEndTime =
            eIn && !eIn.allDay ? EccoDateTime.parseIso8601(eIn.end as string).toEccoTime() : null;

        // build an integer according to DaysOfWeek.java (SUN = 1)
        let repeatEveryDays = 0;
        repeatEveryDays += this.state.repeatEverySun ? 1 : 0;
        repeatEveryDays += this.state.repeatEveryMon ? 2 : 0;
        repeatEveryDays += this.state.repeatEveryTues ? 4 : 0;
        repeatEveryDays += this.state.repeatEveryWed ? 8 : 0;
        repeatEveryDays += this.state.repeatEveryThurs ? 16 : 0;
        repeatEveryDays += this.state.repeatEveryFri ? 32 : 0;
        repeatEveryDays += this.state.repeatEverySat ? 64 : 0;

        const repeatEveryWeeks = this.state.repeatDisable ? 0 : this.state.repeatEveryWeeks;

        let cmdEntry = new CalendarEntryCommand(eIn ? "update" : "add", eIn && eIn.uid)
            .changeTitle(eIn && eIn.title, this.state.title)
            .changeEventCategoryId(eIn && eIn.eventCategoryId, this.state.eventCategoryId)
            .changeStartDate(inStart, this.state.start)
            .changeStartTime(inStartTime && inStartTime.formatHoursMinutes(), this.state.startTime)
            .changeEndDate(inEnd, this.state.end)
            .changeEndTime(inEndTime && inEndTime.formatHoursMinutes(), this.state.endTime)
            .changeAllDay(eIn && eIn.allDay, this.state.allDay)
            .changeRepeatEveryDays(null, repeatEveryDays)
            .changeRepeatEveryWeeks(null, repeatEveryWeeks)
            .changeRepeatEndDate(null, this.state.repeatEnd)
            .changeAttendees(eIn && eIn.attendees, this.state.attendees);

        this.generateCommandWithParent(commandQueue, cmdEntry);
    }

    override render() {
        const setter = (state: State) => this.setState(state);
        const EVENTCATEGORY_LISTNAME = "eventCategory"; // as per Java

        const RecurringFreq = dropdownList(
            "every week(s)",
            state => this.setState(state),
            this.state,
            "repeatEveryWeeks",
            [
                {id: 1, name: "1", disabled: false},
                {id: 2, name: "2", disabled: false},
                {id: 3, name: "3", disabled: false},
                {id: 4, name: "4", disabled: false},
                {id: 8, name: "8", disabled: false},
                {id: 12, name: "12", disabled: false}
            ],
            undefined,
            undefined,
            this.props.readOnly
        );

        const RecurringDisable = checkBox(
            "repeatDisable",
            "stop repeating from this event",
            state => this.setState(state),
            this.state,
            this.props.readOnly
        );

        const NewAttendees = dropdownList(
            "add attendee",
            state => this.setState(state),
            this.state,
            "attendeeNew",
            this.state.workersWithAccessEnabled,
            undefined,
            undefined,
            this.props.readOnly
        );

        const recurringAllowed = this.props.eventResource == null;

        return (
            <>
                <Grid container>
                    <Grid item xs={12}>
                        {textInput(
                            "title",
                            "title",
                            setter,
                            this.state,
                            undefined,
                            this.props.readOnly,
                            true
                        )}
                    </Grid>
                    {this.props.serviceRecipientId && (
                        <Grid item xs={12}>
                            {dropdownList(
                                "category",
                                setter,
                                this.state,
                                "eventCategoryId",
                                this.props.sessionData
                                    .getListDefinitionEntriesByListName(EVENTCATEGORY_LISTNAME)
                                    .map(ld => listDefToIdName(ld)),
                                {},
                                undefined,
                                this.props.readOnly
                            )}
                        </Grid>
                    )}
                    <Grid item xs={4}>
                        {datePickerInput(
                            "start",
                            "start",
                            setter,
                            this.state,
                            this.props.readOnly,
                            this.isRequired("start")
                        )}
                        {!this.state.allDay &&
                            timePicker(
                                "startTime",
                                "time",
                                setter,
                                this.state,
                                this.props.readOnly,
                                this.isRequired("start")
                            )}
                    </Grid>
                    <Grid item xs={4}>
                        {checkBox("allDay", "all day(s)", setter, this.state, this.props.readOnly)}
                    </Grid>
                    <Grid item xs={4}>
                        {datePickerInput(
                            "end",
                            "end",
                            setter,
                            this.state,
                            this.props.readOnly,
                            this.isRequired("end")
                        )}
                        {!this.state.allDay &&
                            timePicker(
                                "endTime",
                                "time",
                                setter,
                                this.state,
                                this.props.readOnly,
                                !this.state.allDay
                            )}
                    </Grid>
                    <Grid item xs={12}>
                        {/*{checkBoxNoGroup("repeatEverySun", "sun", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEveryMon", "mon", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEveryTues", "tues", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEveryWed", "wed", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEveryThurs", "thurs", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEveryFri", "fri", state => this.setState(state), this.state)}*/}
                        {/*{checkBoxNoGroup("repeatEverySat", "sat", state => this.setState(state), this.state)}*/}

                        {/*new: allow all*/}
                        {/*not new: recurring - stop; not recurring - disallow*/}
                        {recurringAllowed
                            ? RecurringFreq
                            : this.props.eventResource!.recurrence
                            ? RecurringDisable
                            : null}
                    </Grid>
                    {recurringAllowed && (
                        <Grid item xs={12}>
                            {datePickerInput(
                                "repeatEnd",
                                "ending",
                                setter,
                                this.state,
                                this.props.readOnly,
                                false
                            )}
                        </Grid>
                    )}
                    <Grid item xs={12}>
                        {NewAttendees}
                    </Grid>
                    <Grid item xs={12}>
                        attendees:{" "}
                        {this.state.attendees
                            ?.filter(a => !!a) // avoid empty array
                            .map(a => (
                                <span
                                    key={a.calendarIdUserReferenceUri}
                                    style={{paddingLeft: "5px", paddingRight: "5px"}}
                                >
                                    &nbsp;{a.name}&nbsp;
                                </span>
                            ))}
                    </Grid>
                    <Grid item xs={3}>
                        <br />
                        <br />
                        {this.props.eventResource
                            ? !this.props.eventResource.evidenceWorkUuid &&
                              button("delete", () => this.setState({delete: true}), "danger")
                            : null}
                    </Grid>
                </Grid>
            </>
        );
    }

    private isRequired(fieldName: EventResourceField) {
        return this.state.requiredFields.indexOf(fieldName) >= 0;
    }

    private isOptional(fieldName: EventResourceField) {
        return this.state.optionalFields.indexOf(fieldName) >= 0;
    }
}
