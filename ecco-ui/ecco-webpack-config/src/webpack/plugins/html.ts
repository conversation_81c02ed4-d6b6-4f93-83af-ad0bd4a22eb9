import {ResolvedOptions} from "../../options/options";
import HtmlWebpackPlugin from "html-webpack-plugin";

export function htmlWebpackPlugin(options: ResolvedOptions) {
    return new HtmlWebpackPlugin({
        inject: true,
        template: options.paths.html,
        templateParameters: options.clientEnv,
        ...(options.mode === "production"
            ? {
                  minify: {
                      removeComments: true,
                      collapseWhitespace: true,
                      removeRedundantAttributes: true,
                      useShortDoctype: true,
                      removeEmptyAttributes: true,
                      removeStyleLinkTypeAttributes: true,
                      keepClosingSlash: true,
                      minifyJS: true,
                      minifyCSS: true,
                      minifyURLs: true
                  }
              }
            : {})
    });
}
