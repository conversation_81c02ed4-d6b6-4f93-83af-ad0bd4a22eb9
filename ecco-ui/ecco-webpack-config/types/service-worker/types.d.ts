declare const process: {
    readonly env: {
        readonly [key: string]: string;
        readonly NODE_ENV: string;
        readonly PUBLIC_URL: string;
    };
};

declare module "*.css" {
    // src/webpack/loaders/css.ts
    const classes: {readonly [key: string]: string};
    export default classes;
}

declare module "*.scss" {
    // src/webpack/loaders/css.ts
    const classes: {readonly [key: string]: string};
    export default classes;
}

declare module "*.sass" {
    // src/webpack/loaders/css.ts
    const classes: {readonly [key: string]: string};
    export default classes;
}

declare module "*.avif" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.bmp" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.gif" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.jpg" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.jpeg" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.png" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.mp3" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.mp4" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.oga" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.ogg" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.ogv" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.svg" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.webm" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.webp" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}

declare module "*.html" {
    // src/webpack/loaders/file.ts
    const src: string;
    export default src;
}
