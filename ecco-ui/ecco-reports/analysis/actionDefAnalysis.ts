import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {EccoDateTime} from "@eccosolutions/ecco-common";
import tableRepresentations = require("../tables/predefined-table-representations");
import types = require("./types");
import Analyser = types.Analyser;
import Group = types.Group;
import GroupedAnalysis = types.GroupedAnalysis;
import ReferralAggregate = types.ReferralAggregate;
import SequenceAnalysis = types.SequenceAnalysis;
import Transformer = types.Transformer;
import ActionDefWithRefToReferralAggregate = types.ActionDefWithRefToReferralAggregate;
import extractPair = types.extractPair;
import GroupFn = types.GroupFn;
import {SupportWork, SupportAction} from "ecco-dto";
import smartStepCount = require("./smartStepCount");
import {AnalysisContext} from "../chart-domain";
import {SmartStepStatusName} from "ecco-dto";
import {referralAggregateReportItemColumns} from "../tables/predefined-table-representations";
import {
    columnMap,
    dateTimeColumn,
    fixedPrecisionNumberColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {smartStepOnlyColumns} from "./workCommonAnalysis";
import {supportWorkColumns} from "./workAnalysis";

//*********************************
// EXPORT group analyser
// ActionDefWithRefToReferralAggregate to ActionDefWithRefToReferralAggregate_Group

/** Table breakdown */
const actionDefCountColumns = columnMap(
    // use one of these
    textColumn<ActionDefWithRefToReferralAggregate_Group>("key", row => row.key),
    textColumn<ActionDefWithRefToReferralAggregate_Group>("a-id", row => row.key),

    textColumn<ActionDefWithRefToReferralAggregate_Group>("outcome", (row, ctx) => {
        return ctx.getSessionData().getAnyActionById(Number(row.key)).getOutcome().getName();
    }),
    textColumn<ActionDefWithRefToReferralAggregate_Group>("smart step", (row, ctx) => {
        return ctx.getSessionData().getAnyActionById(Number(row.key)).getName();
    }),
    numberColumn<ActionDefWithRefToReferralAggregate_Group>("count", row => row.count),
    numberColumn<ActionDefWithRefToReferralAggregate_Group>(
        "totalAchievedSmartSteps",
        row => row.totalAchievedSmartSteps
    ),
    numberColumn<ActionDefWithRefToReferralAggregate_Group>(
        "totalOutstandingSmartSteps",
        row => row.totalOutstandingSmartSteps
    ),
    numberColumn<ActionDefWithRefToReferralAggregate_Group>(
        "achieved",
        row => row.totalAchievedSmartSteps
    ),
    numberColumn<ActionDefWithRefToReferralAggregate_Group>(
        "outstanding",
        row => row.totalOutstandingSmartSteps
    ),
    fixedPrecisionNumberColumn<ActionDefWithRefToReferralAggregate_Group>(
        "success %",
        0,
        row => row.percentSuccess
    )
);

/** result */
interface ActionDefWithRefToReferralAggregate_Group
    extends Group<ActionDefWithRefToReferralAggregate> {
    // since the incoming ActionDefWithRefToReferralAggregate is broken down into
    // a row per SupportAction
    count: number;
    totalAchievedSmartSteps: number;
    totalOutstandingSmartSteps: number;
    percentSuccess: number;
}

/** ungroup on click */
const wrapActionDefSequenceWithOriginalAnalyser: Analyser<
    Group<ActionDefWithRefToReferralAggregate>,
    Sequence<ActionDefWithRefToReferralAggregate>
> = function (
    ctx: AnalysisContext,
    input: Group<ActionDefWithRefToReferralAggregate>
): ActionDef_withRefTo_ReferralAggregate_Analysis {
    return new ActionDef_withRefTo_ReferralAggregate_Analysis(ctx, input.elements);
};

/** Stage Analysis */
class ActionDef_withRefTo_ReferralAggregate_GroupedAnalysis extends GroupedAnalysis<
    ActionDefWithRefToReferralAggregate,
    ActionDefWithRefToReferralAggregate_Group
> {
    constructor(ctx: AnalysisContext, data: Sequence<ActionDefWithRefToReferralAggregate_Group>) {
        super(ctx, data);
        this.recordRepresentation = {
            ActionDefCount: actionDefCountColumns
        };
        this.addOnClickAnalyser("ungroup", wrapActionDefSequenceWithOriginalAnalyser);
        this.addOnClickManyAnalysis("ungroup", ActionDef_withRefTo_ReferralAggregate_Analysis);
    }
}

/** Builder */

/////////////////////////////////
// HARD WIRED LOGGING
function loggedGroupActionDefWithRef(result: ActionDefWithRefToReferralAggregate_Group) {
    if (parseInt(result.key) == hardWiredLogActionsId) {
        console.log("buildActionDefGroupData log for specific actionId %o", result);
    }
    return result;
}
/////////////////////////////////

function buildActionDefGroupData(
    ctx: AnalysisContext,
    input: Sequence<ActionDefWithRefToReferralAggregate>,
    groupFn: GroupFn<ActionDefWithRefToReferralAggregate>
): Sequence<ActionDefWithRefToReferralAggregate_Group> {
    return groupFn(input, ctx).map(pair => {
        let elements: Sequence<ActionDefWithRefToReferralAggregate> = pair.elements;
        let smartStepsCount = new smartStepCount.SmartStepCount(elements);
        let result: ActionDefWithRefToReferralAggregate_Group = {
            key: pair.key,
            elements: pair.elements,
            count: pair.elements.flatten().size(), // count of supportAction's in each actionDef
            totalAchievedSmartSteps: smartStepsCount.getTotalAchievedSmartSteps(),
            totalOutstandingSmartSteps: smartStepsCount.getTotalOutstandingSmartSteps(),
            percentSuccess: smartStepsCount.getPercentSuccess()
        };
        loggedGroupActionDefWithRef(result);
        return result;
    });
}
function groupByOutcomeDef(
    input: Sequence<ActionDefWithRefToReferralAggregate>,
    ctx?: AnalysisContext
): Sequence<Group<ActionDefWithRefToReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                ctx?.getSessionData().getAnyActionById(inputElement.id).getOutcome().getName() || ""
        )
        .pairs()
        .map(extractPair);
}
function groupByActionDef(
    input: Sequence<ActionDefWithRefToReferralAggregate>
): Sequence<Group<ActionDefWithRefToReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.getId().toString())
        .pairs()
        .map(extractPair);
}
export var actionDefWithRACountsAnalyser: Transformer<
    ActionDefWithRefToReferralAggregate,
    ActionDefWithRefToReferralAggregate_Group
> = function (
    ctx: AnalysisContext,
    input: Sequence<ActionDefWithRefToReferralAggregate>
): SequenceAnalysis<ActionDefWithRefToReferralAggregate_Group> {
    return new ActionDef_withRefTo_ReferralAggregate_GroupedAnalysis(
        ctx,
        buildActionDefGroupData(ctx, input, groupByActionDef)
    );
};
export var outcomeDefWithRACountsAnalyser: Transformer<
    ActionDefWithRefToReferralAggregate,
    ActionDefWithRefToReferralAggregate_Group
> = function (
    ctx: AnalysisContext,
    input: Sequence<ActionDefWithRefToReferralAggregate>
): SequenceAnalysis<ActionDefWithRefToReferralAggregate_Group> {
    return new ActionDef_withRefTo_ReferralAggregate_GroupedAnalysis(
        ctx,
        buildActionDefGroupData(ctx, input, groupByOutcomeDef)
    );
};

//*********************************
// EXPORT analyser
// ReferralAggregate to ActionDefWithRefToReferralAggregate

function lookupActionStatusName(id: number) {
    let str = id ? SmartStepStatusName[id] : "-";
    return str;
}
/**
 * Table breakdown
 * Breakdown table of every supportAction referenced by actionDefId
 */
const actionDefColumns = columnMap(
    numberColumn<ActionDefWithRefToReferralAggregate>("a-id", row => row.getId()),
    // now use a: name
    textColumn<ActionDefWithRefToReferralAggregate>("a-name", row => row.supportAction.name),
    // now use a: status
    textColumn<ActionDefWithRefToReferralAggregate>("a-status", row =>
        lookupActionStatusName(row.supportAction.status)
    ),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "r-id",
        row => row.reportItem.referral.referralCode || row.reportItem.referral.referralId.toString()
    ),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "c-id",
        row => row.reportItem.referral.clientCode || row.reportItem.referral.clientId.toString()
    ),
    textColumn<ActionDefWithRefToReferralAggregate>("w-id", row => row.supportWork.id),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "client",
        row => row.reportItem.referral.clientDisplayName
    ),
    textColumn<ActionDefWithRefToReferralAggregate>("status now", row =>
        tableRepresentations.messageLookup(row.reportItem.referral.statusMessageKey)
    ),
    dateTimeColumn<ActionDefWithRefToReferralAggregate>("work date", row =>
        EccoDateTime.parseIso8601(row.supportWork.workDate)
    ),
    textColumn<ActionDefWithRefToReferralAggregate>("type", (row, ctx) =>
        ctx
            .getSessionData()
            .getListDefinitionEntryById(row.supportWork.commentTypeId)
            .getDisplayName()
    ),
    numberColumn<ActionDefWithRefToReferralAggregate>(
        "time (mins)",
        row => row.supportWork.minsSpent
    ),
    textColumn<ActionDefWithRefToReferralAggregate>("task", row =>
        tableRepresentations.evidenceTaskNameLookup(
            row.supportWork.taskName,
            row.supportWork.serviceAllocationId
        )
    ),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "service",
        (row, ctx) =>
            ctx
                .getSessionData()
                .getServiceCategorisation(row.reportItem.referral.serviceAllocationId).serviceName
    ),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "project",
        (row, ctx) =>
            ctx
                .getSessionData()
                .getServiceCategorisation(row.reportItem.referral.serviceAllocationId).projectName
    ),
    /*textColumn<ActionDefWithRefToReferralAggregate>(
        "region",
        row => row.reportItem.referral.currentRegionName
    ),*/
    textColumn<ActionDefWithRefToReferralAggregate>(
        "worker",
        row => row.reportItem.referral.supportWorkerDisplayName
    ),
    textColumn<ActionDefWithRefToReferralAggregate>(
        "exit reason",
        row => row.reportItem.referral.exitReason
    )
);
export var actionDefToReferral = joinNestedPathColumnMaps<
    ActionDefWithRefToReferralAggregate,
    ReferralAggregate
>("r", row => row.reportItem, referralAggregateReportItemColumns);
export var actionDefToSupportAction = joinNestedPathColumnMaps<
    ActionDefWithRefToReferralAggregate,
    SupportAction
>("a", row => row.supportAction, smartStepOnlyColumns);
export var actionDefToWork = joinNestedPathColumnMaps<
    ActionDefWithRefToReferralAggregate,
    SupportWork
>("w", row => row.supportWork, supportWorkColumns);
export const actionDefWithReferralColumns = joinColumnMaps(
    actionDefColumns,
    actionDefToReferral,
    actionDefToWork,
    actionDefToSupportAction
);

/**
 * Analysis class which shows what we can do with an ActionDefWithRefToReferralAggregate
 */
class ActionDef_withRefTo_ReferralAggregate_Analysis extends SequenceAnalysis<ActionDefWithRefToReferralAggregate> {
    constructor(ctx: AnalysisContext, data: Sequence<ActionDefWithRefToReferralAggregate>) {
        super(ctx, data, (item: ActionDefWithRefToReferralAggregate) => item.getId().toString());
        this.derivativeAnalysers = {
            actionDefCountsAnalyser: actionDefWithRACountsAnalyser,
            outcomeDefCountsAnalyser: outcomeDefWithRACountsAnalyser
        };
        this.recordRepresentation = {
            ActionDef: actionDefWithReferralColumns
        };
        //this.addOnClickAnalyser("ungroup", wrapWorkWithParentRefSequenceAnalyser);
        //this.addOnClickManyAnalysis("ungroup", ...
    }
}

/**
 * Builder
 * also see smartStepAnalysis.smartStepCountsReport
 */

/////////////////////////////////
// HARD WIRED LOGGING
const hardWiredLogActionsId = 38179;
function returnLoggedActionDefWithRef(
    ra: ReferralAggregate,
    supportWork: SupportWork,
    supportAction: SupportAction
): ActionDefWithRefToReferralAggregate {
    let result: Partial<ActionDefWithRefToReferralAggregate> = {
        reportItem: ra,
        supportWork: supportWork,
        supportAction: supportAction,
        initialText: null,
        id: supportAction.actionId,
        name: supportAction.name!,
        disabled: false,
        activityTypes: []
    };
    if (supportAction.actionId == hardWiredLogActionsId) {
        console.log(
            "buildActionDefWithRefToReferralAggregate log for specific actionId %o",
            result
        );
    }
    return result as ActionDefWithRefToReferralAggregate;
}
/////////////////////////////////

function buildActionDefWithRefToReferralAggregate(
    input: Sequence<ReferralAggregate>
): Sequence<ActionDefWithRefToReferralAggregate> {
    let allActionDefs: Sequence<ActionDefWithRefToReferralAggregate> = input
        .filter(ra => ra != null)
        .map(ra =>
            ra
                .supportWork! // non-null from filter above
                .filter(supportWork => supportWork != null && supportWork.actions != null)
                .map(supportWork =>
                    supportWork.actions
                        .filter(supportAction => supportAction != null)
                        .map(supportAction => {
                            // to populate the actionDef we could do this... but we get the name from the SupportAction
                            // var actionDef = lookupActionDef(actionDefId);
                            // var result = <ActionDefWithRefToReferralAggregate>actionDef;
                            // result.reportItem = ra;
                            // return result;
                            return returnLoggedActionDefWithRef(ra, supportWork, supportAction);
                        })
                )
        )
        .flatten<ActionDefWithRefToReferralAggregate>();
    return allActionDefs;
}

/**
 * Analyser to transform an incoming data structure (ReferralAggregate) to another (ActionDefWithRefToReferralAggregate)
 */
export var actionDef_withRefTo_ReferralAggregate_Analyser: Transformer<
    ReferralAggregate,
    ActionDefWithRefToReferralAggregate
> = function (
    ctx: AnalysisContext,
    input: Sequence<ReferralAggregate>
): SequenceAnalysis<ActionDefWithRefToReferralAggregate> {
    return new ActionDef_withRefTo_ReferralAggregate_Analysis(
        ctx,
        buildActionDefWithRefToReferralAggregate(input)
    );
};
