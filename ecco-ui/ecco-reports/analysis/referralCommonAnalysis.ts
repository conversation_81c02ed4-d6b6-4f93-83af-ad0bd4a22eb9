///<reference path="../../../ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/typings/lazy.d.ts"/>

import Lazy = require("lazy");
import types = require("./types");
import tableRepresentations = require("../tables/predefined-table-representations");
import Group = types.Group;
import ReferralAggregate = types.ReferralAggregate;
import extractPair = types.extractPair;
import Sequence = LazyJS.Sequence;
import {getGlobalEccoAPI} from "ecco-components";
import {AnalysisContext} from "../chart-domain"; // Note: LazyJS not Lazy, due to how module is weird.

function messageLookup(key: string) {
    const messages = getGlobalEccoAPI().sessionData.getMessages();
    // @ts-ignore
    return messages[key] || "[" + key + "]";
}

/** e.g. take ages, and quantise into 0-7, 8-15, 16-17, 18-64, 65 and over */
interface QuantiserFn {
    (value: number): string;
}

function defaultAgeGroupQuantiser(value: number): string {
    if (value < 16) {
        return "under 16";
    }
    if (value < 18) {
        return "16-17";
    }
    if (value < 25) {
        return "18-24";
    }
    if (value < 65) {
        return "25-64";
    }
    return "65 and over";
}

function getQuantisedAgeInYearsAtTimeOfReferral(item: ReferralAggregate): string | null {
    if (!item.client!.birthDate) {
        return "no birth date";
    }
    if (!item.referral.receivedDate) {
        return "no referral date";
    }
    const age = tableRepresentations.getAgeInYearsAtTimeOfReferral(item);
    return age != null ? defaultAgeGroupQuantiser(age) : null;
}

function defaultDaysToMonthsGroupQuantiser(value: number): string {
    if (value <= 30) {
        return "upto 1 month";
    }
    if (value <= 60) {
        return "upto 2 months";
    }
    if (value <= 90) {
        return "upto 3 months";
    }
    if (value <= 182) {
        return "upto 6 months";
    }
    if (value <= 365) {
        return "upto 12 months";
    }
    if (value <= 547) {
        return "upto 18 months";
    }
    if (value <= 730) {
        return "upto 24 months";
    }
    return "more than 24 months";
}

function defaultDaysToWeeksGroupQuantiser(value: number): string {
    if (value <= 7) {
        return "upto 1 week";
    }
    if (value <= 14) {
        return "upto 2 weeks";
    }
    if (value <= 21) {
        return "upto 3 weeks";
    }
    if (value <= 28) {
        return "upto 4 weeks";
    }
    return "more than 4 weeks";
}

function getQuantisedLengthOnService(item: ReferralAggregate): string {
    var diffDays = tableRepresentations.lengthOfDaysOnService(item);
    return diffDays == null
        ? "no accepted/start date"
        : defaultDaysToMonthsGroupQuantiser(diffDays);
}

function getQuantisedLengthOnWaiting(item: ReferralAggregate): string {
    var diffDays = tableRepresentations.lengthOfDaysOnWaiting(item);
    return diffDays == null ? "no decision date" : defaultDaysToWeeksGroupQuantiser(diffDays);
}

export function groupByReferralStatus(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            messageLookup(inputElement.referral.statusMessageKey || "no status")
        )
        .pairs()
        .map(extractPair);
}
export function groupByExitReason(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => messageLookup(inputElement.referral.exitReason || "no reason"))
        .pairs()
        .map(extractPair);
}
export function groupByLatestClientStatus(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.referral.latestClientStatusId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(
                          inputElement.referral.latestClientStatusId
                      )
                      .getDisplayName()
                : "-"
        )
        .pairs()
        .map(extractPair);
}
export function groupBySignpostReason(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            messageLookup(inputElement.referral.signpostedReason || "no reason")
        )
        .pairs()
        .map(extractPair);
}
export function groupByAgeAtDateOfReferral(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => getQuantisedAgeInYearsAtTimeOfReferral(inputElement) || "")
        .pairs()
        .map(extractPair);
}

export function groupByCountOfChildRecipients(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.referral.childServiceRecipientIds
                ? inputElement.referral.childServiceRecipientIds.length.toString()
                : "0"
        )
        .pairs()
        .map(extractPair);
}

export function groupBySrId<T extends {serviceRecipientId: number}>(
    input: Sequence<T>
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => inputElement.serviceRecipientId.toString())
        .pairs()
        .map(extractPair);
}
export function groupByServiceAllocation<T extends {serviceAllocationId: number}>(
    input: Sequence<T>,
    ctx: AnalysisContext
): Sequence<Group<T>> {
    const allocToName = (id: number) =>
        ctx.getSessionData().getServiceCategorisation(id).serviceName;
    return input
        .groupBy(
            inputElement => allocToName(inputElement.serviceAllocationId) || "no service assigned"
        )
        .pairs()
        .map(extractPair);
}
export function groupByProjectAllocation<T extends {serviceAllocationId: number}>(
    input: Sequence<T>,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    const allocToName = (id: number) =>
        ctx!.getSessionData().getServiceCategorisation(id)?.projectName;
    return input
        .groupBy(
            inputElement => allocToName(inputElement.serviceAllocationId) || "no project assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupByReferredService(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                ctx
                    .getSessionData()
                    .getServiceCategorisation(inputElement.referral.serviceAllocationId)
                    .serviceName || "no service assigned"
        )
        .pairs()
        .map(extractPair);
}

export function getServiceAllocation(aggregate: ReferralAggregate) {
    return aggregate.referral.serviceAllocationId == null
        ? null
        : aggregate.sessionData!.getServiceCategorisation(aggregate.referral.serviceAllocationId);
}

export function groupByCompany(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.referral.serviceAllocationId != null
                ? getServiceAllocation(inputElement)?.companyName || "no company name"
                : "not allocated"
        )
        .pairs()
        .map(extractPair);
}

export function groupByServiceGroup(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.referral.serviceAllocationId != null
                ? getServiceAllocation(inputElement)?.serviceGroupName || "no service group"
                : "not allocated"
        )
        .pairs()
        .map(extractPair);
}

export function groupByClientGroup(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.referral.serviceAllocationId
                ? getServiceAllocation(inputElement)?.clientGroupName || "no client group"
                : "not allocated"
        )
        .pairs()
        .map(extractPair);
}

export function groupByReferralProjectName(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                ctx
                    .getSessionData()
                    .getServiceCategorisation(inputElement.referral.serviceAllocationId)
                    .projectName || "no current project assigned"
        )
        .pairs()
        .map(extractPair);
}
export function groupByReferralRegionName(
    input: Sequence<ReferralAggregate>,
    ctx: AnalysisContext
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                ctx
                    .getSessionData()
                    .getProject(
                        ctx
                            .getSessionData()
                            .getServiceCategorisation(inputElement.referral.serviceAllocationId)
                            .projectId || null
                    )?.regionName || "no current region assigned"
        )
        .pairs()
        .map(extractPair);
}
export function groupByReferrer(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.referral.source || "no referrer source")
        .pairs()
        .map(extractPair);
}
// takes a grouping, and alters that grouping to be a count of the incoming key
// eg group by groupByReferralClientId returns the key as the cid, but really, we want the count of the cid as the key
export function groupByReferralGroupCount(
    input: Sequence<Group<ReferralAggregate>>
): Sequence<Group<ReferralAggregate>> {
    return (
        input
            .groupBy(inputElement => inputElement.count!.toString())
            // pairs moves this structure into an array of ['value', ref: Group<RA>]
            // where the Group<RA> is the incoming grouping, eg clientId
            .pairs()
            // extractPair turns the pairs array into a Group {key: array[0], elements: array[1]}
            // which will be key: 'value', elements: Group<RA> (key, elements, count)
            // so we use our own extractPair to get at the group elements - we don't want the key anymore
            .map(extractPair)
            // we need to replace the elements grouping with the elements
            // and ignore the original incoming grouping 'key'
            .map(grp => {
                // toArray doesn't lose us anything, since pairs() creates the array that extractPair wraps in Lazy
                let groupOfRefAggArray: Group<ReferralAggregate>[] = grp.elements.toArray();
                let allRefAgg: Sequence<ReferralAggregate> = Lazy(groupOfRefAggArray)
                    .map(grp => grp.elements)
                    .flatten<ReferralAggregate>();
                // return a new group mapping which uses the new key and new count, but the old elements
                let result: Group<ReferralAggregate> = {
                    key: grp.key,
                    count: grp.elements.size(),
                    elements: allRefAgg
                };
                return result;
            })
    );
}
export function groupByReferralClientId(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => inputElement.referral.clientId.toString() || "no client assigned")
        .pairs()
        .map(extractPair);
}

export function groupByReferralAssignedWorker(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                // TODO: Must do by workerId but that needs adding at server side
                //            inputElement.referral.workerId ? inputElement.referral.workerId.toString() : "(no worker)")
                inputElement.referral.supportWorkerDisplayName || "no worker assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupByReferralAssignedInterviewer1(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(
            inputElement =>
                // TODO: Must do by workerId but that needs adding at server side
                //            inputElement.referral.workerId ? inputElement.referral.workerId.toString() : "(no worker)")
                inputElement.referral.interviewer1WorkerDisplayName || "no interviewer assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupByEthnicity(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.client!.ethnicOriginId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(inputElement.client!.ethnicOriginId)
                      .getDisplayName()
                : "no ethnicity assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupByGender(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => {
            return inputElement.client!.genderId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(inputElement.client!.genderId)
                      .getDisplayName()
                : "no gender saved"; // avoid 'assigned' here
        })
        .pairs()
        .map(extractPair);
}

export function groupByReligion(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.client!.religionId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(inputElement.client!.religionId)
                      .getDisplayName()
                : "no religion assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupBySexualOrientation(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => {
            return inputElement.client!.sexualOrientationId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(
                          inputElement.client!.sexualOrientationId
                      )
                      .getDisplayName()
                : "no sexual orient. allocated";
        })
        .pairs()
        .map(extractPair);
}

export function groupByFirstLanguage(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement =>
            inputElement.client!.firstLanguageId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(inputElement.client!.firstLanguageId)
                      .getDisplayName()
                : "no first language assigned"
        )
        .pairs()
        .map(extractPair);
}

export function groupByDisability(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => {
            return inputElement.client!.disabilityId
                ? inputElement
                      .sessionData!.getListDefinitionEntryById(inputElement.client!.disabilityId)
                      .getDisplayName()
                : "no disability assigned";
        })
        .pairs()
        .map(extractPair);
}

export function groupByLengthOnService(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => getQuantisedLengthOnService(inputElement))
        .pairs()
        .map(extractPair);
}
export function groupByLengthOnWaiting(
    input: Sequence<ReferralAggregate>
): Sequence<Group<ReferralAggregate>> {
    return input
        .groupBy(inputElement => getQuantisedLengthOnWaiting(inputElement))
        .pairs()
        .map(extractPair);
}
