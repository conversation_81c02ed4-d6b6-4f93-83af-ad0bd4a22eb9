import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import types = require("./types");
import SequenceAnalysis = types.SequenceAnalysis;
import {AnalysisContext} from "../chart-domain";
import {Building, FormEvidence, RepairDto, streetAddress} from "ecco-dto";
import {
    booleanColumn,
    columnMap,
    dateColumn,
    joinColumnMaps,
    joinNestedPathColumnMaps,
    numberColumn,
    textColumn
} from "../controls/tableSupport";
import {
    customFormWorkOnlyColumns,
    listDefIdLookup
} from "../tables/predefined-table-representations";
import {EccoDate} from "@eccosolutions/ecco-common";

//*********************************
// Generalised functions

//*********************************
// Analysis: breakdown of buildings

export const buildingOnlyColumns = columnMap<Building>(
    // timestamp is created time
    numberColumn("b-id", row => row.buildingId),
    numberColumn("sr-id", row => row.serviceRecipientId),
    textColumn("calendar-id", row => row.calendarId),
    numberColumn(
        "s-id",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.serviceAllocationId).serviceId
    ),
    numberColumn("parent-id", row => row.parentId),
    textColumn("parent-name", row => row.parentName),
    textColumn("name", row => row.name),
    textColumn("external ref", row => row.externalRef),
    numberColumn("resourceTypeId", row => row.resourceTypeId),
    textColumn("resourceTypeName", row => row.resourceTypeName),
    booleanColumn("disabled", row => row.disabled),
    // parent-a-id - see FixedContainerToViewModel
    numberColumn("parent-adr-id", row => row.locationId),
    textColumn("address", row => streetAddress(row.address)),
    textColumn("town", row => (row.address ? row.address.town : null)),
    textColumn("postcode", row => (row.address ? row.address.postcode : null))
    // choicesMap
    // textMap
    // dateMap
    // links - see FixedContainerToViewModel
);

export class BuildingOnlyAnalysis extends SequenceAnalysis<Building> {
    constructor(ctx: AnalysisContext, data: Sequence<Building>) {
        super(ctx, data, (item: Building) => `${item.buildingId}`);
        this.recordRepresentation = {
            BuildingOnly: buildingOnlyColumns
        };
        this.derivativeAnalysers = {};
    }
}

//*********************************
// Analysers: Building

//*********************************
// Analysis: grouped building functions



//*********************************
// REPAIRS

export class RepairOnlyAnalysis extends SequenceAnalysis<RepairDto> {
    constructor(ctx: AnalysisContext, data: Sequence<RepairDto>) {
        super(ctx, data, (item: RepairDto) => `${item.repairId}`);
        this.recordRepresentation = {
            RepairOnly: repairOnlyColumns,
            RepairWithCustomForm: repairWithCustomFormColumns
        };
        this.derivativeAnalysers = {
        };
    }
}

export const repairOnlyColumns = columnMap<RepairDto>(
    numberColumn("rp-id", row => row.repairId),
    numberColumn("b-id", row => row.buildingId), // NB displayName includes building
    numberColumn("sr-id", row => row.serviceRecipientId),
    numberColumn(
        "s-id",
        (row, ctx) =>
            ctx.getSessionData().getServiceCategorisation(row.serviceAllocationId).serviceId
    ),

    textColumn("name", row => row.displayName),
    textColumn("status", row => row.statusMessage), // exited / to start etc...
    textColumn("category", (row, ctx) => listDefIdLookup(row.categoryId, ctx.getSessionData())),
    numberColumn("rate-id", (row, ctx) => row.rateId),
    textColumn("rate", row => row.rateName),
    textColumn("priority", (row, ctx) => listDefIdLookup(row.priorityId, ctx.getSessionData())),

    dateColumn("received", row => EccoDate.parseIso8601(row.receivedDate)),
    textColumn("signpost comment", row => row.signpostedExitComment),
    dateColumn("decided", row => EccoDate.parseIso8601(row.decisionMadeOn)),
    //textColumn("signposted at", (row) => row.acceptOnServiceState == "SIGNPOSTED" ? "accept on service" : row.appropriateReferralState == "SIGNPOSTED" ? "appropriate referral" : null),

    // source
    textColumn("from", row => row.source), // NB this includes the agency name, if available
    textColumn("worker", row => row.supportWorkerDisplayName),
    dateColumn("start", row => EccoDate.parseIso8601(row.receivingServiceDate || null)),

    // exit
    dateColumn("exited", row => EccoDate.parseIso8601(row.exitedDate)),
    textColumn("exit reason", (row, ctx) => listDefIdLookup(row.exitReasonId, ctx.getSessionData()))

    //dateColumn("review", (row) => EccoDate.parseIso8601(row.reviewDate))
);

export const repairToCustomFormColumns = joinNestedPathColumnMaps<RepairDto, FormEvidence<any>>(
    "f",
    row => row.customFormWorkLatest,
    customFormWorkOnlyColumns
);
export const repairWithCustomFormColumns = joinColumnMaps(
    repairOnlyColumns,
    repairToCustomFormColumns
);
