import Lazy = require("lazy");
import Sequence = LazyJS.Sequence; // Note: LazyJS not Lazy, due to how module is weird.
import {ActivityType, SingleValueHistoryDto} from "ecco-dto";
import * as types from "@eccosolutions/ecco-common";
import {EccoDate} from "@eccosolutions/ecco-common";
import {ReportCriteriaDto} from "ecco-dto";
import {AclEntryDto} from "ecco-dto";
import {Action as ActionDefDto} from "ecco-dto";
import {
    BaseOutcomeBasedWork,
    BaseWork,
    FormEvidence,
    SupportAction,
    SupportWork,
    BaseServiceRecipientCommandDto,
    EvidenceGroup
} from "ecco-dto";
import {Client as ClientDto} from "ecco-dto";
import {ClientAttendanceDto} from "ecco-dto";
import {SessionData, HactSessionData} from "ecco-dto";
import {QuestionAnswerSnapshotDto, QuestionnaireWorkDto} from "ecco-dto";
import {ReferralDto, RelatedRelationship, ServiceRecipientAssociatedContact} from "ecco-dto";
import {RiskWorkEvidenceDto as RiskWork} from "ecco-dto";
import {User} from "ecco-dto";
import {
    representation,
} from "../controls/tableSupport";
import {AnalyserReportStage, AnalysisContext, ReportStage, TableReportStage} from "../chart-domain";
import {ColumnRepresentationsMap, TableRepresentationBase} from "../ReportTable";

//*********************************
// DATA STRUCTURES
// could be generalised, if not already
//*********************************
/**
 * Allow an entity to point to its parent in a manner which avoids us creating lots of types.
 *
 * NB As a type we can be strongly typed between child type C and parent P.
 * NB As an interface we would only reference the T which would be easier to cast:
 *      export interface EntityWithParent<T> {parent: T;}
 *      let result = <T & EntityWithParent<U>> entity;
 *      result.parent = parent;
 */
export type EntityWithParent<P, C> = C & {
    parent: P;
};

export function mapPairToObject<T>(group: Group<T>) {
    return {
        key: group.key,
        count: group.elements.size(),
        elements: group.elements
    };
}
// property becomes the key of Group
export function groupByProperty<T>(
    input: Sequence<T>,
    property: keyof T,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => `${inputElement[property]}` || "no value assigned")
        .pairs()
        .map(extractPair)
        .map(mapPairToObject);
}
export function groupByFunction<T>(
    input: Sequence<T>,
    fn: (arg: T) => string,
    ctx?: AnalysisContext
): Sequence<Group<T>> {
    return input
        .groupBy(inputElement => fn(inputElement) || "no value assigned")
        .pairs()
        .map(extractPair)
        .map(mapPairToObject);
}

export function filterByPropertyValue<T>(
    input: Sequence<T>,
    property: keyof T,
    values: string[],
    ctx?: AnalysisContext
): Sequence<T> {
    return input.filter(inputElement => values.indexOf(`${inputElement[property]}`) > -1);
}

class AnalysisStageContext implements AnalysisContext {
    // NB rptCtx is ChartDefinition, so could be renamed ReportContext
    constructor(public rptCtx: AnalysisContext, public stgCtx: ReportStage) {}

    getCurrentStage() {
        return this.stgCtx;
    }

    getSessionData(): SessionData {
        return this.rptCtx.getSessionData();
    }
    getReportFrom(): EccoDate | null {
        return this.rptCtx.getReportFrom();
    }
    getReportTo(): EccoDate | null {
        return this.rptCtx.getReportTo();
    }
    hasColumn(name: string): boolean {
        return this.rptCtx.hasColumn(name);
    }
    hasColumnStart(startsWith: string): boolean {
        return this.rptCtx.hasColumnStart(startsWith);
    }
    getServiceId(): number | undefined | null {
        return this.rptCtx.getServiceId();
    }
    getQuestionnaireEvidenceGroups(): string[] {
        return this.rptCtx.getQuestionnaireEvidenceGroups();
    }
}

/**
 * T = input, U = output type
 *
 * Use as: var fn: Analyser<string, number> = function(data) { return 1; }
 */
export interface Analyser<T, U> {
    (ctx: AnalysisContext, data: T): Analysis<U>;
}

export interface Accumulator<T, ITEM> {
    reduce(items: Sequence<ITEM>): T;
}

/**
 * When an analyser works on a stream and produces a stream, we'll call it a Transformer
 * Sequence<T> = input, Sequence<U> = output type
 */
export interface Transformer<T, U> extends Analyser<Sequence<T>, Sequence<U>> {}

export interface AnalysisFactory<T> {
    new (ctx: AnalysisContext, data: T): Analysis<T>;
}

/** <T> is the type we want to view or analyse further - treat as ABSTRACT base class */
export class Analysis<T> {
    derivativeAnalysers: types.StringToObjectMap<Analyser<T, any> | string> = {};

    /** ungroupedAnalysis can be left null unless you want to override this being the previous.
     * see Analysis.analyseWithForSequence() */
    constructor(protected ctx: AnalysisContext, protected data: T) {}

    /** For use where we are going to produce a chart etc. It's possible that all should be Grouped */
    analyseWithForSequenceAnalysis(stageCtx: AnalyserReportStage): SequenceAnalysis<T> {
        // FIXME This is an unsafe cast.
        // We should investigate whether analyseWith ever produces a result
        // that is not a SequenceAnalysis.
        var analysis = <SequenceAnalysis<T>>this.analyseWith(stageCtx);
        return analysis;
    }

    analyseWith(stage: AnalyserReportStage): Analysis<any> {
        const analyserName = stage.getAnalyserType();
        if (!analyserName || !this.derivativeAnalysers || !this.derivativeAnalysers[analyserName]) {
            var source = Object.getPrototypeOf(this);
            throw new Error(
                "No analyser found called: " +
                    analyserName +
                    " on " +
                    source +
                    ". Available are: " +
                    Object.keys(this.derivativeAnalysers!)
            );
        }
        var analyser = this.derivativeAnalysers[analyserName];
        if (typeof analyser === "string") {
            analyser = analysersByName[analyser];
            if (!analyser) {
                throw new Error(
                    "Couldn't find analyser named: " +
                        this.derivativeAnalysers[analyserName] +
                        " for action: " +
                        analyserName
                );
            }
        }
        const ctx = new AnalysisStageContext(this.ctx, stage);
        var analysis = this.data && analyser(ctx, this.data);
        return analysis;
    }

    getData() {
        return this.data;
    }

    static getOnClickAnalyser<T>(
        analyserName: string,
        onClickAnalysers: types.StringToObjectMap<Analyser<T, any> | string>
    ): Analyser<T, any> {
        if (!analyserName || !onClickAnalysers[analyserName]) {
            const source = Object.getPrototypeOf(this);
            throw new Error(
                "No click analyser found called: " +
                    analyserName +
                    " on " +
                    source +
                    ". Available are: " +
                    Object.keys(onClickAnalysers)
            );
        }
        let analyser = onClickAnalysers[analyserName];
        if (typeof analyser === "string") {
            analyser = analysersByName[<string>analyser];
            if (!analyser) {
                throw new Error(
                    "Couldn't find analyser named: " +
                        onClickAnalysers[analyserName] +
                        " for action: " +
                        analyserName +
                        ". Available by name are: " +
                        Object.keys(analysersByName)
                );
            }
        }
        return analyser;
    }
}

/** Analysis that we'd use for a badge or single record view */
export class SingleRecordAnalysis<T> extends Analysis<T> {
    /** A transformation of T to give a set of named values which can be considered columns of a table,
     *  but in this case will apply to a single record of type T */
    recordRepresentation: types.StringToObjectMap<ColumnRepresentationsMap<T>> = {};
    onClickAnalysers: types.StringToObjectMap<Analyser<T, any> | string> = {};

    getRecordRepresentationSingle(name: string): ColumnRepresentationsMap<T> {
        if (!name || !this.recordRepresentation[name]) {
            var source = Object.getPrototypeOf(this).toString();
            throw new Error(
                "No record representation found called: " +
                    name +
                    " on " +
                    source +
                    ". Available are: " +
                    Object.keys(this.recordRepresentation)
            );
        }
        return this.recordRepresentation[name];
    }

    // CLONE
    addOnClickAnalyser(key: string, analyser: Analyser<T, any> | string) {
        this.onClickAnalysers[key] = analyser;
    }

    // CLONE
    /** Click of a row of a table (where T is e.g. ReferralAggregate), or a chart segment (where T is Group<something>) */
    onClickWith(analyserName: string, item: T): Analysis<any> {
        let analyser: Analyser<T, any> = Analysis.getOnClickAnalyser(
            analyserName,
            this.onClickAnalysers
        );
        let analysis = item && analyser(this.ctx, item);
        return analysis;
    }
}

export class SequenceAnalysis<T> extends Analysis<Sequence<T>> {
    private onClickAnalysers: types.StringToObjectMap<Analyser<T, any> | string> = {};
    private onClickManyAnalysers: types.StringToObjectMap<Analyser<Sequence<T>, any> | string> = {};
    private onClickAllAnalysers: types.StringToObjectMap<Analyser<Sequence<T>, any> | string> = {};
    private onClickManyAnalysisFactory: types.StringToObjectMap<AnalysisFactory<any> | string> = {};

    /** A transformation of T to give a set of named values which can be considered columns of a table,
     * such that we can map Sequence<T> to table columns */
    recordRepresentation: types.StringToObjectMap<ColumnRepresentationsMap<T>> = {};

    /**
     * keyFn for Group<T> return item.key, for ReferralAggregate, return item.referral.referralId
     */
    constructor(ctx: AnalysisContext, data: Sequence<T>, private keyFn: (arg: T) => string) {
        super(ctx, data);
    }

    /** Specify how selecting one of the Sequence will be processed.
     analyser is allowed to be string if in another module we can't access.
     "ungroup" key is for working with the elements of the selection, "single" is for working with the
     analysis itself - such as when doing chained KPI badges */
    addOnClickAnalyser(key: string, analyser: Analyser<T, any> | string) {
        this.onClickAnalysers[key] = analyser;
    }

    /**
     * Specify how selecting all of the data will be processed.
     * Without this, 'ungroup' is selected and the data from the previous stage is passed on
     */
    addOnClickManyAnalyser(key: string, analyser: Analyser<Sequence<T>, any> | string) {
        this.onClickManyAnalysers[key] = analyser;
    }

    /**
     * Specify how selecting all of the data will be processed.
     * Without this, 'ungroup' is selected and the data from the previous stage is passed on
     */
    addOnClickAllAnalyser(key: string, analyser: Analyser<Sequence<T>, any> | string) {
        this.onClickAllAnalysers[key] = analyser;
    }

    /**
     * Specify how selecting many of the Sequence will be processed.
     * The analysis is called after processing 'single' analysis on each key - see types#onClickWithManyByReduce
     */
    addOnClickManyAnalysis(key: string, analysisFactory: AnalysisFactory<any> | string) {
        if (!this.onClickAnalysers[key]) {
            // we need the analyser as a single click analyser, since the many click will repeat this analyser
            throw new Error(
                "addOnClickManyAnalysis key '"
                    .concat(key)
                    .concat("' is also required in addOnClickAnalyser")
            );
        }
        this.onClickManyAnalysisFactory[key] = analysisFactory;
    }

    getRecordRepresentation(stage: TableReportStage): TableRepresentationBase<T> {
        const name = stage.getTableRepresentationName();
        const selectionList = stage.getTableColumns();

        const dynamicCols: ColumnRepresentationsMap<T> | null =
            stage.getTableDynamicColumnsSources()
                ? this.getRecordRepresentationFromSources(
                      name,
                      stage.getTableDynamicColumnsSources()
                  )
                : null;

        if (!name || !(this.recordRepresentation[name] || dynamicCols)) {
            var source = Object.getPrototypeOf(this).toString();
            throw new Error(
                "No table representation found called: " +
                    name +
                    " on " +
                    source +
                    ". Available static columns are: " +
                    Object.keys(this.recordRepresentation) +
                    ". Available dynamic columns are: " +
                    Object.keys(dynamicCols!)
            );
        }

        let columnRepresentationsMap: ColumnRepresentationsMap<T> = {};
        if (this.recordRepresentation[name]) {
            columnRepresentationsMap = this.recordRepresentation[name];
        }
        if (dynamicCols) {
            Object.keys(dynamicCols).forEach(c => (columnRepresentationsMap[c] = dynamicCols[c]));
        }
        return representation(columnRepresentationsMap, selectionList);
    }

    // sub-class to provide the columns
    getRecordRepresentationFromSources(
        name: string | undefined,
        sources: string[] | undefined
    ): ColumnRepresentationsMap<T> {
        throw new Error("columnSourceDefIds provided but no implementation to provide them");
    }

    getKey(item: T): string {
        return this.keyFn(item);
    }

    /** Click of a row of a table (where T is e.g. ReferralAggregate), or a chart segment (where T is Group<something>) */
    onClickWith(analyserName: string, item: T): Analysis<any> {
        let analyser: Analyser<T, any> = Analysis.getOnClickAnalyser(
            analyserName,
            this.onClickAnalysers
        );
        return item && analyser(this.ctx, item);
    }

    onClickWithMany(analyserName: string, item: Sequence<T>): Analysis<any> {
        let analyser: Analyser<Sequence<T>, any> = Analysis.getOnClickAnalyser(
            analyserName,
            this.onClickManyAnalysers
        );
        return item && analyser(this.ctx, item);
    }

    /**
     * Click of all the data.
     * This is different to onClickWith(single), which ungroups say already calculated data.
     * Its different to onClickWith(many) which actually works by calling onClickWith(single) using reduce - see (see types#onClickWithManyByReduce)
     * and this doesn't allow us calculated date for a single analyser, a badge.
     * So we need an onClickWithAll to be able to specify an analyser to do the calculations.
     */
    onClickWithAll(analyserName: string, item: Sequence<T>): Analysis<any> {
        let analyser: Analyser<Sequence<T>, any> = Analysis.getOnClickAnalyser(
            analyserName,
            this.onClickAllAnalysers
        );
        return item && analyser(this.ctx, item);
    }

    /** Handles 'many clicks' in the sense that each click is handled individually
     * to ensure the analyser is accurate, then the data is extracted so that all we
     * have to do is construct the Analysis (resulting from the analyser) with the
     * data we now have. A specific analyser for many clicks could be created
     * accordingly, should any calculations need to be performed.
     */
    // NB 'any' here could be 'U' - they are the same resulting data type
    onClickWithManyByReduce(analyserName: string, items: Sequence<T>): Analysis<any> {
        let singleAnalyser: Analyser<T, any> = Analysis.getOnClickAnalyser(
            analyserName,
            this.onClickAnalysers
        );

        let dataConverted: Sequence<any> = items.reduce(
            (itemsConverted: Sequence<any>, item: T) => {
                // for each item, do the same as 'onClickWith' above
                let singleAnalysis: Analysis<any> = item && singleAnalyser(this.ctx, item);
                let singleData: any = singleAnalysis.getData();
                return itemsConverted.concat(singleData);
            },
            Lazy({})
        );
        //.flatten<any>();

        // analysers are a function of one type of data to an analysis (container) of another type of data
        // we need to produce an analysis of all the data converted and we don't want to perform an analyser
        // function because we already have the data so we just want to construct the correct analysis
        // however, we are constructing an analysis with a sequence on top of whatever T is, so we can't really
        // cast into the analyserName provided since it won't expect a sequence wrapper so we can either:
        //  1) not return an analysis - and just the raw data passed to the showDataForStage
        //      - best avoided, although it does appear that the analysis isn't used much, we would lose a layer of abstraction
        //  2) wrap the provided analysis so that it unwraps the sequence and passes it to the underlying analyser
        //      - this would require creating an interface for the analyser, and isn't the quickest approach
        //  3) provide some default converter, such as 'ungroup' that exists for single clicks
        //      - however, we can assume that the onClickWithManyByReduce exactly follows that of onClickWith
        //  4) assume some logic, such as the incoming analyser is a Group<something> and returns a Sequence<something else>
        //      - actually we only need to assume that singleAnalyser can be repeated and joined into the same resulting Analysis
        // OPTION 4 makes sense for what we see the need for - clicking pie charts
        // OPTION 3 is used to help create the resulting Analysis without too much magic or refactoring

        // this is the contract we are assuming:
        //   - that the 'ungroup' singleAnalysis can be repeated, concatenated, and placed into the same resulting Analysis
        //   - eg 'this' Analysis<Sequence<T>> is actually Analysis<Sequence<Group<T>>> (eg a GroupedReferralAnalysis)
        //   - and the singleAnalyser takes this Analysis<Sequence<Group<T>>> and produces Analysis<Sequence<U>>
        //   - these are joined up and put into the Analysis - the same that would have resulted from a single click
        //   - but its easier to have the factory to create it
        let analysisFactory: AnalysisFactory<any> = this.getOnClickAnalysisFactory(analyserName);
        // noinspection UnnecessaryLocalVariableJS
        let analysisConverted: Analysis<any> = new analysisFactory(this.ctx, dataConverted);
        return analysisConverted;
    }

    private getOnClickAnalysisFactory(analysisFactoryName: string): AnalysisFactory<any> {
        if (!analysisFactoryName || !this.onClickManyAnalysisFactory[analysisFactoryName]) {
            let source = Object.getPrototypeOf(this);
            throw new Error(
                "No many click analyser found called: " +
                    analysisFactoryName +
                    " on " +
                    source +
                    ". Available are: " +
                    Object.keys(this.onClickManyAnalysisFactory)
            );
        }
        let analysisFactory = this.onClickManyAnalysisFactory[analysisFactoryName];
        if (typeof analysisFactory === "string") {
            analysisFactory = analysisFactoriesByName[<string>analysisFactory];
            if (!analysisFactory) {
                throw new Error(
                    "Couldn't find analysis factory named: " +
                        this.onClickManyAnalysisFactory[analysisFactoryName] +
                        " for action: " +
                        analysisFactoryName +
                        ". Available by name are: " +
                        Object.keys(analysisFactoriesByName)
                );
            }
        }
        return analysisFactory;
    }
}

/**
 * Have an interface so we can build up columns from the data
 * We've extended MatrixRow to be a Group of a Group so that
 * subsequent breakdown data doesn't need a 'row' but is
 * simply a group of a group.
 */
export interface MatrixRow<T> extends Array<Group<T>> {
    // useful for the analyser's getKey() so we can get the row number from a selected cell
    // a table click does actually send the data
    //rowNumber: number;
    //answerValueFrom: string;
    // a column is a Group of T[] - the key is the column name
    // a row is equivalent to an array of columns
    // where the column names are the key
    //columns: StringToObjectMap<T[]>;
}

export class MatrixAnalysis<U extends MatrixRow<any>> extends SequenceAnalysis<U> {
    constructor(ctx: AnalysisContext, data: Sequence<U>) {
        super(ctx, data, (item: U) => ""); // keyFn could be a rowIndex to allow matches from table clicks
    }
}

/** DTO for where analysis is done on server or has been saved locally - omits elements as we will extend this
 *  to give us the counts, totals, averages, max etc values */
export interface GroupSummary {
    keyId: number;

    key: string;

    /** Count could vary by analyser, but should be a count of the entity type being analysed, i.e. the count
     * of entities such that an average value for a field is totalForField/count */
    count?: number;
}

/** A set of report items grouped by some criteria. */
export interface Group<T> {
    key: string;

    /** Items related to this key. This allows them to be passed to a table or chained chart if the representation
     * of this key (e.g. a pie segment or bar in a bar chart) is clicked.
     */
    elements: Sequence<T>;

    count?: number;
}

/** A GroupedAnalysis produces data that is a sequence of Group<T>. When we select a group, we need to be able
 * to resolve the ungroupedAnalysis to continue the chain.
 *
 * TODO: It's possible that all should be Grouped. If we are going to show results in a table, then each row is
 *       keyed by something we then might want to break down somehow.
 */
export class GroupedAnalysis<T, U extends Group<any>> extends SequenceAnalysis<U> {
    // NOTE: U extends Group<T> but TS doesn't like that

    /** ungroupedAnalysis can be left null unless you want to override this being the previous.
     * see Analysis.analyseForGroupWith() */
    constructor(ctx: AnalysisContext, data: Sequence<U>) {
        super(ctx, data, (item: U) => item.key);
    }
}

export interface GroupFn<T> {
    (input: Sequence<T>, ctx?: AnalysisContext): Sequence<Group<T>>;
}

export function extractPair<T>(group: [string, Array<T>]): Group<T> {
    if (!group[1]) {
        throw new Error("no elements for group:" + group[0]);
    }
    return {key: group[0], elements: Lazy(group[1])};
}

export class NumberAnalysis extends SequenceAnalysis<number> {
    constructor(ctx: AnalysisContext, data: Sequence<number>) {
        super(ctx, data, (item: number) => item.toString());
    }
}

// Report data types.
// These data types can be either the input or the output of an Analyser<T, U>.
//
//  * ReferralAggregate
//  * ReferralSmartStepCounts: Referral with counts of achieved/outstanding
//    Smart Steps.
//     * ReferralGroupSmartStepCounts<TGroup>: The same, but with total counts
//       for any of the referral groupings listed below (TGroup extends
//       Group<Referral>).
//  * ReferralMileage: Referral with 'mileage' data (see MileageEtcAnalysis).
//     * ReferralGroupMileage<TGroup>: The same, but with total counts for any
//       of the referral groupings listed below (TGroup extends
//       Group<Referral>).
//  * Group<Referral>: A set of Referrals grouped by some criteria. Either the   was KeyedReportItem
//    whole group or the referrals within the group may be the input to a
//    derivative analysis.
//     * ReferralsByAgeAtDateOfReferral (one group per referral)
//     * ReferralsByAssignedWorker (one group per referral)
//     * ReferralsByProjectName (one group per referral)
//     * ReferralsByStatus (one group per referral)
//     * ReferralsByReferredService (one group per referral)
//     * ReferralsByDayOfWeek: grouped by day on which the client attends (many
//       groups per referral)
//     * ReferralsByActivityInterest: grouped by the client's expressed
//       activity interests (many groups per referral).

export interface ReferralAggregate {
    referral: ReferralDto;
    client?: ClientDto;
    contacts?: Sequence<ServiceRecipientAssociatedContact>;
    supportWork?: Sequence<SupportWork>;
    supportRiskWork?: Sequence<BaseOutcomeBasedWork>;
    questionnaireWork?: Sequence<QuestionnaireWorkDto>;
    customFormWork?: Sequence<FormEvidence<any>>;
    customFormWorkLatest?: FormEvidence<any>;
    serviceRecipientCommandLatest?: BaseServiceRecipientCommandDto;
    riskWork?: Sequence<RiskWork>;
    activityInterest?: Sequence<ActivityType>;
    groupActivities?: Sequence<ClientAttendanceDto>;
    relatedReferrals?: Sequence<RelatedRelationship>;
    singleValueHistory?: Sequence<SingleValueHistoryDto>;
    sessionData?: SessionData; // global reference, loaded once
    hactSessionData?: HactSessionData; // global reference, loaded once
    reportCriteria?: ReportCriteriaDto; // global reference
}

// work item to represent one question/answer
export interface QnAnswerWork {
    work: BaseWork | null;
    qnAnswer: QuestionAnswerSnapshotDto | null;
}
/** A questionnaire with answer item where we've added a reference to the parent ReportItem while doing map() etc processing */
export type QnAnswerWorkParent<P> = EntityWithParent<P, QnAnswerWork>;
export interface QnAnswerWorkWithRefToReferralAggregate
    extends QnAnswerWorkParent<ReferralAggregate> {}

export interface WorkWithRefGroupWithCount
    extends WorkGroupWithCount<WorkWithRefToReferralAggregate> {
    count?: number;
}
export interface WorkGroupWithCount<T> extends Group<T> {
    count?: number;
}

/** A work item where we've added a reference to the parent ReportItem while doing map() etc processing */
export interface WorkWithRefToReferralAggregate extends BaseOutcomeBasedWork {
    evidenceGroup?: EvidenceGroup;
    /** parent item */
    reportItem: ReferralAggregate;
}
export interface WorkWithRefToReferralAggregateSvh extends WorkWithRefToReferralAggregate {
    // optional to avoid 'extends' through report code
    svh?: SingleValueHistoryDto;
}
/** A work item where we've added a reference to the parent ReportItem while doing map() etc processing */
export interface CustomFormWorkWithRefToReferralAggregate extends FormEvidence<any> {
    /** parent item */
    reportItem: ReferralAggregate;
}
/** A supportAction where we've added a reference to the parent ReportItem for reference in further processing/displaying */
export interface SupportActionWithRefToReferralAggregate extends SupportAction {
    /** parent item */
    reportItem: ReferralAggregate;
    /** easy reference to the item that matched the supportAction */
    supportWork: SupportWork;
}

/** An actionDef where we've added a reference to the parent ReportItem while doing map() etc processing */
export interface ActionDefWithRefToReferralAggregate extends ActionDefDto {
    /** parent item */
    reportItem: ReferralAggregate;
    /** easy reference to the item that matched the actionDefId */
    supportWork: SupportWork;
    supportAction: SupportAction;
}

/** A relationship where we've added a reference to the parent ReportItem while doing map() etc processing */
export interface RelationshipWithRefToReferralAggregate extends RelatedRelationship {
    /** parent item */
    reportItem: ReferralAggregate;
}

export interface ReferralAggregateGroupWithActionDef extends Group<ReferralAggregate> {
    totalAchievedSmartSteps: number;
    totalOutstandingSmartSteps: number;
}

/** See CountsByMonthViewModel.java */
export interface CountsByMonthDto {
    /** e.g. serviceId */
    entityId: number;

    /** e.g. serviceName */
    entityName: string;

    /** optional, eg task taskDefinitionId */
    entityId2: number;

    entityName2: string;
    /** e.g. received, closed - populated client side, see TimeSeriesDataSource from 5777c6d3 */
    groupBy: string | undefined;

    /** ISO-8601 year-month e.g. 2015-09 */
    yearMonth: string;

    count: number;
}

/** TODO: Visits means Work here, so perhaps we should rename */
export interface GroupWithVisits<T> extends Group<T>, WorkAnalysis {}

export interface GroupWithWorkTypeVisits<T extends BaseWork> extends Group<T> {
    support: Group<T> & WorkAnalysis;
    threat: Group<T> & WorkAnalysis;
    total: Group<T> & WorkAnalysis;
}

export interface ReferralAggregateGroupWithWorkTypeVisits extends WorkWithRefGroupWithCount {
    // WorkWithRefGroupWithCount, Group<ReferralAggregate>
    //count: number
    support: ReferralAggregateGroupWithVisits;
    threat: ReferralAggregateGroupWithVisits;
}

export interface ReferralAggregateGroupWithVisits extends Group<ReferralAggregate>, WorkAnalysis {
    count: number;
}

export interface WorkAnalysis {
    latestWorkDate: EccoDate | null;
    lastSignedWorkDate: EccoDate | null;
    lastUnSignedWorkDate: EccoDate | null;
    unsignedWorkCount: number;
    totalTimeSpentMins: number;
    totalVisits: number;
    averageVisitLength: number;
    countByCommentType: {[key: string]: number};
}

export interface SingleValueHistoryWithWorkSummary extends SingleValueHistoryDto {
    totalTimeSpentMins: number;
    work: Sequence<BaseWork>;
    /** parent item */
    reportItem: ReferralAggregate;
}

export interface WorkWithRefToSingleValueHistory extends BaseWork {
    svh: SingleValueHistoryDto;
}

export interface SupportActionWithWork {
    supportWork: SupportWork;
    supportAction: SupportAction;
}

export interface UserAggregate {
    user: User;
    aclEntry: AclEntryDto;
}

export var analysersByName: types.StringToObjectMap<Analyser<any, any>> = {};
export var analysisFactoriesByName: types.StringToObjectMap<AnalysisFactory<any>> = {};
