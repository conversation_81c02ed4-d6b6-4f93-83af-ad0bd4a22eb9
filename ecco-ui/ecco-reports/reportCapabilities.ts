import {
    ActivityAttendanceQueryDataSource,
    AddressHistoryQueryDataSource,
    AgencyQueryDataSource,
    ClientQueryDataSource,
    CustomFormLatestQueryDataSource,
    GroupedWorkQueryDataSource,
    FinanceChargeQueryDataSource,
    FinanceReceiptQueryDataSource,
    ProfessionalQueryDataSource,
    QuestionnaireQueryDataSource,
    ReferralQueryDataSource,
    RelatedWorkThenReferralQueryDataSource,
    ReferralsGroupedByServiceAsTimeSeriesDataSource,
    ReferralsGroupedBySourceAsTimeSeriesDataSource,
    ReferralsGroupedByEthnicityAsTimeSeriesDataSource,
    ReferralsGroupedBySexualOrientationAsTimeSeriesDataSource,
    ReferralsGroupedByDisabilityAsTimeSeriesDataSource,
    ReferralsTimeSeriesDataSource,
    ReviewQueryDataSource,
    RiskFlagsQueryDataSource,
    RiskWorkQueryDataSource,
    RotaAgreementQueryDataSource,
    RotaDemandQueryDataSource,
    RotaScheduleQueryDataSource,
    ServiceRecipientCommandQueryDataSource,
    ServiceTypeQueryDataSource,
    SmartStepLatestSnapshotQueryDataSource,
    SmartStepMultiSnapshotQueryDataSource,
    SupportRiskWorkQueryDataSource,
    SupportWorkQueryDataSource,
    TasksByMonthDataSource,
    TaskStatusQueryDataSource,
    UserQueryDataSource,
    SupportWorkLatestSnapshotQueryDataSource,
    RiskWorkLatestSnapshotQueryDataSource,
    BuildingQueryDataSource,
    QuestionnaireMultiSnapshotQueryDataSource,
    QuestionnaireLatestSnapshotQueryDataSource,
    AnswersGroupedByQuestionAsTimeSeriesDataSource,
    ServiceRecipientAssociatedContactDataSource,
    RiskRatingsQueryDataSource,
    EventQueryDataSource,
    SupportFlagsQueryDataSource,
    RepairQueryDataSource
} from "./reportDataSourceFactory";
import {AnalysisContext, DataSource, ReportEntity, SelectionCriteria} from "./chart-domain";
import {Messages} from "ecco-dto";
import {SessionData} from "ecco-dto";
import {
    asAtText,
    ReferralStatus,
    ReferralStatusHandler,
    ReferralStatusLookupById,
    statusesById
} from "./referralStatusHandler";
import {ReportCriteriaDto} from "ecco-dto";
import {getGlobalEccoAPI} from "ecco-components";
import {SelectListOption} from "@eccosolutions/ecco-common";

function getReportEntityText(messages: Messages, rep: string) {
    const lookup = `reportEntity.${ReportEntity[rep as ReportEntity]}`;
    // @ts-ignore
    return messages[lookup];
}

/**
 * Builder to work out which reports can use which criteria of ReportCriteriaDto
 * and therefore match user expectations with what the server side can process.
 * NB This might be nice if it was generated server-side when the report definition returns
 * but would still require manually maintaining there.
 */
export class ReportCriteriaAllowableBuilder {
    private serviceProject = false;
    private questionnaireEvidenceGroup = false; // TODO needs implementing on edit criteria form
    private child = false; // TODO needs implementing on edit criteria form
    private geographicalArea = false;
    private from = false;
    private to = false;
    private selectionPropertyPath = false; // TODO needs implementing on edit criteria form
    private entityStatus = false;
    private entityStatusOptions: SelectListOption[] = [];
    private referralStatus = false;
    private username = false;

    constructor() {
        return this;
    }

    /**
     * Ensure the criterion is consistent between the editing form and what is sent.
     * The criteria here goes straight into chart.load() which then re-finds the dates.
     * This cleanup helps ensure there are no secret defaults between the client and server behaviour,
     * and allows the 'describe' to display exactly what it finds.
     */
    public cleanCriteria(selectionCriteria: SelectionCriteria) {
        if (
            selectionCriteria.getReportCapability().isReferralBasedReport() &&
            ReferralStatusHandler.referralStatusAtEndDate(selectionCriteria.getDto().referralStatus)
        ) {
            selectionCriteria.clearFromDate();
        }
        // clear 'newReferralsOnly' if not appropriate
        if (
            !ReferralStatusHandler.referralStatusReceivedDuringPeriodAllowed(
                selectionCriteria.getDto().referralStatus
            )
        ) {
            selectionCriteria.getDto().newReferralsOnly = null;
        }

        if (!this.useChild()) {
            selectionCriteria.getDto().isChild = null;
        }
        if (!this.useGeographicalArea()) {
            selectionCriteria.getDto().geographicAreaIds = null;
            selectionCriteria.getDto().geographicAreaIdSelected = null;
        }
        if (!this.useFrom()) {
            selectionCriteria.getDto().absoluteFromDate = null;
        }
        if (!this.useTo()) {
            selectionCriteria.getDto().absoluteToDate = null;
        }
        if (!this.useReferralStatus()) {
            selectionCriteria.getDto().referralStatus = null;
        }
        if (!this.useServiceProject()) {
            selectionCriteria.getDto().serviceId = null;
            selectionCriteria.getDto().projectId = null;
        }
        if (!this.useSelectionPropertyPath()) {
            selectionCriteria.getDto().selectionPropertyPath = null;
        }
        if (!this.useUsername()) {
            selectionCriteria.getDto().userId = null;
            selectionCriteria.getDto().username = null;
        }
    }

    public static defaultNewReferralsOnly() {
        return true;
    }

    public withServiceProject() {
        this.serviceProject = true;
        return this;
    }
    public withQuestionnaireEvidenceGroup() {
        this.questionnaireEvidenceGroup = true;
        return this;
    }
    public withChild() {
        this.child = true;
        return this;
    }
    public withGeographicalArea() {
        this.geographicalArea = true;
        return this;
    }
    public withFrom(value = true) {
        this.from = value;
        return this;
    }
    public withTo(value = true) {
        this.to = value;
        return this;
    }
    public withSelectionPropertyPath() {
        this.selectionPropertyPath = true;
        return this;
    }
    public withEntityStatus() {
        this.entityStatus = true;
        return this;
    }
    public withReferralStatus(value = true) {
        this.referralStatus = value;
        return this;
    }
    public withEntityStatusOptions(allowablePropertyPaths: PropertyPath[]) {
        allowablePropertyPaths.forEach(entry => {
            this.entityStatusOptions.push({name: entry.display, id: entry.propertyName});
        });
        return this;
    }
    public withUsername() {
        this.username = true;
        return this;
    }
    public useServiceProject() {
        return this.serviceProject;
    }
    public useQuestionnaireEvidenceGroup() {
        return this.questionnaireEvidenceGroup;
    }
    public useChild() {
        return this.child;
    }
    public useGeographicalArea() {
        return this.geographicalArea;
    }
    public useFrom() {
        return this.from;
    }
    public useTo() {
        return this.to;
    }
    public useSelectionPropertyPath() {
        return this.selectionPropertyPath;
    }
    public useReferralStatus() {
        return this.referralStatus;
    }
    public useEntityStatus() {
        return this.entityStatus;
    }
    public getEntityStatusOptions() {
        return this.entityStatusOptions;
    }
    public useUsername() {
        return this.username;
    }
}

export interface ReportEntityCapability {
    getReportEntity(): ReportEntity;
    /** Provides the 'status' of the referral */
    isReferralBasedReport(): boolean;
    allowablePropertyPaths(sessionData: SessionData): PropertyPath[] | null;
    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder;
    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any> | null
    ): DataSource<any>;
    describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ): string;
}

export class PropertyPath {
    constructor(public propertyName: string, public display: string) {}
}

abstract class ReferralBasedReportCapability {
    abstract getReportEntity(): ReportEntity;

    isReferralBasedReport(): boolean {
        return true;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withGeographicalArea()
            .withReferralStatus()
            .withSelectionPropertyPath();
    }

    allowablePropertyPaths(sessionData: SessionData): PropertyPath[] {
        if (sessionData.isEnabled("client.useDateOfDeath")) {
            return [new PropertyPath("dateOfDeath", "date of death")];
        }
        return [];
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(messages, this.getReportEntity());

        let reportSelectionText = selectionCriteria.getDto().selectionPropertyPath;
        let referralStatusPathText = ReferralReportCapability.getReferralStatusPath(
            reportCriteria,
            this.isReferralBasedReport()
        );

        let reportPathText = reportSelectionText
            ? referralStatusPathText
                ? reportSelectionText.concat(" and ").concat(referralStatusPathText)
                : reportSelectionText
            : referralStatusPathText;

        return reportEntityText.concat(reportPathText ? " ".concat(reportPathText) : "");
    }

    describeEntityAndPathGrouped(
        reportCriteria: ReportCriteriaDto,
        reportPathTextIn: string,
        reportEntityText: string
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();

        let reportPathText = reportPathTextIn;
        let referralStatusPathText = ReferralReportCapability.getReferralStatusPath(
            reportCriteria,
            this.isReferralBasedReport()
        );
        if (referralStatusPathText) {
            reportPathText = reportPathText.concat(" and ").concat(referralStatusPathText);
        }

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class ReferralFullReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralFull;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralQueryDataSource(
            ctx,
            selectionCriteria,
            previousDataSource,
            selectionCriteria.fetchHactSessionData()
        );
    }

    public static getReferralStatusPath(
        reportCriteria: ReportCriteriaDto,
        isReferralReport: boolean
    ): string {
        let reportPathText = "";
        if (reportCriteria.referralStatus) {
            // display allNoDates as '(all data)', but don't offer it as an option in ChartCriteriaForm#getReferralStatusList
            let referralStatusText =
                reportCriteria.referralStatus == "allNoDates"
                    ? "(all data)"
                    : reportCriteria.referralStatus == "created"
                    ? "created"
                    : statusesById[reportCriteria.referralStatus];

            // if we are not referral based - then we keep the from/to dates and change the status text to be 'at end date'
            if (!isReferralReport) {
                reportPathText = reportPathText.concat(" and referrals ");
                if (
                    ReferralFullReportCapability.referralStatusAtEndDate(
                        reportCriteria.referralStatus
                    )
                ) {
                    referralStatusText = referralStatusText.replace(asAtText, "(at end date)");
                }
            }
            reportPathText = reportPathText.concat(referralStatusText);

            // only show 'limited to received' if we are not received, otherwise this is unnecessary and confusing for the user
            if (
                reportCriteria.newReferralsOnly &&
                ReferralStatusLookupById[ReferralStatus.Received] != reportCriteria.referralStatus
            ) {
                reportPathText = reportPathText.concat(" limited to received date (in the period)");
            }
        }

        return reportPathText;
    }

    public static referralStatusAtEndDate(referralStatus: string) {
        let applicableStatusAtEndDate = [
            ReferralStatus.LiveAtEnd,
            ReferralStatus.IncompleteAtEnd,
            ReferralStatus.WaitingAtEnd
        ];
        return referralStatus
            ? applicableStatusAtEndDate.some(
                  status => ReferralStatusLookupById[status] == referralStatus
              )
            : false;
    }

    public static referralStatusReceivedDuringPeriodAllowed(referralStatus: string) {
        let applicableReferralStatus = [
            ReferralStatus.Ongoing,
            ReferralStatus.MoveIn,
            ReferralStatus.Live,
            ReferralStatus.Waiting,
            ReferralStatus.Incomplete
        ];
        return referralStatus
            ? applicableReferralStatus.some(
                  status => ReferralStatusLookupById[status] == referralStatus
              )
            : false;
    }
}

export class ReferralReportCapability extends ReferralFullReportCapability {
    override getReportEntity(): ReportEntity.Referral {
        return ReportEntity.Referral;
    }

    override getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        // TODO avoid loading getRelatedEntities (supply extra argument 'true')
        return new ReferralQueryDataSource(
            ctx,
            selectionCriteria,
            previousDataSource,
            selectionCriteria.fetchHactSessionData(),
            true
        );
    }
}

export class ReferralSummaryReportCapability extends ReferralFullReportCapability {
    override getReportEntity(): ReportEntity.ReferralSummary {
        return ReportEntity.ReferralSummary;
    }

    override getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralQueryDataSource(
            ctx,
            selectionCriteria,
            previousDataSource,
            selectionCriteria.fetchHactSessionData(),
            true,
            false
        );
    }
}

abstract class BaseWorkReportCapability implements ReportEntityCapability {
    abstract getReportEntity(): ReportEntity;
    abstract getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ): DataSource<any>;

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withGeographicalArea()
            .withReferralStatus();
    }

    allowablePropertyPaths(sessionData: SessionData): PropertyPath[] {
        return [new PropertyPath("workDate", "work date")]; // as per report definitions
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(messages, this.getReportEntity());
        let reportPathText = "'took place on'";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

/**
 * Load support work first, then referral data if requested.
 * NB getDataSource switches depending on what is requested.
 */
export class SupportWorkReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.SupportWork;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        if (selectionCriteria.fetchReferral() || selectionCriteria.fetchReferralFull()) {
            return new RelatedWorkThenReferralQueryDataSource(
                ctx,
                selectionCriteria,
                previousDataSource,
                true,
                false,
                false,
                selectionCriteria.fetchHactSessionData()
            );
        } else {
            return new SupportWorkQueryDataSource(ctx, selectionCriteria);
        }
    }
}

export class SupportWorkSnapshotReportCapability extends BaseWorkReportCapability {
    override getReportEntity(): ReportEntity {
        return ReportEntity.SupportWorkSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new SupportWorkLatestSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class RiskWorkSnapshotReportCapability extends BaseWorkReportCapability {
    override getReportEntity(): ReportEntity {
        return ReportEntity.RiskWorkSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RiskWorkLatestSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class CustomFormSnapshotLatestReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.CustomFormSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        // convert to ReferralAggregate
        if (selectionCriteria.fetchReferral()) {
            return new RelatedWorkThenReferralQueryDataSource(
                ctx,
                selectionCriteria,
                previousDataSource,
                false,
                false,
                true,
                selectionCriteria.fetchHactSessionData()
            );
        } else {
            return new CustomFormLatestQueryDataSource(ctx, selectionCriteria);
        }
    }
}

export class CustomFormReportCapability extends BaseWorkReportCapability {
    getReportEntity() {
        return ReportEntity.CustomFormWork;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        // convert to ReferralAggregate
        return new RelatedWorkThenReferralQueryDataSource(
            ctx,
            selectionCriteria,
            previousDataSource,
            false,
            false,
            true,
            selectionCriteria.fetchHactSessionData()
        );

        /*
                if (selectionCriteria.fetchReferral()) {
                    return new RelatedWorkThenReferralQueryDataSource(ctx, selectionCriteria, previousDataSource,
                                                                      false, false, true, selectionCriteria.fetchHactSessionData());
                } else {
                    return new CustomFormLatestQueryDataSource(ctx, selectionCriteria);
                }
        */
    }
}

export class RiskWorkReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.RiskWork;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RiskWorkQueryDataSource(ctx, selectionCriteria);
    }
}

export class SupportRiskWorkReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.SupportRiskWork;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new SupportRiskWorkQueryDataSource(ctx, selectionCriteria);
    }
}

export class SupportFlagsReportCapability extends BaseWorkReportCapability {
    getReportEntity() {
        return ReportEntity.SupportFlags;
    }

    /* commented since we already have referralStatus for 'live'
    allowableCriteria(): ReportCriteriaAllowableBuilder {
        return super.allowableCriteria()
            .withEntityStatusOptions([{display: "live (as at date)", propertyName: "liveAtEnd"}]);
    }
    */

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new SupportFlagsQueryDataSource(ctx, selectionCriteria);
    }
}

export class RiskFlagsReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.RiskFlags;
    }

    /* commented since we already have referralStatus for 'live'
    allowableCriteria(): ReportCriteriaAllowableBuilder {
        return super.allowableCriteria()
            .withEntityStatusOptions([{display: "live (as at date)", propertyName: "liveAtEnd"}]);
    }
    */

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RiskFlagsQueryDataSource(ctx, selectionCriteria);
    }
}

export class RiskRatingsReportCapability extends BaseWorkReportCapability {
    override getReportEntity() {
        return ReportEntity.RiskRatings;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RiskRatingsQueryDataSource(ctx, selectionCriteria);
    }
}

export class GroupedWorkReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.GroupedWorkAnalysis;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withGeographicalArea()
            .withReferralStatus();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        // this isn't the grouped property path
        return [new PropertyPath("workDate", "work date")]; // as per report definitions
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new GroupedWorkQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(
            messages,
            "reportEntity." + ReportEntity[ReportEntity.GroupedWorkAnalysis]
        );

        let reportPathText = "'took place on' (grouped by ";
        // groupBy specifies the server property to group by, so we need a little translation.
        // The options are defined in ReferalController.getFieldExpression
        if ("serviceRecipient.service" == selectionCriteria.getDto().groupBy) {
            reportPathText = reportPathText.concat("service");
        } else {
            // author is the other option so far
            reportPathText = reportPathText.concat(selectionCriteria.getDto().groupBy || "");
        }
        reportPathText = reportPathText.concat(")");

        let referralStatusPathText = ReferralReportCapability.getReferralStatusPath(
            reportCriteria,
            this.isReferralBasedReport()
        );
        if (referralStatusPathText) {
            reportPathText = reportPathText.concat(" and ").concat(referralStatusPathText);
        }

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

/**
 * Load questionnaire work first, then referral data if requested.
 * NB getDataSource switches depending on what is requested.
 */
export class QuestionnaireReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Questionnaire;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        const criteria = new ReportCriteriaAllowableBuilder().withFrom().withTo();
        if (selectionCriteria.fetchStaff()) {
            return criteria;
        }
        if (selectionCriteria.fetchServiceRecipient()) {
            return criteria;
        }
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withGeographicalArea()
            .withReferralStatus();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [new PropertyPath("workDate", "work date")]; // as per report definitions
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        // convert to ReferralAggregate
        if (selectionCriteria.fetchReferral()) {
            return new RelatedWorkThenReferralQueryDataSource(
                ctx,
                selectionCriteria,
                previousDataSource,
                false,
                true,
                false,
                selectionCriteria.fetchHactSessionData()
            );
        } else {
            return new QuestionnaireQueryDataSource(ctx, selectionCriteria);
        }
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.Questionnaire]];
        let reportPathText = "'took place on'";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

abstract class QuestionnaireSnapshotReportCapability {
    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return (
            new ReportCriteriaAllowableBuilder()
                .withFrom()
                .withTo()
                // we can't add this here, since doing so allows the user to choose 'at end' dates
                // which means the 'from' date is knocked off and the report fails (see 'cleanCriteria')
                // actually, this happens in ReportCriteriaDtoFactory.getCriteria -> createCriteria -> getDateRange
                // calls ByStatusAtEnd which assumes an at-end doesn't have a from, and clears it.
                // So, either the report could default to 1970 (server side?) or we allow the from date
                // if the report is not primarily referral based - so 'a smart step progress report
                // in a period where the referral is live at the end' - seems perfectly valid
                // which seems fine to break the link between start/end and referral status (if the report is not referral-based)
                // it just might need some automated testing
                //.withReferralStatus()
                .withServiceProject()
        );
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    abstract getReportEntity(): ReportEntity;

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(messages, this.getReportEntity());
        let reportPathText = "work date";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class QuestionnaireMultiSnapshotReportCapability
    extends QuestionnaireSnapshotReportCapability
    implements ReportEntityCapability
{
    override getReportEntity(): ReportEntity {
        return ReportEntity.QuestionnaireMultiSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new QuestionnaireMultiSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class QuestionnaireLegacyMultiSnapshotReportCapability
    extends QuestionnaireMultiSnapshotReportCapability
    implements ReportEntityCapability
{
    override getReportEntity(): ReportEntity {
        return ReportEntity.QuestionnaireSnapshot;
    }
}

export class QuestionnaireSingleSnapshotReportCapability
    extends QuestionnaireSnapshotReportCapability
    implements ReportEntityCapability
{
    override allowableCriteria(
        selectionCriteria: SelectionCriteria
    ): ReportCriteriaAllowableBuilder {
        const criteria = new ReportCriteriaAllowableBuilder().withTo();
        if (selectionCriteria.fetchServiceRecipient()) {
            return criteria;
        }
        return (
            new ReportCriteriaAllowableBuilder()
                .withTo()
                // referral status works here as this is a snapshot (single date) only
                // and doesn't have the issue of hiding a from date - see commit comments
                .withReferralStatus()
                //.withEntityStatus()
                //.withEntityStatusOptions([{display: "live (as at date)", propertyName: "liveAtEnd"}])
                .withServiceProject()
        );
    }

    override getReportEntity(): ReportEntity {
        return ReportEntity.QuestionnaireSingleSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new QuestionnaireLatestSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class AnswersGroupedByQuestionReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.AnswersByQuestion;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new AnswersGroupedByQuestionAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(messages, this.getReportEntity());
        let reportPathText = " (grouped by question)";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }

    isReferralBasedReport(): boolean {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withTo()
            .withServiceProject()
            .withReferralStatus();
    }

    allowablePropertyPaths(sessionData: SessionData): PropertyPath[] {
        return [];
    }
}

abstract class SmartStepSnapshotReportCapability {
    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return (
            new ReportCriteriaAllowableBuilder()
                .withFrom()
                .withTo()
                // we can't add this here, since doing so allows the user to choose 'at end' dates
                // which means the 'from' date is knocked off and the report fails (see 'cleanCriteria')
                // actually, this happens in ReportCriteriaDtoFactory.getCriteria -> createCriteria -> getDateRange
                // calls ByStatusAtEnd which assumes an at-end doesn't have a from, and clears it.
                // So, either the report could default to 1970 (server side?) or we allow the from date
                // if the report is not primarily referral based - so 'a smart step progress report
                // in a period where the referral is live at the end' - seems perfectly valid
                // which seems fine to break the link between start/end and referral status (if the report is not referral-based)
                // it just might need some automated testing
                //.withReferralStatus()
                .withServiceProject()
        );
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    abstract getReportEntity(): ReportEntity;

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText = getReportEntityText(messages, this.getReportEntity());
        let reportPathText = "work date";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class SmartStepMultiSnapshotReportCapability
    extends SmartStepSnapshotReportCapability
    implements ReportEntityCapability
{
    override getReportEntity(): ReportEntity {
        return ReportEntity.SmartStepMultiSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new SmartStepMultiSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class SmartStepSingleSnapshotReportCapability
    extends SmartStepSnapshotReportCapability
    implements ReportEntityCapability
{
    override allowableCriteria(
        selectionCriteria: SelectionCriteria
    ): ReportCriteriaAllowableBuilder {
        return (
            new ReportCriteriaAllowableBuilder()
                .withTo()
                // referral status works here as this is a snapshot (single date) only
                // and doesn't have the issue of hiding a from date - see commit comments
                .withReferralStatus()
                //.withEntityStatus()
                //.withEntityStatusOptions([{display: "live (as at date)", propertyName: "liveAtEnd"}])
                .withServiceProject()
        );
    }

    override getReportEntity(): ReportEntity {
        return ReportEntity.SmartStepSingleSnapshot;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new SmartStepLatestSnapshotQueryDataSource(ctx, selectionCriteria);
    }
}

export class ReviewReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Review;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withEntityStatus() // can be 'liveAtEnd' to get current reviews
            .withEntityStatusOptions([{display: "live (as at date)", propertyName: "liveAtEnd"}])
            .withSelectionPropertyPath();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [new PropertyPath("startDate", "start date")]; // as per report definitions
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReviewQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.Review]];
        let reportPathText = "start date";

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class AddressHistoryReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.AddressHistory;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new AddressHistoryQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.AddressHistory]];
        let reportSelectionText = "on move in/out dates";
        return reportEntityText.concat(" ".concat(reportSelectionText));
    }
}

export class FinanceChargeReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.FinanceCharge;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new FinanceChargeQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.FinanceCharge]];
        let reportSelectionText = "on move in/out dates";
        return reportEntityText.concat(" ".concat(reportSelectionText));
    }
}

export class FinanceReceiptReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.FinanceReceipt;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new FinanceReceiptQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.FinanceReceipt]];
        let reportSelectionText = "on received dates";
        return reportEntityText.concat(" ".concat(reportSelectionText));
    }
}

export class ActivityAttendanceReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.ActivityAttendance;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo().withServiceProject();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [new PropertyPath("activityDate", "activity date")]; // as per report definitions
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ActivityAttendanceQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ActivityAttendance]];
        let reportPathText = "activity date";

        reportPathText = reportPathText.concat(
            ReferralReportCapability.getReferralStatusPath(
                reportCriteria,
                this.isReferralBasedReport()
            )
        );

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class ServiceRecipientCommandReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.ServiceRecipientCommand;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withUsername();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [new PropertyPath("timestamp", "timestamp")]; // as per report definitions
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ServiceRecipientCommandQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ServiceRecipientCommand]];
        let reportPathText = "created time";

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

export class AgencyReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Agency;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new AgencyQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.Agency]];
        return reportEntityText;
    }
}

export class ProfessionalReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Professional;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ProfessionalQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        return messages["reportEntity." + ReportEntity[ReportEntity.Professional]];
    }
}

export class AssociatedContactsReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.AssociatedContact;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withServiceProject();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return null;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ServiceRecipientAssociatedContactDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        return messages["reportEntity." + ReportEntity[ReportEntity.AssociatedContact]];
    }
}

export class TaskStatusReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.TaskStatus;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder()
            .withFrom()
            .withTo()
            .withServiceProject()
            .withGeographicalArea()
            .withReferralStatus();
        //.withEntityStatus()
        //.withEntityStatusOptions([{display: "due or completed", propertyName: "dueDateOrCompleted"}])
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new TaskStatusQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        return messages["reportEntity." + ReportEntity[ReportEntity.TaskStatus]];
    }
}

export class RotaDamandReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.RotaDemand;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RotaDemandQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.RotaDemand]];

        return reportEntityText;
    }
}

export class RotaAgreementReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.RotaAgreement;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RotaAgreementQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.RotaAgreement]];

        return reportEntityText;
    }
}

export class RotaScheduleReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.RotaSchedule;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RotaScheduleQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.RotaSchedule]];

        return reportEntityText;
    }
}

export class EventResourceReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.EventResource;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo().withServiceProject();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new EventQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        const lookup = "reportEntity." + ReportEntity[ReportEntity.EventResource];
        let reportEntityText = messages[lookup as keyof Messages];
        let reportSelectionText = "on event dates";
        return reportEntityText.concat(" ".concat(reportSelectionText));
    }
}

// ReferralBased because the server side of Client queries uses the referral criteria to get the clients
// NB This may no longer be the case, and should perhaps not be ReferralBased - search "selectionRootEntity": "Client"
export class ClientReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.Client;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ClientQueryDataSource(ctx, selectionCriteria, previousDataSource);
    }
}

export class TasksByMonthReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.TasksByMonth;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new TasksByMonthDataSource(ctx, selectionCriteria, previousDataSource);
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        let reportEntityText = messages["reportEntity." + ReportEntity[ReportEntity.TasksByMonth]];
        let reportPathText = " (grouped by due date)";

        let referralStatusPathText = ReferralReportCapability.getReferralStatusPath(
            reportCriteria,
            this.isReferralBasedReport()
        );
        if (referralStatusPathText) {
            reportPathText = reportPathText.concat(" and ").concat(referralStatusPathText);
        }

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

/**
 * ReferralBased, but grouped per month and service - see ReportController.
 * Therefore an array is returned whose length is the number of months and services in the range.
 */
export class ReferralsByMonthReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsByMonth;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsTimeSeriesDataSource(ctx, selectionCriteria, previousDataSource);
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsByMonth]];

        let reportPathText =
            selectionCriteria.getDto().groupBy == "exitedDate"
                ? " (grouped by exited date)"
                : " (grouped by received date)";

        let referralStatusPathText = ReferralReportCapability.getReferralStatusPath(
            reportCriteria,
            this.isReferralBasedReport()
        );
        if (referralStatusPathText) {
            reportPathText = reportPathText.concat(" and ").concat(referralStatusPathText);
        }

        return reportEntityText.concat(" ".concat(reportPathText));
    }
}

/**
 * ReferralBased, but grouped per service - see ReportController.
 * Therefore an array is returned whose length is the number of services in the query.
 */
export class ReferralsGroupedByServiceReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsByService;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsGroupedByServiceAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsByService]];
        let reportPathText = " (grouped by service)";
        return this.describeEntityAndPathGrouped(reportCriteria, reportPathText, reportEntityText);
    }
}

/**
 * ReferralBased, but grouped per agency source - see ReportController.
 * Therefore an array is returned whose length is the number of agency sources in the query.
 */
export class ReferralsGroupedBySourceReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsBySource;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsGroupedBySourceAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsBySource]];
        let reportPathText = " (grouped by source)";
        return this.describeEntityAndPathGrouped(reportCriteria, reportPathText, reportEntityText);
    }
}

export class ReferralsGroupedByEthnicityReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsByEthnicity;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsGroupedByEthnicityAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsByEthnicity]];
        let reportPathText = " (grouped by ethnicity)";
        return this.describeEntityAndPathGrouped(reportCriteria, reportPathText, reportEntityText);
    }
}

export class ReferralsGroupedBySexualOrientationReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsBySexualOrientation;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsGroupedBySexualOrientationAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsBySexualOrientation]];
        let reportPathText = " (grouped by sexual orientation)";
        return this.describeEntityAndPathGrouped(reportCriteria, reportPathText, reportEntityText);
    }
}

export class ReferralsGroupedByDisabilityReportCapability
    extends ReferralBasedReportCapability
    implements ReportEntityCapability
{
    override getReportEntity() {
        return ReportEntity.ReferralsByDisability;
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ReferralsGroupedByDisabilityAsTimeSeriesDataSource(
            ctx,
            selectionCriteria,
            previousDataSource
        );
    }

    override describeEntityAndPath(
        reportCriteria: ReportCriteriaDto,
        selectionCriteria: SelectionCriteria
    ) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        let reportEntityText =
            // @ts-ignore
            messages["reportEntity." + ReportEntity[ReportEntity.ReferralsByDisability]];
        let reportPathText = " (grouped by disability)";
        return this.describeEntityAndPathGrouped(reportCriteria, reportPathText, reportEntityText);
    }
}

export class BuildingReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Building;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new BuildingQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        return getReportEntityText(messages, this.getReportEntity());
    }
}

export class RepairReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.Repair;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder().withFrom().withTo();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new RepairQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        const lookup = "reportEntity." + ReportEntity[this.getReportEntity()];
        return messages[lookup as keyof Messages];
    }
}

export class UserReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.User;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new UserQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        return getReportEntityText(messages, this.getReportEntity());
    }
}

export class ServiceTypeReportCapability implements ReportEntityCapability {
    getReportEntity() {
        return ReportEntity.ServiceType;
    }

    isReferralBasedReport() {
        return false;
    }

    allowableCriteria(selectionCriteria: SelectionCriteria): ReportCriteriaAllowableBuilder {
        return new ReportCriteriaAllowableBuilder();
    }

    allowablePropertyPaths(sessionData: SessionData) {
        return [];
    }

    getDataSource(
        ctx: AnalysisContext,
        selectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>
    ) {
        return new ServiceTypeQueryDataSource(ctx, selectionCriteria);
    }

    describeEntityAndPath(reportCriteria: ReportCriteriaDto, selectionCriteria: SelectionCriteria) {
        const messages = getGlobalEccoAPI().sessionData.getMessages();
        // @ts-ignore
        return messages["reportEntity." + ReportEntity[ReportEntity.ServiceType]];
    }
}
