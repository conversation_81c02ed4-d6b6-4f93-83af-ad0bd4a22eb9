import {SelectionCriteriaDto} from "ecco-dto";
import {EccoDate} from "@eccosolutions/ecco-common";
import {ReportCriteriaDto} from "ecco-dto";
import {SelectionCriteria} from "./chart-domain";
import {ReferralStatusHandler} from "./referralStatusHandler";

export class ReportCriteriaDtoFactory {
    public static getReportCriteriaDto(spec: SelectionCriteria): ReportCriteriaDto {
        // prioritise the at end dates because with the status, that is all that is allowed
        // otherwise we end up with the selectorType overriding the report despite the user changing status
        // see DEV-582
        if (spec.getDto().entityStatus == "liveAtEnd") {
            // TODO have 'snapshot' here also, for where 'live' isn't appropriate
            return new ByStatusAtEnd(spec.getDto()).getCriteria();
        }
        if (ReferralStatusHandler.referralStatusAtEndDate(spec.getDto().referralStatus)) {
            return new ByStatusAtEnd(spec.getDto()).getCriteria();
        }

        return this.getDateSelectorType(spec.getDto());
    }

    public static getDateSelectorType(spec: SelectionCriteriaDto): ReportCriteriaDto {
        /**
         * DO NOT create new selectorTypes without handling the 'search criteria' ChartCriteriaForm
         * otherwise confusion reigns for the user.
         * At the moment the selectorTypes here revolve around default dates
         * which mean the user can change the search criteria dates accordingly.
         */
        switch (spec.selectorType) {
            case "byStartOfDay":
                return new ByStartOfDay(spec).getCriteria();
            case "byStartOfWeekMonday":
                return new ByStartOfWeekMonday(spec).getCriteria();
            case "byStartOfMonth":
                return new ByStartOfMonthDateRange(spec).getCriteria();
            case "byStartOfQuarter":
                return new ByStartOfQuarterDateRange(spec).getCriteria();
            case "byEndOfQuarter":
                return new ByEndOfQuarter(spec).getCriteria();
            default:
                if (spec.selectorType) {
                    throw new Error("Unsupported selectorType: " + spec.selectorType);
                }
                return new AbsoluteDateRange(spec).getCriteria();
        }
    }
}

class DateRange {
    constructor(public from: EccoDate | null, public to: EccoDate | null) {}
}

class ReportCriteriaEmpty {
    private criteria: ReportCriteriaDto | undefined;

    constructor(protected spec: SelectionCriteriaDto) {}

    protected getDateRange(): DateRange {
        return new DateRange(null, null);
    }

    protected createCriteria(): ReportCriteriaDto {
        var dateRange: DateRange = this.getDateRange();

        return {
            companyId: this.spec.companyId,
            clientGroupId: this.spec.clientGroupId,
            serviceGroupId: this.spec.serviceGroupId,
            serviceId: this.spec.serviceId,
            projectId: this.spec.projectId,
            serviceRecipientFilter: this.spec.serviceRecipientFilter,
            userId: this.spec.userId,
            username: this.spec.username,
            taskDefName: this.spec.taskDefName || "",
            supportEvidenceGroup: this.spec.supportEvidenceGroup,
            questionnaireEvidenceGroup: Array.isArray(this.spec.questionnaireEvidenceGroup)
                ? null
                : this.spec.questionnaireEvidenceGroup,
            questionnaireEvidenceGroupArr: Array.isArray(this.spec.questionnaireEvidenceGroup)
                ? this.spec.questionnaireEvidenceGroup
                : null,
            commandNameArr: this.spec.commandNameArr,
            customFormEvidenceGroup: this.spec.customFormEvidenceGroup,
            isChild: this.spec.isChild,
            geographicAreaIdSelected: this.spec.geographicAreaIdSelected,
            geographicAreaIds: this.spec.geographicAreaIds,
            from: dateRange.from ? dateRange.from.formatIso8601() : null,
            to: dateRange.to ? dateRange.to.formatIso8601() : null,
            selectionPropertyPath: this.spec.selectionPropertyPath,
            entityStatus: this.spec.entityStatus,
            referralStatus: this.spec.referralStatus,
            newReferralsOnly: this.spec.newReferralsOnly,
            includeRelated: this.spec.includeRelated
        };
    }

    public getCriteria(): ReportCriteriaDto {
        if (!this.criteria) {
            this.criteria = this.createCriteria();
            this.applySpecToCriteria();
        }
        return this.criteria;
    }

    private applySpecToCriteria() {
        this.criteria && (this.criteria.optionalData = this.spec.fetchRelatedEntities);
    }
}

class AbsoluteDateRange extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        var dateFrom = EccoDate.parseIso8601(this.spec.absoluteFromDate!);
        var dateTo = EccoDate.parseIso8601(this.spec.absoluteToDate!);
        return new DateRange(dateFrom, dateTo);
    }
}

class ByStartOfMonthDateRange extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        var relativePeriodMonths = 1;
        return getDateRangeForMonths(relativePeriodMonths, this.spec);
    }
}

class ByStartOfQuarterDateRange extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        var relativePeriodMonths = 3;
        return getDateRangeForMonths(relativePeriodMonths, this.spec);
    }
}

class ByStartOfDay extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        const relativePeriodDays = 1;
        return getDateRangeForDays(relativePeriodDays, this.spec);
    }
}

class ByEndOfQuarter extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        const relativePeriodMonths = 3;
        return getDateRangeForMonths(relativePeriodMonths, this.spec, true);
    }
}

class ByStartOfWeekMonday extends ReportCriteriaEmpty {
    protected override getDateRange(): DateRange {
        var relativePeriodWeeks = 1;
        return getDateRangeForWeeks(relativePeriodWeeks, this.spec);
    }
}

/**
 * Its more accurate to use 'byReferralStatus' as a snapshot date
 * - the terminology we have been using for snapshots is 'atEnd' - see ecco-dto.ts ReferralStatus.
 * So reports byReferralStatus use the end/to date which can be changed in the search criteria.
 * However, we need to allow the 'from' date in case we switch from 'liveAtEnd' to 'live' - which allows a 'from'
 * but the 'from' gets removed here. However, adding the from date meant the description etc still has 'as at' with from.
 * Therefore the fix is to prioritise the status 'asAt' over the selectorType (which is only a client-side concept anyway).
 */
class ByStatusAtEnd extends ReportCriteriaEmpty {
    constructor(spec: SelectionCriteriaDto) {
        super(spec);
    }
    protected override getDateRange(): DateRange {
        // if we have a dateSelectorType, then move the 'as at' date along with the 'prev/next'
        if (this.spec.selectorType) {
            const toDateIso = ReportCriteriaDtoFactory.getDateSelectorType(this.spec).to;
            return new DateRange(null, EccoDate.parseIso8601(toDateIso));
        } else {
            let to = this.spec.absoluteToDate
                ? EccoDate.parseIso8601(this.spec.absoluteToDate)
                : EccoDate.todayLocalTime();
            return new DateRange(null, to);
        }
    }
}

function getDateRangeForMonths(
    relativePeriodMonths: number,
    spec: SelectionCriteriaDto,
    absoluteDatesAreFixed = false
) {
    const monthsMoveStart = relativePeriodMonths * spec.relativeStartIndex!;
    const monthsMoveEnd = relativePeriodMonths * spec.relativeEndIndex!;

    let dateFrom: EccoDate;
    let dateTo: EccoDate;

    if (spec.absoluteFromDate && spec.absoluteToDate) {
        dateFrom = EccoDate.parseIso8601(spec.absoluteFromDate).addMonths(monthsMoveStart);
        dateTo = EccoDate.parseIso8601(spec.absoluteToDate).addMonths(monthsMoveEnd);
    } else {
        let startOfMonth = EccoDate.todayLocalTime().withDate(1);
        let startOfThisPeriodMonth = startOfMonth.getMonth() - 1;
        startOfThisPeriodMonth -= startOfThisPeriodMonth % relativePeriodMonths; // results in 0,3,6,9 for quarterly
        startOfThisPeriodMonth += 1; // 1 indexed
        dateFrom = startOfMonth.withMonth(startOfThisPeriodMonth + monthsMoveStart);
        dateTo = startOfMonth.withMonth(startOfThisPeriodMonth + monthsMoveEnd).subtractDays(1);
    }

    if (absoluteDatesAreFixed) {
        if (spec.absoluteFromDate) {
            dateFrom = EccoDate.parseIso8601(spec.absoluteFromDate);
        }
        if (spec.absoluteToDate) {
            dateTo = EccoDate.parseIso8601(spec.absoluteToDate);
        }
    }

    return new DateRange(dateFrom, dateTo);
}

/**
 * @param relativePeriodWeeks - number of weeks that 'relative' part of spec relates to and how many we
 *                              move by for prev/next buttons.
 */
function getDateRangeForWeeks(relativePeriodWeeks: number, spec: SelectionCriteriaDto) {
    var weeksMoveStart = relativePeriodWeeks * spec.relativeStartIndex!;
    var weeksMoveEnd = relativePeriodWeeks * spec.relativeEndIndex!;

    if (spec.absoluteFromDate && spec.absoluteToDate) {
        var dateFrom = EccoDate.parseIso8601(spec.absoluteFromDate).addDays(7 * weeksMoveStart);
        var dateTo = EccoDate.parseIso8601(spec.absoluteToDate).addDays(7 * weeksMoveEnd);
        return new DateRange(dateFrom, dateTo);
    } else {
        var relativeStartWeek: EccoDate | null = null;

        // if a relativeStartDate is provided, then use that as a base for the
        // relativeStartIndex to count from, until the current period is found
        if (spec.relativeStartDate) {
            var now = EccoDate.todayLocalTime();
            relativeStartWeek = EccoDate.parseIso8601(spec.relativeStartDate);
            // ensure we have at least one week otherwise we get infinit loop
            var countWeeks = Math.max(spec.relativeEndIndex! - spec.relativeStartIndex!, 1);
            if (relativeStartWeek.earlierThan(now)) {
                while (relativeStartWeek.addDays(7 * countWeeks).earlierThan(now)) {
                    relativeStartWeek = relativeStartWeek.addDays(7 * countWeeks);
                }
            }
        } else {
            var diffToMon = EccoDate.todayLocalTime().getDayOfWeek() - 1;
            if (diffToMon < 0) {
                diffToMon += 7;
            }
            relativeStartWeek = EccoDate.todayLocalTime().subtractDays(diffToMon);
        }

        var dateFrom = relativeStartWeek.addDays(7 * weeksMoveStart);
        var dateTo = relativeStartWeek.addDays(7 * weeksMoveEnd);
        return new DateRange(dateFrom, dateTo);
    }
}

/**
 * @param relativePeriodWeeks - number of weeks that 'relative' part of spec relates to and how many we
 *                              move by for prev/next buttons.
 */
function getDateRangeForDays(
    relativePeriodDays: number,
    spec: SelectionCriteriaDto,
    absoluteDatesAreFixed = false
) {
    const daysMoveStart = relativePeriodDays * spec.relativeStartIndex!;
    const daysMoveEnd = relativePeriodDays * spec.relativeEndIndex!;

    let dateTo, dateFrom;
    if (spec.absoluteFromDate && spec.absoluteToDate) {
        dateFrom = EccoDate.parseIso8601(spec.absoluteFromDate).addDays(daysMoveStart);
        dateTo = EccoDate.parseIso8601(spec.absoluteToDate).addDays(daysMoveEnd);
    } else {
        // relativeStartDate is a bit meaningless on a daily report
        let relativeStartDay: EccoDate = EccoDate.todayLocalTime();
        dateFrom = relativeStartDay.addDays(daysMoveStart);
        dateTo = relativeStartDay.addDays(daysMoveEnd);
    }

    if (absoluteDatesAreFixed) {
        if (spec.absoluteFromDate) {
            // ** CHECK
            dateFrom = EccoDate.parseIso8601(spec.absoluteFromDate);
        }
        if (spec.absoluteToDate) {
            // ** CHECK
            dateTo = EccoDate.parseIso8601(spec.absoluteToDate);
        }
    }

    return new DateRange(dateFrom, dateTo);
}
