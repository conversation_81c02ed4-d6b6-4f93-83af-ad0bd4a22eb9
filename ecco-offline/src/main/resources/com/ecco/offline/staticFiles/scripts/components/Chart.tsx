import $ = require("jquery");
import * as React from "react";
import { findDOMNode } from "react-dom";
import ChartControl = require("../reports/charts/ChartControl");
import {SelectionCriteriaDto} from "ecco-dto";

interface ReportProps {
    chartUuid: string
    override?: Partial<SelectionCriteriaDto> | undefined
    border?: boolean | undefined
    autoRun?: boolean | undefined
}

/**
 * Charts are used in referralViewTabs.tag and dashboard.tag as below:
        <div class="container-fluid">
            <div class="row ecco-rounded-row">
            <reports:embedded-chart
                chartUuid="01400000-0000-babe-babe-dadafee1600d"
                serviceRecipientFilter="serviceRecipient:${referral.serviceRecipient.id}"
                chartClasses="col-sm-12"
                badgeClasses="col-sm-6"/>
            </div>
        </div>
    The above assumes that the bootstrap styles are already loaded.

    The /charts/ url uses charts/handler.jsp to load a rich-client-page
    with heads/dataTablesBootstrapCss.jsp and require module bootstrap.
    The rich-client-page is the bootstrap loading page.

    The chart-page.tag is used by archived-reporting.
 */

class Chart extends React.Component<ReportProps, {}> {

    // ref happens before this is called
    override componentDidMount() {
        // see https://medium.com/@shuvohabib/using-jquery-in-react-component-the-refs-way-969de9aa651f
        // or https://teamgaslight.com/blog/wrapping-jquery-with-react
        const el = findDOMNode(this.refs.chartData);
        const elNav = findDOMNode(this.refs.chartNav);
        const showRunButton = !this.props.autoRun;
        const control = new ChartControl(this.props.chartUuid, this.props.override,
            "col-md-offset-2 col-md-8 col-lg-offset-0 col-lg-6",
            undefined, undefined, showRunButton); // override defaults in ReportStagesControl
        control.setTitleHandler( ($title) => {
            if (elNav.hasChildNodes()) {
                elNav.firstChild.replaceWith($title[0])
            }
            else {
                elNav.appendChild($title[0]);
            }
        });
        control.attach($(el));
        control.load();
    }

    override render() {
        return(
            <div className="container-fluid" style={{paddingBottom: "25px"}}>
                <div className="row">
                    <div className="col-xs-12">
                        <div className={this.props.border && "ecco-rounded"} style={{paddingBottom: "25px"}}>
                            <div ref="chartNav">&nbsp;</div>
                            <div ref="chartData">&nbsp;</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

}

export default Chart;