import {applicationRootPath, resourceRootPath} from "application-properties";
import {apiClient} from "ecco-components";
import {EccoModal, link} from "ecco-components-core";
import {
    SessionData, StaffDto,
    StaffJobDto,
    WorkersAjaxRepository
} from "ecco-dto";
import * as React from "react"
import ReactBootstrap = require("react-bootstrap");
import {SessionDataService} from "../feature-config/SessionDataService";
import Button = ReactBootstrap.Button;
import {EccoDate} from "@eccosolutions/ecco-common";
import {NewWorkerJobWizard} from "./NewWorkerJobWizard";

const workerRepository = new WorkersAjaxRepository(apiClient);

interface WorkerProps {
    workerId: number;
    //onSelectedJobSrIdHref?: (sessionData: SessionData, job: StaffJobDto) => string;
    onSelectedJob?: ((sessionData: SessionData, job: StaffJobDto) => void) | undefined;
    onClose: () => void;
}

interface WorkerState {
    worker: StaffDto;
    showWorkerLink?: boolean | undefined;
    sessionData: SessionData;
}

export function openJobDirect(srId: number, newTab = false) {
    var href = new URL(`${applicationRootPath}nav/r/main/staff/job/${srId}/`, location.href).href;
    if (newTab) {
        window.open(href, '_blank');
    } else {
        location.href = href;
    }
}

/**
 * Renders a button link of the staff name, which when clicked shows that staff's jobs.
 * Returns the selected jobs via the onSelected callback.
 */
export class WorkerJobsPopup extends React.Component<WorkerProps, WorkerState> {

    constructor(props, context) {
        super(props, context);

        this.state = {
            worker: null,
            sessionData: null
        };
    }

    public override componentDidMount() {
        SessionDataService.getFeatures().then(sessionData =>
                // TODO we show all jobs, but readOnly should be applied
                workerRepository.findOneWorker(this.props.workerId).then(w => {
                    this.setState( {
                        sessionData: sessionData,
                        worker: w
                    })
                })
        );
    }

    override render() {
        const {workerId, onSelectedJob} = this.props;
        const {worker, sessionData} = this.state;
        const existingOnly = false;

        if (!worker) {
            return null;
        }
        return (
                <EccoModal
                    title={""}
                    fullScreen={false}
                    maxWidth="xs"
                    show={true}
                    onEscapeKeyDown={() => this.props.onClose()}
                >
                    <>
                    {/*<div>
                        <i className="pull-right fa fa-close" onClick={() => this.props.onClose()}/>
                    </div>*/}
                    {!existingOnly && <Button bsSize="medium" bsStyle="link"
                                              onClick={() => {
                                                  this.props.onClose();
                                                  NewWorkerJobWizard.popup(worker.workerId, j => openJobDirect(j.serviceRecipient.serviceRecipientId), undefined, undefined, -200);
                                              }}>
                        new job</Button>}
                    <div style={{padding: "10px"}}></div>
                    {worker.jobs && worker.jobs.length == 0 && (<div>no jobs</div>)}
                    {worker.jobs && worker.jobs.length > 0 && (<div>jobs</div>)}
                    {worker.jobs && worker.jobs.map(job => {
                        // TODO !job._readOnly;
                        const isAccessbile = true;
                        const svcCatName = sessionData.getServiceCategorisationName(job.serviceRecipient.serviceAllocationId);
                        const SummaryLabel = <>{svcCatName} - {WorkerJobsPopup.summariseStatus(job)} (j-id: {job.id})</>;
                        return (
                                <div key={job.id.toString()}>
                                    {isAccessbile
                                            ? link(SummaryLabel, () => onSelectedJob(sessionData, job))
                                                // onSelectedReferralSrIdHref && onSelectedReferralSrIdHref(sessionData, referral)
                                            : <span>
                                  {SummaryLabel}
                                </span>
                                    }
                                </div>
                        );
                    })}
                    {(!worker.jobs) && (<img className="spinner"
                                           src={resourceRootPath + "themes/ecco/images/loading.gif"}
                                           style={{ width: 16, height: 16 }}/>)
                    }
                    </>
                </EccoModal>
        );
    }

    public static summariseStatus(item: StaffJobDto) {
        return `${item.startDate ? EccoDate.iso8601ToFormatShort(item.startDate) : ''}`;
    }
}
