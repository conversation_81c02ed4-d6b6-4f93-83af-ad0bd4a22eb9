import {
    ClientDetailEditor,
    handleLazy,
    LoadSRWithEntitiesContext,
    ModalForCommandForm,
    ServiceRecipientContext,
    ServiceRecipientWithEntitiesContext,
    TaskIntegrations,
    TasksControlNG,
    TasksControlProps,
    withCommandForm
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {SessionData, TaskNames} from "ecco-dto";
import {TaskDto, TaskWithTitle} from "ecco-dto/workflow-dto";
import * as React from "react";
import ChecklistEvidenceForm from "../../evidence/checklist/ChecklistEvidenceForm";
import {mountWithServices} from "../../offline/ServicesContextProvider";
import {AcceptOnServiceDialog, AppropriateReferralDialog} from "../../referral/components/AcceptOrSignpostForms";
import {AssessmentDateDialog} from "../../referral/components/AssessmentDateDialog";
import {EmergencyDetailsForm} from "../../referral/components/EmergencyDetailsForm";
import {FundingDialog} from "../../referral/components/FundingForm";
import {PendingStatusDialog} from "../../referral/components/PendingStatusForm";
import {ReferralProjectDialog} from "../../referral/components/ReferralProject";
import {ReferralSourceDialog} from "../../referral/components/ReferralSource";
import {
    getAgreement1Form,
    getAgreement2Form,
    getAgreement3Form,
    getAgreement4Form,
    getAgreement5Form,
    getAgreement6Form,
    getAgreement7Form,
    getAgreement8Form,
    getAgreement9Form,
    getAgreement10Form,
    getConsentForm,
    getDataProtectionForm
} from "../../referral/components/signedAgreements";
import {getCustomFormWithSignatureForm} from "../../referral/components/signedCustomForm";
import {StartOnServiceDialog} from "../../referral/components/StartOnServiceForm";
import {AgreementsListEditor} from "../../service-agreements/ServiceAgreementsList";
import {EvidencePage, EvidencePageLoaderForCommandForm} from "ecco-evidence";
import {useHactIntegration} from "../../evidence/hact/useHactIntegration";
import {useHistoryLinkEventIntegration} from "../../evidence/questionnaire/useHistoryLinkEventIntegration";
import ReferralTaskAllocateControl = require("../../referral/aspects/ReferralTaskAllocateControl");
import ReferralTaskSPDataControl = require("../../referral/aspects/ReferralTaskSPDataControl");

const ReviewChoiceDelegator = React.lazy(() => import("../../evidence/support/components/ReviewChoices"));
const ReviewScheduleDialog = React.lazy(() => import("../../evidence/support/components/ReviewSchedule"));
const ReferralDetailsDialog = React.lazy(() => import("../../referral/components/ReferralDetailsForm"));
const WaitingListCriteriaForm = React.lazy( () => import("../../referral/components/WaitingListCriteriaForm").then(i => ({default: i.WaitingListCriteriaForm})));
const ReferralExitDialog = React.lazy(() => import("../../referral/components/ReferralExit"));
const CustomForm = React.lazy( () => import("../../referral/components/CustomForm").then(i => ({default: i.CustomForm})));
const DeliveredByDialog = React.lazy(() => import("../../referral/components/DeliveredBy"));
const WorkerJobDetailForm = React.lazy(() => import("../../hr/WorkerJobDetailForm"));


export function loadServiceRecipientAndAttach(el: Element,
                                              srId: number) {
    mountWithServices(
            <LoadSRWithEntitiesContext srId={srId}>
                <TasksControl srId={srId}/>
            </LoadSRWithEntitiesContext>,
            el);
}

/** This expects a React component to be returned to be rendered either in a content panel (either in-page or dialog)
 * If buttons such as save/cancel are to be used, then
 * they should use ReactDOM.createPortal(theComponents, document.getElementById("editor-actions")) within
 * the render() method of the component.
 * @param onSaved Callback to refresh etc
 * @return the JSX element to be displayed or null having already popped up a modal
 */
function lookupForm(context: ServiceRecipientContext, task: TaskWithTitle, onSaved: () => void): JSX.Element | null {


    const sessionData = context.serviceRecipient.features;
    const taskDef = sessionData.getTaskDefinitionByName(task.taskName);
    if (SessionData.isTaskDefinitionDedicated(taskDef)) {
        return lookupFormDedicatedTasks(context.serviceRecipient.serviceRecipientId, task, onSaved);
    } else if (sessionData.isTaskDefinitionAgreement(taskDef)) {
        return lookupAgreementTasks(context, task, onSaved);
    } else if (sessionData.isTaskDefinitionEvidence(taskDef)) {
        return lookupEvidenceTasks(context, task, onSaved);
    } else {
        return <b>TODO: lookupForm TYPE missing for {task.taskName}</b>;
    }
}

/** Either return the JSX element to be displayed or null having already popped up a modal */
function lookupFormDedicatedTasks(serviceRecipientId: number, task: TaskWithTitle, onSaved: () => void, formRef = () => {}): JSX.Element | null {

    // TODO migrate to lookupForm - here we pop modals for old code
    const popupOnComplete = () => {
    }; // we don't need to do the old reloadUrl, since we currently clear the cache on TaskList.render

    switch (task.taskName) {
        case TaskNames.allocateToServices:
            ReferralTaskAllocateControl.showInModal(serviceRecipientId, popupOnComplete, task.taskHandle);
            break;
        case TaskNames.initialSPData:
        case TaskNames.exitSPData:
            ReferralTaskSPDataControl.showInModal(serviceRecipientId, task.taskName, task.taskHandle, popupOnComplete);
            break;

        case TaskNames.allocateWorker:
            return <StartOnServiceDialog allocateWorkerOnly={true} taskName={task.taskName} taskHandle={task.taskHandle}/>;
        case TaskNames.assessmentDate:
            return <AssessmentDateDialog task={task}/>;
        case TaskNames.close:
            return <ReferralExitDialog serviceRecipientId={serviceRecipientId} task={task} onSaved={onSaved} />;
        case TaskNames.decideFinal:
            return <AcceptOnServiceDialog task={task}/>;
        case TaskNames.deliveredBy:
            return <DeliveredByDialog serviceRecipientId={serviceRecipientId} task={task} />
        case TaskNames.emergencyDetails:
            return <EmergencyDetailsForm serviceRecipientId={serviceRecipientId} taskHandle={task.taskHandle} readOnly={false}/>;
        case TaskNames.funding:
            return <FundingDialog taskHandle={task.taskHandle}/>;
        case TaskNames.pendingStatus:
            return <PendingStatusDialog serviceRecipientId={serviceRecipientId} taskHandle={task.taskHandle} readOnly={false}/>;
        case TaskNames.project:
            return <ReferralProjectDialog serviceRecipientId={serviceRecipientId} task={task}/>;
        case TaskNames.projectRegion:
            return <ReferralProjectDialog serviceRecipientId={serviceRecipientId} task={task} region={true}/>;
        case TaskNames.projectAsAccommodation:
        case TaskNames.accommodation: // TODO: We're migrating to assigning client.residence
            // for DEV-76 - by doing this we're allowing project to be changed at this point too
            return <ReferralProjectDialog serviceRecipientId={serviceRecipientId} task={task}
                                          asSelectList={true}/>;
        case TaskNames.referralAccepted:
            return <AppropriateReferralDialog task={task}/>;
        case TaskNames.referralDetails:
            return <ReferralDetailsDialog taskHandle={task.taskHandle}/>;
        case TaskNames.source:
        case TaskNames.sourceWithIndividual:
            return <ReferralSourceDialog taskHandle={task.taskHandle}/>;
        case TaskNames.start:
        case TaskNames.startAccommodation: // NB deprecate this by being the same as 'start'
            return <StartOnServiceDialog taskName={task.taskName} taskHandle={task.taskHandle}/>;
        case TaskNames.waitingListCriteria:
            return <WaitingListCriteriaForm serviceRecipientId={serviceRecipientId}
                                          taskHandle={task.taskHandle}
                                          formRef={formRef}/>;
        case TaskNames.clientWithContact:
        case TaskNames.clientWithContact2:
            return <ClientDetailEditor serviceRecipientId={serviceRecipientId}
                                       taskName={task.taskName}
                                       taskHandle={task.taskHandle}
                                       showCode={false}
                                       formRef={formRef}/>;

        case TaskNames.agreementOfAppointments:
            return <AgreementsListEditor serviceRecipientId={serviceRecipientId}
                                         taskHandle={task.taskHandle}
            />;
        case TaskNames.scheduleReviews:
            return <ReviewScheduleDialog serviceRecipientId={serviceRecipientId}
                                         taskHandle={task.taskHandle}/>;
        case TaskNames.jobDetails:
            return <WorkerJobDetailForm serviceRecipientId={serviceRecipientId}
                                        task={task}
                                        onSaved={onSaved}/>;

        /*
        // TODO: move this to tab showing outstanding tasks and new button
        listWrapper.addEntry("new task", null, () => {
            AdHocTaskForm.showInModal(data.serviceRecipientId);
        });
         */
        case TaskNames.autoStart:
        //case TaskNames.pfpRoutines:// TODO convert to custom form evidence - see EditableModal.showInModal($element);
        default:
            // For now the rest of these should use existing code to pop up a modal - via onClick popping the modal
            return <b>TODO: lookupForm for {task.taskName}</b>;
    }
    return null;
}

function lookupAgreementTasks(context: ServiceRecipientContext, task: TaskWithTitle, onSaved: () => void, formRef = () => {}) {

    const serviceRecipientId = context.serviceRecipient.serviceRecipientId;
    const serviceAllocationId = context.serviceRecipient.serviceAllocationId;
    const sessionData = context.serviceRecipient.features;

    switch (task.taskName) {
        case TaskNames.consent:
            return getConsentForm(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.dataProtection:
            return getDataProtectionForm(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement:
            return getAgreement1Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement2:
            return getAgreement2Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement3:
            return getAgreement3Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement4:
            return getAgreement4Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement5:
            return getAgreement5Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement6:
            return getAgreement6Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement7:
            return getAgreement7Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement8:
            return getAgreement8Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement9:
            return getAgreement9Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        case TaskNames.agreement10:
            return getAgreement10Form(sessionData, serviceRecipientId, serviceAllocationId, task.taskHandle, false, formRef);
        default:
            console.error(task.taskName);
            return null;
    }
}

export function lookupEvidenceTasks(context: ServiceRecipientContext, task: TaskWithTitle, onSaved?: (() => void) | undefined) {

    const serviceRecipientId = context.serviceRecipient.serviceRecipientId;
    const sessionData = context.serviceRecipient.features;

    const taskDef = sessionData.getTaskDefinitionByName(task.taskName);

    if (sessionData.isTaskDefinitionEvidence(taskDef)) {

        if (taskDef.type == "EVIDENCE_CHECKLIST") {
            ChecklistEvidenceForm.showInModalByIds(serviceRecipientId, task.title, task.taskName); // TODO taskHandle

        } else if (taskDef.type == "EVIDENCE_CUSTOMFORM") {
            const config = context.serviceRecipient.configResolver.getServiceType();
            const withSignature = config.taskDefinitionSettingHasFlag(task.taskName,  "captureClientSignature", "y");
            const taskNameGroup = config.getTaskDefinitionSetting(task.taskName, "taskNameGroup");
            return withSignature
                    ? getCustomFormWithSignatureForm(sessionData, context.serviceRecipient, task.taskName, taskNameGroup, task.taskHandle, null, null)
                    : <CustomForm serviceRecipientId={serviceRecipientId}
                                  taskName={task.taskName}
                                  taskNameGroup={taskNameGroup}
                                  taskHandle={task.taskHandle}/>;

        } else {

            // trap a review to get the review-choice page first
            if (task.taskName == TaskNames.needsAssessmentReductionReview) {
                return withCommandForm(commandForm =>
                        possiblyModalForm(
                                "review",
                                true, true,
                                () => commandForm.cancelForm(),
                                () => {/* there is no save button here */
                                },
                                false,
                                true,
                                <ReviewChoiceDelegator serviceRecipientId={serviceRecipientId}
                                                       task={task}
                                                       onCompleted={() => commandForm.cancelForm()} // hacky way to call complete callbacks*/
                                />));
                // TODO: need cancel button and footer as per other controls
            }

            const title = context.serviceType.lookupTaskName(task.taskName);

            const isNewVisual = context.serviceType.taskDefinitionSettingHasFlag(task.taskName,  "showVisualStyleAs", "tabular-new");
            if (isNewVisual || taskDef.metadata?.questionsToActions?.length > 0) {

                // called via direct link nav/r/main/sr2/200075/tasks/compliance
                //  - SvcRecRouter has <Route path={`${basepath}tasks/:taskName`} component={pageComponentFactory("tasks")} />
                //  - where defaultPageComponentRegistry maps to TasksControlNG
                // "WORKS" snippet (dom)
                //  NB the tasks/ path shows SEVERAL times, as react re-draws
                // "withCommandForm" snippet (react)
                //  NB the tasks/ path shows ONCE, and the tasks show behind it

                // called via direct link /nav/r/welcome/myteam/:srId/tasks/:taskName
                //  - WelcomeAppBar picks up /welcome then <Route path={`${path}myteam`}><MyReferralsList/></Route>
                //  - where MyReferralsList 'match' calls TasksControlNG, calls CommandForm, calls formForTask from useServicesContext().taskIntegrations
                // "WORKS" snippet (dom)
                //  NB the welcome/ path shows SEVERAL times, as react re-draws
                // "withCommandForm" snippet (react)
                //  NB the tasks/ path shows ONCE

                // called via button from /nav/r/welcome/myteam/
                //  - where ReferralActionCards renders buttons that call lookupEvidenceTasks


                // works if non-visual, EvidenceDelegatingForm, but then shows several popups on the /tasks/ list
                // interestingly, the normal click shows a flash of some pages first which is probably the 'several forms'
                // see ReferralActionsCard lookupEvidenceTasks which comes here, and MyReferralsList 'match' on /nav/r/welcome/myteam/:srId/tasks/:taskName
                // NB it seems we need something that calls ReactDom.Render to show the form
                // NB going directly to a task - e.g. nav/r/main/sr2/200075/tasks/compliance means the cross doesn't go anywhere
                //    - ideally we'd have the tasks rendered behind, which we may be able to do by using a Route on the same path

                /*WORKED, but can't close
                showInCommandForm(possiblyModalForm(
                        "test",
                        true, true,
                        () => {},
                        () => {/!* there is no save button here *!/},
                        true,
                        true,
                        <EvidencePageLoaderForCommandForm srId={serviceRecipientId} taskName={task.taskName}>
                            <EvidencePage/>
                        </EvidencePageLoaderForCommandForm>
                ));*/
                /*WORKS
                showInCommandForm(<ModalForCommandForm
                        show={true}
                        title={title}
                        action="save"
                        maxWidth="md">
                            <EvidencePageLoaderForCommandForm srId={serviceRecipientId} taskName={task.taskName}>
                                <EvidencePage/>
                            </EvidencePageLoaderForCommandForm>
                    </ModalForCommandForm>);*/
                /*return withCommandForm(commandForm =>
                      possiblyModalForm(
                              "test",
                              true, true,
                              () => commandForm.cancelForm(),
                              () => {/!* there is no save button here *!/},
                              true,
                              true,
                              <div>bob</div>
                      )
                );*/
                /*return <Dialog open={true}>
                    <DialogTitle>Something went well</DialogTitle>
                    <DialogContent>
                        There was an error
                    </DialogContent>
                </Dialog>;*/
                /*return <EccoV3Modal title="sdf" action="update" saveEnabled={false} show={true}
                                    onCancel={() => {}}>
                        <div>bob</div>
                </EccoV3Modal>;*/

                return <ModalForCommandForm
                            show={true}
                            afterSave={() => onSaved && onSaved()}
                            title={title}
                            action="save"
                            maxWidth="md">
                        <EvidencePageLoaderForCommandForm srId={serviceRecipientId} taskName={task.taskName}>
                            <EvidencePage/>
                        </EvidencePageLoaderForCommandForm>
                    </ModalForCommandForm>
            }

            import("../../evidence/EvidenceDelegatingForm")
                    .then(({EvidenceDelegatingForm}) => {
                        EvidenceDelegatingForm.showInReactModalByIds(serviceRecipientId, title, task.taskName);
                    });
            return null;
        }
    }
    console.error(task.taskName);
    return null;
}

function formForTask(context: ServiceRecipientContext, task: TaskDto, onSaved: () => void) {
    const taskWithTitle = task as TaskWithTitle;
    taskWithTitle.title = context.serviceType.lookupTaskName(task.taskName);
    return handleLazy(lookupForm(context, task as TaskWithTitle, onSaved));
}

function handleNotCmdWrappedTaskClick(srId: number, task: TaskDto, context: ServiceRecipientContext): boolean {

    const sessionData = context.serviceRecipient.features;
    const taskDef = sessionData.getTaskDefinitionByName(task.taskName);
    const isNewVisual = context.serviceType.taskDefinitionSettingHasFlag(task.taskName,  "showVisualStyleAs", "tabular-new");
    const evidencePageButNotCustomFormNorNewForm = sessionData.isTaskDefinitionEvidence(taskDef)
            && taskDef.name != TaskNames.needsAssessmentReductionReview
            && sessionData.getTaskDefinitionByName(task.taskName).type != "EVIDENCE_CUSTOMFORM"
            && !isNewVisual
            && !(taskDef.metadata?.questionsToActions?.length > 0)
    const notCmdWrapped = [TaskNames.allocateToServices, TaskNames.initialSPData, TaskNames.exitSPData].indexOf(task.taskName) > -1;
    if (evidencePageButNotCustomFormNorNewForm || notCmdWrapped) {
        const onComplete = () => {}; // we don't need to do the old reloadUrl, since we currently clear the cache on TaskList.render
        formForTask(context, task, onComplete);
        return true;
    }

    return false;
}

const registerIntegrationHooks = () => {
    useHactIntegration(); // also registered in StatusPanel
    useHistoryLinkEventIntegration();
}

export default function TasksControl(props: TasksControlProps) {
    // TasksControl defers to TasksControlNG
    //      TasksControl has the lookups
    //      TasksControlNG shows either the list or control
    return <TasksControlNG {...props}/>
}

const handleTaskClick = (srId: number,
                         task: TaskDto,
                         context: ServiceRecipientWithEntitiesContext,
                         setActiveTask: (task: TaskDto | null) => void) => {

    // Its not entirely clear what needs to set the activeTask and what doesn't, so we mimic what pre-existed
    // Perhaps its something to do with refreshing/reloading the task list?
    if (handleNotCmdWrappedTaskClick(srId, task, context)) {
        return;
    } else {
        // Otherwise do it the React way using lookupForm
        setActiveTask(task)
    }
};

/** Task integrations we can pass in via EccoAPI */
export const taskIntegrations: TaskIntegrations = {
    formForTask,
    handleTaskClick,
    registerIntegrationHooks
}
