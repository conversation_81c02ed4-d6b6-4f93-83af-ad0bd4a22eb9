import $ = require("jquery");
import CheckboxGroupInput = require("../controls/CheckboxGroupInput");
import CheckboxInput = require("../controls/CheckboxInput");
import DateInput = require("../controls/DateInput");
import DateTimeInput = require("../controls/DateTimeInput");
import ElementContainer = require("../controls/ElementContainer");
import InputGroup = require("../controls/InputGroup");
import NumberInput = require("../controls/NumberInput");
import SelectList = require("../controls/SelectList");
import TextAreaInput = require("../controls/TextAreaInput");
import TimeInput = require("../controls/TimeInput");
import services = require("ecco-offline-data");
import {ActionsChangedCallback, ActionState, EccoDate, EccoDateTime, EccoTime} from "@eccosolutions/ecco-common";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {applicationRootPath} from "application-properties";
import {CommandQueue, CommandSource, CommentCommand, RiskCommentCommand, SupportCommentCommand} from "ecco-commands";
import * as configDomain from "ecco-dto";
import {
    Client,
    EvidenceGroup,
    EvidencePageType,
    isOffline,
    ListDefinitionEntry, ReferralSummaryWithEntities,
    ServiceRecipientWithEntities,
    TaskNames
} from "ecco-dto";
import * as dto from "ecco-dto/evidence-dto";
import {WorkEvidenceCommandDto} from "ecco-dto/evidence-dto";
import {EvidenceDef} from "ecco-evidence";

import {AttachmentsControl} from "../attachments/attachments";
import {ValidationCheck, ValidationChecksBuilder, ValidationErrors} from "../common/validation";
import {removeDraftsFromPage} from "../controls/autosave";
import DialogContent from "../controls/DialogContent";
import {RiskStatusAreaControl} from "./risk/RiskStatusAreaControl";
import {flagStyledAsGraphical} from "ecco-components";
import {reactElementContainer} from "ecco-components-core";


function parseIntOrNull(numberStr: string): number {
    return numberStr ? parseInt(numberStr) : null;
}

let instanceCount = 1; // So we can discriminate between multiple forms in same DOM (e.g. tabs)

class InputGroupValid extends InputGroup {
    constructor(private overrideCheck: () => boolean | null,
                label: string|$.JQuery|null,
                inputControl: any,
                id?: string,
                inline: boolean = false) {
        super(label, inputControl, id, inline);
    }
    public override validateOverride(): boolean | null {
        return this.overrideCheck();
    }
}

class EvidenceCommentForm extends ElementContainer implements CommandSource, DialogContent {

    private formId = instanceCount++;

    // actions and onFinished are only used when showing this alone in a modal
    private onFinished: () => void;

    protected actions: ActionState[] = [this.getSaveAction()];

    /** Callback whenever available actions change state */
    actionChangeCallback: (actions: ActionState[]) => void;

    /** Calendar event Id if provided */
    private eventId: string;

    private instructions: string;
    private commentGroup: InputGroup;
    private comment: TextAreaInput;
    private startGroup: InputGroup;
    private startDate: DateInput;
    private startDateTime: DateTimeInput;
    private endTime: TimeInput;
    private minsSpentGroup: InputGroup;
    private minsSpent = new NumberInput("minutes-spent");
    private mileageToGroup: InputGroup;
    private mileageTo = new NumberInput("mileage-to");
    private mileageDuringGroup: InputGroup;
    private mileageDuring = new NumberInput("mileage-during");
    private minsTravelGroup: InputGroup;
    private minsTravel = new NumberInput("travel-mins");
    private typeListGroup: InputGroup;
    private typeList = new SelectList("comment-type-selection");
    private clientStatusListGroup: InputGroup;
    private clientStatusList = new SelectList("client-status-selection").withEmptyEntryValue("");
    private meetingStatusListGroup: InputGroup;
    private meetingStatusList = new SelectList("meeting-status-selection").withEmptyEntryValue("");
    private locationListGroup: InputGroup;
    private locationList = new SelectList("location-selection").withEmptyEntryValue("");
    private flagCheckboxes: CheckboxGroupInput;
    private riskManagementCheckbox: CheckboxInput;
    private risksOutstandingCheckboxes: CheckboxInput[];
    private risksOutstandingDiv = $("<div>").addClass("col-xs-6");

    private commentLabel: string;
    private typeLabel: string;
    private showComment = true;
    private showMileage = true;
    private showStartAsDateTime = true;
    private showEndTime: boolean;
    private showMinsTravel = false;
    private latestFlagEvidenceByFlagId: {[id: number]: dto.FlagEvidenceDto} = {};
    private latestFlagIds: number[] = [];
    private showAttachments = false;

    private location: GeolocationPosition | GeolocationPositionError;

    private errors = new ValidationErrors("");

    private taskName: string;

    constructor(private serviceRecipient: ServiceRecipientWithEntities,
            private evidenceDef: EvidenceDef, private submit: () => Promise<void>,
            private getWorkUuid: () => Uuid,
            private work?: dto.BaseOutcomeBasedWork, private reviewId?: number,
            private previousCommandQueue?: CommandQueue,
            eventIdIn?: string,
            private previousCommandOptions: "temporalOnly" | "all" = "temporalOnly") {
        super();

        this.location = serviceRecipient.features.getGpsIfEnabled();

        this.taskName = evidenceDef.getTaskName();

        // if not provided, see if we can determine (legacy approach)
        if (!eventIdIn) {
            const params = new URLSearchParams(window.location.search);
            if (params.get('eventId')) {
                this.eventId = params.get('eventId');
            }
        } else {
            this.eventId = eventIdIn;
        }

        const serviceType: configDomain.ServiceType = serviceRecipient.configResolver.getServiceType();

        this.showComment
            = !serviceType.taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "!comment");

        this.commentLabel
            = serviceType.getTaskDefinitionSetting(this.taskName,  "commentLabel") || "comment";
        this.typeLabel
            = serviceType.getTaskDefinitionSetting(this.taskName,  "typeLabel") || "type";

        this.showMileage
            = serviceType.taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "mileage");
        this.showMinsTravel
            = serviceType.taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "travelMinutes");

        this.showEndTime
            = serviceType.taskDefinitionSettingHasFlag(this.taskName, "tookPlaceOn", "startEnd");

        this.showStartAsDateTime
            = serviceType.taskDefinitionSettingHasFlag(this.taskName, "tookPlaceOn", "dateTime") ||
              this.showEndTime;

        this.showAttachments
            = serviceType.taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "attachments")
            || this.taskName == TaskNames.needsAttachment;

        if (serviceRecipient.features) {
            this.instructions = serviceRecipient.features.getSetting("com.ecco.evidence:comment.instructions");
            this.showAttachments = this.showAttachments ||
                    serviceRecipient.features.isEnabled("demo.showCommentComponents.attachments");
        }

        // A bit of a hack for getting the id
        this.typeList.populateFromList(serviceType.getCommentTypesById(serviceRecipient.features, this.taskName),
            (ct) => ({ key: ct.getId().toString(), value: ct.getDisplayName(), isHidden: ct.getDisabled(), isDefault: false, readOnly: false }),
            (ct) => this.work && ct.getId() == this.work.commentTypeId);

        this.populateClientStatus();
        this.populateMeetingStatus();
        this.populateLocation();

        if (this.showComment) {
            if (serviceRecipient.features && serviceRecipient.features.isEnabled("support.evidence.tapToEditTextAreas")) {
                this.comment = new TextAreaInput(this.commentLabel, undefined, true).withAutoSave();
            } else {
                this.comment = new TextAreaInput(this.commentLabel).withAutoSave();
            }
        }
        this.layoutForm();
        if (this.work) {
            this.populateFromWork();
            // 'else' because it doesn't make sense to be editing and have previousCommands
        } else if (this.previousCommandQueue) {
            this.previousCommandQueue.getCommands().then(commands => {
                if (commands && commands.length > 0) {
                    let commentCmd = commands.filter(cmd => cmd instanceof CommentCommand).pop();
                    this.populateFromPreviousEntry(<WorkEvidenceCommandDto>commentCmd.toCommandDto());
                }
            });
        }
    }

    private populateFromPreviousEntry(commentDto: WorkEvidenceCommandDto) {
        // so far this method is called in the case where 'stayOnSave' is on
        // and is designed to populate fields based on the previous data
        // specifically - the work end time to the work start time
        if (this.showStartAsDateTime && commentDto.minsSpent) {
            let workDateTime = EccoDateTime.parseIso8601(commentDto.workDate.to);
            let newStartTime = workDateTime.addMinutes(commentDto.minsSpent.to);
            this.setWorkDate(newStartTime);
        } else {
            this.setWorkDate(EccoDateTime.parseIso8601(commentDto.workDate.to));
        }

        if (this.showComment && this.previousCommandOptions != "temporalOnly") {
            this.comment.setVal(commentDto.comment.to);
        }
    }

    private populateFromWork() {
        // NOTE: commentType is done as part of typeList.popluateFromList
        this.showComment && this.comment.setVal(this.work.comment);
        this.setWorkDate(EccoDateTime.parseIso8601(this.work.workDate));
        this.work.minsSpent && this.minsSpent.setVal(this.work.minsSpent.toString());
        this.work.mileageTo && this.mileageTo.setVal(this.work.mileageTo.toString());
        this.work.mileageDuring && this.mileageDuring.setVal(this.work.mileageDuring.toString());
        this.work.minsTravel && this.minsTravel.setVal(this.work.minsTravel.toString());
    }

    private setWorkDate(workDate: EccoDateTime) {
        if (this.showStartAsDateTime) {
            this.startDateTime.setDate(workDate);
        }
        else {
            this.startDate.setDate(workDate.toEccoDate());
        }
    }

    //noinspection JSUnusedLocalSymbols
    public submitForm(next: boolean = false): Promise<any> {
        if (this.errors.isValid()) {
            return this.submit();
        }
        else {
            alert(this.errors.getGlobalMessage());
            return Promise.reject( new Error("Validation error") );
        }
    }

    public emitChangesTo(queue: CommandQueue) {

        let modifying = this.work != null;
        const cmd = this.evidenceDef.getEvidenceGroup() == EvidenceGroup.threat
                ? RiskCommentCommand.create(modifying, this.getWorkUuid(), this.serviceRecipient.serviceRecipientId, this.evidenceDef.getEvidenceGroup(), this.taskName)
                : SupportCommentCommand.create(modifying, this.getWorkUuid(), this.serviceRecipient.serviceRecipientId, this.evidenceDef.getEvidenceGroup(), this.taskName);
            const initialTypeId = parseIntOrNull(this.typeList.getInitiallySelectedKey());
            cmd.withReviewId(this.reviewId)
                .withLocation(this.location);
            if (this.showComment) {
                cmd.changeComment(this.work && this.work.comment, this.getComment())
            }
            cmd.changeWorkDate(this.work && this.work.workDate, this.getStartDate())
                .changeCommentTypeId(initialTypeId, this.getCommentTypeId())
                .changeClientStatusId(parseIntOrNull(this.clientStatusList.getInitiallySelectedKey()), this.getClientStatusId())
                .changeMeetingStatusId(parseIntOrNull(this.meetingStatusList.getInitiallySelectedKey()), this.getMeetingStatusId())
                .changeLocationId(parseIntOrNull(this.locationList.getInitiallySelectedKey()), this.getLocationId())
                .changeMinsSpent(this.work && this.work.minsSpent, this.getMinsSpent())
                .changeMinsTravel(this.work && this.work.minsTravel, this.getMinsTravel())
                .changeMileageTo(this.work && this.work.mileageTo, this.getMileageTo())
                .changeMileageDuring(this.work && this.work.mileageDuring, this.getMileageDuring())
                .withAttachments(this.getAttachmentIds());

            if (this.flagCheckboxes) {
                const flagsOnNow = this.flagCheckboxes.checked().map(f => Number(f));
                cmd.changeFlags(this.latestFlagIds, flagsOnNow);
            }

            if (this.riskManagementCheckbox) {
                cmd.changeRiskManagementRequired(null, this.riskManagementCheckbox.isChecked());
            }

            if (this.risksOutstandingCheckboxes) {
                const uuidsHandled = this.risksOutstandingCheckboxes
                        .filter((cb) => cb.isChecked())
                        .map((cb) => cb.getValue());
                cmd.changeRiskManagementHandled(null, uuidsHandled);
            }

            if (this.eventId) {
                cmd.changeEventId(this.work && this.work.eventId, this.eventId);
            }

        queue.addCommand(cmd.build());
    }

    public cleanCommentsAndTime() {
        this.showComment && this.comment.setVal("");
        this.minsSpent.setVal("");
        removeDraftsFromPage();
    }

    /* datetimepicker etc needed to replicate genericType.jsp and commentBox.jsp
     * case settings.tookPlaceOn.dateTime: show workDateTime field labelled "start time"
     * TODO case settings.tookPlaceOn.startEnd: show workDateTime and tookPlaceEnd (see TookPlaceBetweenTimeControl.ts)
     *    and place difference into a hidden minsSpent field
     * default: show workDate field (no time) as "took place on" and submit time as midnight at the beginning of that day
     */
    private layoutForm() {
        const serviceType: configDomain.ServiceType = this.serviceRecipient.configResolver.getServiceType();

        const wide = serviceType.taskDefinitionSettingHasFlag(this.evidenceDef.getTaskName(), "commentWidth", "wide")
            || this.evidenceDef.getEvidencePageType() == EvidencePageType.commentsOnly;

        let inputClasses = wide ? "col-xs-12 col-sm-6" : "col-xs-12 col-sm-6 col-lg-12";

        this.addClass("comment-form col-xs-12 pull-right" + (wide ? "" : " col-lg-4"));
        const row = new ElementContainer().addClass("row");

        const srId = this.serviceRecipient.serviceRecipientId;
        if (this.eventId) {
            let $elContact = $("<div>").addClass("panel-body").appendTo(
                $("<div>").addClass("panel panel-default").appendTo(
                    $("<div>").addClass("col-xs-12").appendTo(row.element())
                ));
            let $elEvent = $("<div>").addClass("alert alert-warning").appendTo(
                $("<div>").addClass("col-xs-12").appendTo(row.element())
            );
            const eventQ = isOffline()
                // not quite sure how this retrieves one event? see 1d593545
                ? services.getCalendarRepository().nearby(this.serviceRecipient.features.getDto().calendarId)
                : services.getCalendarRepository().fetchEventsById([].concat(this.eventId));
            const referralClientPairQ: Promise<{referral: ReferralSummaryWithEntities, client: Client}> = ((this.serviceRecipient.prefix == 'r') && !isOffline())
                ? services.getReferralRepository().findOneReferralSummaryWithEntitiesUsingDto(srId).then(r =>
                    services.getClientRepository().findOneClient(r.clientId).then(c => ({referral: r, client: c}))
                )
                : Promise.resolve(null);
            Promise.all([eventQ, referralClientPairQ])
                .then( ([events, referralClientPair] ) => {
                    let event = events.filter(event => event.uid == this.eventId)[0];
                    let start = EccoDateTime.parseIso8601(<string>event.start);
                    let end = EccoDateTime.parseIso8601(<string>event.end);
                    let duration = end ? end.subtractDateTime(start) : null;
                    let title = event.eventCategoryId
                        ? this.serviceRecipient.features.getListDefinitionEntryById(event.eventCategoryId).getDisplayName()
                        : event.title;
                    let durationTxt = duration ? "<br>duration: " + duration.inMinutes().toFixed() + " mins" : "";

                    if (referralClientPair) {
                        const sessionData = referralClientPair.referral.features;
                        // see AssociatedContactCard for contact info, and EventCard for appointmentLocation
                        $elContact.append($("<div>").css("font-weight", "bold").text(referralClientPair.referral.displayName))
                            .append($("<div>").text("r-id: " + referralClientPair.referral.referralId))
                            .append($("<div>").text("c-id: " + referralClientPair.referral.clientCode))
                            .append($("<div>").text("service: " + sessionData.getServiceCategorisationName(referralClientPair.referral.serviceAllocationId)))
                            .append($("<div>").text("event address: " + event.location));
                        if (referralClientPair.client.mobileNumber) {
                            $elContact.append($("<div>").text("mobile: " + referralClientPair.client.mobileNumber));
                        }
                        if (referralClientPair.client.phoneNumber) {
                            $elContact.append($("<div>").text("phone number: " + referralClientPair.client.phoneNumber));
                        }
                        if (referralClientPair.client.genderId) {
                            $elContact.append($("<div>").text("gender: " + this.serviceRecipient.features.getListDefinitionEntryById(referralClientPair.client.genderId).getDisplayName()));
                        }
                        if (referralClientPair.client.birthDate) {
                            // see dateofbirth.tag
                            const birthDate = EccoDate.parseIso8601(referralClientPair.client.birthDate);
                            let diff = EccoDate.todayUtc().toUtcJsDate().getTime() - birthDate.toUtcJsDate().getTime();
                            diff = diff / (1000 * 3600 * 24 * 365.25); // stand a chance of dealing safely with leap-day births
                            diff = Math.floor(diff);
                            $elContact.append($("<div>").text("date of birth: " + birthDate.formatPretty() + " (" + diff+" yrs)"));
                        }
                        const riskStatus = new RiskStatusAreaControl(referralClientPair.referral.serviceRecipientId);
                        $elContact.append(riskStatus.element());
                        riskStatus.load();
                    }



                    $elEvent.html("this work relates to the following appointment: <br>" + title
                        + "<br>start: " + start.formatShort() + durationTxt);
                    // TODO: set took place to correct start date / date-time
            });
        }

        if (this.instructions) {
            row.append( $("<div>").addClass("col-xs-12")
                .append( $("<label>")
                    .append( $("<strong>")
                        .text(this.instructions) ) ) );
        }

        if (this.showComment) {
            this.commentGroup = new InputGroup(this.commentLabel, this.comment)
                .addClass("col-xs-12");
            if (!this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "allowNullComment")) {
                this.commentGroup
                    .withValidationChecks(ValidationChecksBuilder.VALID_TEXT, this.errors)
                    .enableValidation();
            }
            row.append(this.commentGroup);
            this.handlePrintable("comment", this.commentGroup.element());
        }

        if (this.serviceRecipient.configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "showRiskManagementRequired")) {

            this.riskManagementCheckbox = new CheckboxInput("risk management required");
            this.riskManagementCheckbox.element().addClass(inputClasses);
            this.handlePrintable("showRiskManagementRequired", this.riskManagementCheckbox.element());
            row.append(this.riskManagementCheckbox);
        }

        const defaultDateToToday = this.serviceRecipient.configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(this.taskName,  "defaultWorkDate", "today");

        const workDateChecks = new ValidationChecksBuilder()
            .addCheck(ValidationCheck.Required);
// TODO: turn this into addWarningCheck (so it turns up as a warning not error)
        if (!this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "allowOldWorkDate", "y")) {
            //workDateChecks
                //.addWarningCheck(ValidationCheck.Recent);

        }

        const canCreateFutureWork = this.serviceRecipient.features.hasRoleReferralAdmin()
                && this.serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.taskName,  "allowFutureWorkDate", "y");
        if (canCreateFutureWork) {
            workDateChecks.addCheck(ValidationCheck.NearFuture); // Allows a future appt to be recorded as cancelled
        } else {
            workDateChecks.addCheck(ValidationCheck.NotFuture);
        }

        if (this.showStartAsDateTime) {
            const defaultDateTime = defaultDateToToday ? EccoDateTime.nowLocalTime()
                                    : null;
            this.startDateTime = new DateTimeInput(defaultDateTime, "start" + this.formId, true); // TODO: Ensure interval is 1 min when we migrate
            this.startGroup = new InputGroup("start time", this.startDateTime)
                .addClass(inputClasses)
                .withValidationChecks(workDateChecks, this.errors)
                .enableValidation();
        }
        else {
            const defaultDate = defaultDateToToday ? EccoDate.todayLocalTime()
                                    : null;
            this.startDate = new DateInput(defaultDate, "workDate" + (this.formId == 1 ? "" : this.formId), true); // TODO: revisit id when have removed jquery-datepicker

            const startLabel = $("<span>")
                .attr("error-text", "took place on")
                .append("took place on &nbsp;&nbsp;")
                .append( $("<a>")
                    .text("(today)")
                    .click( () => this.startDate.setDate(EccoDate.todayLocalTime())));
            this.startGroup = new InputGroup(startLabel, this.startDate)
                .addClass(inputClasses)
                .withValidationChecks(workDateChecks, this.errors)
                .enableValidation();
        }
        row.append(this.startGroup);

        if (this.showEndTime) {
            this.endTime = new TimeInput(null, "tookPlaceEnd", false, true);
            let $durationText = $("<span>").attr("id", "durationText");
            let endGroup = new InputGroup("end time", this.endTime) //$("<span>").append(this.endTime.element(), $durationText))
                .addClass(inputClasses);
            // use the same validation - because endTime populates minsSpent
            if (serviceType.taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "minsSpent")) {
                endGroup
                    .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                    .enableValidation();
            }
            row.append(endGroup.append($durationText));

            //noinspection ObjectAllocationIgnored
            new TookPlaceBetweenTimeControl(this.startDateTime, this.endTime, this.minsSpent.element(), $durationText);
        } else {

            if (this.serviceRecipient.features.isEnabled("support.showCommentComponents.minutesSpent") ||
                this.serviceRecipient.configResolver.getServiceType()
                    .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "minutesSpent")) {
                this.minsSpentGroup = new InputGroup("mins spent", this.minsSpent)
                    .addClass(inputClasses);
                this.handlePrintable("minutesSpent", this.minsSpentGroup.element());

                if (serviceType.taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "minsSpent")) {
                    this.minsSpentGroup
                        .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                        .enableValidation();
                }
                row.append(this.minsSpentGroup);
            }
        }

        if (this.showMileage) {
            this.mileageToGroup = new InputGroup("mileage to/from", this.mileageTo)
                .addClass(inputClasses);
            row.append(this.mileageToGroup);

            this.mileageDuringGroup = new InputGroup("mileage with client", this.mileageDuring)
                .addClass(inputClasses);
            row.append(this.mileageDuringGroup);

            this.handlePrintable("mileage", this.mileageToGroup.element());
            this.handlePrintable("mileage", this.mileageDuringGroup.element());
        }

        if (this.showMinsTravel) {
            this.minsTravelGroup = new InputGroup("travel time to/from (mins)", this.minsTravel)
                .addClass(inputClasses);
            row.append(this.minsTravelGroup);
            this.handlePrintable("travelMinutes", this.minsTravelGroup.element());
        }

        if (this.serviceRecipient.features.isEnabled("support.showCommentComponents.type") ||
            this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "type")) {
            //const messages = this.serviceRecipient.features.getMessages();
            //const typeLabel = messages["form.evidenceComment.type"]
            this.typeListGroup = new InputGroup(this.typeLabel, this.typeList)
                .addClass(inputClasses);
            this.handlePrintable("type", this.typeListGroup.element());

            if (serviceType.taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "type")) {
                this.typeListGroup
                    .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                    .enableValidation();
            }
            row.append(this.typeListGroup);
        }

        const meetingStatusShow = this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "meetingStatus");
        if (meetingStatusShow) {
            const messages = this.serviceRecipient.features.getMessages();
            const meetingStatusLabel = messages["form.evidenceComment.meetingStatus"]
            this.meetingStatusListGroup = new InputGroup(meetingStatusLabel, this.meetingStatusList)
                .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                .addClass(inputClasses);
            this.handlePrintable("meetingStatus", this.meetingStatusListGroup.element());
            if (this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "meetingStatus")) {
                this.meetingStatusListGroup.enableValidation();
            }
            row.append(this.meetingStatusListGroup);
        }

        if (this.serviceRecipient.configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "clientStatus")) {

            const clientRequired = this.serviceRecipient.configResolver.getServiceType()
                    .taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "clientStatus");
            const clientRequiredIfMeeting = meetingStatusShow && this.serviceRecipient.configResolver.getServiceType()
                    .taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "clientStatusIfMeetingStatus");

            const validOverride = () => {
                if (clientRequiredIfMeeting) {
                    // if meeting not selected, then clientstatus is valid
                    if (this.meetingStatusList.selected(true) == null) {
                        return true;
                    }
                }
                return null;
            }
            const messages = this.serviceRecipient.features.getMessages();
            const clientStatusLabel = messages["form.evidenceComment.clientStatus"]
            this.clientStatusListGroup = new InputGroupValid(validOverride, clientStatusLabel, this.clientStatusList)
                .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                // setup but do not apply enableValidation until later
                .addClass(inputClasses);
            this.handlePrintable("clientStatus", this.clientStatusListGroup.element());
            if (clientRequired || clientRequiredIfMeeting) {
                this.clientStatusListGroup.enableValidation();
            }
            row.append(this.clientStatusListGroup);

            if (meetingStatusShow && clientRequiredIfMeeting) {
                this.meetingStatusListGroup.withCallback((valid) => {
                    this.clientStatusListGroup.triggerValidation();
                })
            }
        }

        if (this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName, "showCommentComponents", "location")) {
            const messages = this.serviceRecipient.features.getMessages();
            this.locationListGroup = new InputGroup(messages["form.evidenceComment.location"], this.locationList)
                .withValidationChecks(ValidationChecksBuilder.REQUIRED, this.errors)
                // setup but do not apply enableValidation until later
                .addClass(inputClasses);
            this.handlePrintable("location", this.locationListGroup.element());
            if (this.serviceRecipient.configResolver.getServiceType()
                    .taskDefinitionSettingHasFlag(this.taskName,  "validateComment", "location")) {
                this.locationListGroup.enableValidation();
            }
            row.append(this.locationListGroup);
        }

        if (this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName, "showFlags", "y")) {

            this.flagCheckboxes = new CheckboxGroupInput();
            this.flagCheckboxes.element().addClass(inputClasses);
            row.append(this.flagCheckboxes);
        }

        if (this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "showRiskManagementDealsWith")) {
            this.risksOutstandingCheckboxes = [];
            row.append(this.risksOutstandingDiv);
        }

        if (!isOffline() && this.showAttachments) {
            const readOnly = false; // TODO

            // /api/service-recipients/${holder.serviceRecipientId}/evidence/${holder.evidenceGroupKey}/attachments/unused/
            const unattachedHref = `${applicationRootPath}api/service-recipients/${srId}/evidence/${this.evidenceDef.getEvidenceGroup().name}/attachments/unused/`;

            //  /api/service-recipients/${serviceRecipientId}/evidence/${workId}/attachments/
            const workId: string = null;

            const attachedHref = !workId ?  ""  // only for if editing work
                : `${applicationRootPath}api/service-recipients/${srId}/evidence/${workId}/attachments/`;

            // upload from the comment box using all params
            // /api/secure/uploadHiddenJs.html?evidencePage=19&amp;evidencePageGroup=19&amp;source=referral&amp;parentId=142
            const uploadHref = new URL(`${applicationRootPath}api/secure/uploadHiddenJs.html`, location.href);
            uploadHref.searchParams.set("evidencePage", this.evidenceDef.getTaskName());
            uploadHref.searchParams.set("evidenceGroupName", this.evidenceDef.getEvidenceGroup().name);
            uploadHref.searchParams.set("source", "service-recipient"); // TODO: detect this
            uploadHref.searchParams.set("serviceRecipientId", srId.toString());

            try {
                const attId = `attachmentFileId${this.formId}`;
                const attachments = new AttachmentsControl(uploadHref.href, unattachedHref, attachedHref, "#"+attId, 10240000, readOnly);
                this.handlePrintable("attachments", attachments.element());

                const col = $("<div>").addClass(inputClasses + " form-group")
                    .append('<label>attach files (max 10Mb each)</label>')
                    .append(`<input type="hidden" name="attachments[0].fileId" id="${attId}">`)
                    .append(attachments.element());
                row.append(col);
            } catch(e) {
                // HACK for attachments = undefined on iPad
                console.log(e);
            }
        }

        this.append(row);

        // want hr always if we're wide, but don't want it at all if no smart step control
        if (this.evidenceDef.getEvidencePageType() != EvidencePageType.commentsOnly) {
            this.append($("<hr>").addClass(!wide && "hidden-lg"));
        }
    }

    public populateFlags(flagEvidenceByFlagId: {[id: number]: dto.FlagEvidenceDto}) {
        this.latestFlagEvidenceByFlagId = flagEvidenceByFlagId;

        if (this.flagCheckboxes) {
            // NB disabled are filtered out
            let flags = this.serviceRecipient.configResolver.getServiceType().getFlagsByListNameOrId(this.serviceRecipient.features, this.taskName)

            // populate the latest flags...
            // seems a bit odd to do it here, but the whole populateFlags callback needs sorting
            // and we only want to turn off flags that are available to select (else we look like we're turning them off)
            flags.forEach(flag => {
                const flagEvidence = flagEvidenceByFlagId[flag.getId()]
                if (flagEvidence && flagEvidence.value) {
                    this.latestFlagIds.push(flag.getId());
                }
            })

            const flagEditPermission = this.serviceRecipient.features.hasRoleEditEvidenceFlags()
            this.flagCheckboxes.populateFromList(flags, (flag: ListDefinitionEntry) => {
                const flagEvidence = flagEvidenceByFlagId[flag.getId()];

                let flagMount: $.JQuery | undefined = undefined;
                const el = flagStyledAsGraphical(this.serviceRecipient.features, {flagId: flag.getId()}, false);
                if (el) {
                    flagMount = $("<span>");
                    reactElementContainer(el, flagMount[0]);
                }
                return {
                    id: null,
                    label: flag.getName(),
                    value: flag.getId().toString(),
                    selected: flagEvidence && flagEvidence.value,
                    readOnly: !flagEditPermission,
                    icon: flagMount
                }
            });
        }
    }

    public populateRiskOutstanding(risksOutstanding: dto.SupportWork[]) {
        if (this.risksOutstandingCheckboxes && risksOutstanding) {
            risksOutstanding.forEach((supportWork) => {
                const handleRiskCheckbox = new CheckboxInput("handle", undefined, supportWork.id);
                this.risksOutstandingCheckboxes.push(handleRiskCheckbox);
                this.risksOutstandingDiv.append(handleRiskCheckbox.element());//.addClass("col-xs-3"));
                this.risksOutstandingDiv.append($("<textarea>").text(supportWork.comment)); //.prop("disabled", true).addClass("col-xs-3").text(supportWork.comment));
            });
        }
    }

    public populateClientStatus() {
        if (this.serviceRecipient.configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "clientStatus")) {
            const clientStatusListName = this.serviceRecipient.configResolver.getServiceType()
                .getTaskDefinitionSetting(this.taskName,  "clientStatusListName");
            this.clientStatusList.populateFromList(this.serviceRecipient.features.getListDefinitionEntriesByListName(clientStatusListName),
                        (listDef) => ({ key: listDef.getId().toString(), value: listDef.getName(), isHidden: listDef.getDisabled(), isDefault: listDef.getDefault() }),
                        (listDef) => this.work && listDef.getId() == this.work.clientStatusId);
        }
    }

    public populateMeetingStatus() {
        if (this.serviceRecipient.configResolver.getServiceType()
            .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "meetingStatus")) {
            const meetingStatusListName = this.serviceRecipient.configResolver.getServiceType()
                .getTaskDefinitionSetting(this.taskName,  "meetingStatusListName");
            this.meetingStatusList.populateFromList(this.serviceRecipient.features.getListDefinitionEntriesByListName(meetingStatusListName),
                    (listDef) => ({ key: listDef.getId().toString(), value: listDef.getName(), isHidden: listDef.getDisabled(), isDefault: listDef.getDefault() }),
                    (listDef) => this.work && listDef.getId() == this.work.meetingStatusId);
        }
    }

    public populateLocation() {
        if (this.serviceRecipient.configResolver.getServiceType()
                .taskDefinitionSettingHasFlag(this.taskName,  "showCommentComponents", "location")) {
            const locationListName = this.serviceRecipient.configResolver.getServiceType()
                .getTaskDefinitionSetting(this.taskName,  "locationListName");
            this.locationList.populateFromList(this.serviceRecipient.features.getListDefinitionEntriesByListName(locationListName),
                    (listDef) => ({ key: listDef.getId().toString(), value: listDef.getName(), isHidden: listDef.getDisabled(), isDefault: listDef.getDefault() }),
                    (listDef) => this.work && listDef.getId() == this.work.locationId);

        }
    }

    /** True if required fields are set */
    public isValid(): boolean {
        return this.errors.isValid();
    }

    public getStartDate() {
        let date :string = null;
        if (this.showStartAsDateTime) {
            if (!this.startDateTime.getDate()) {
                date = EccoDateTime.nowLocalTime().formatIso8601();
            }
            else {
                date = this.startDateTime.getDate().formatIso8601();
            }
        }
        else {
            if (!this.startDate.getDate()) {
                date = EccoDateTime.nowLocalTime().formatIso8601();
            }
            else {
                date = this.startDate.getDate().toDateTimeMidnight().formatIso8601();
            }
        }
        return date;
    }

    public getAttachmentIds() {
        const elements: Element[] = $("input[name*=fileId]").toArray();
        return elements
            .filter( el => el != null && el.getAttribute("value") != null)
            .map( el => parseInt(el.getAttribute("value")) );
    }

    public getComment() {
        return this.comment.val();
    }

    public getCommentTypeId() {
        return this.typeList.selectedValAsNumber();
    }

    public getClientStatusId() {
        return parseIntOrNull(this.clientStatusList.val());
    }

    public getMeetingStatusId() {
        return parseIntOrNull(this.meetingStatusList.val());
    }

    public getLocationId() {
        return parseIntOrNull(this.locationList.val());
    }

    public getMinsSpent() {
        return parseIntOrNull(this.minsSpent.val());
    }

    public getMinsTravel() {
        return parseIntOrNull(this.minsTravel.val());
    }

    public getMileageTo() {
        return this.mileageTo.val() ? parseFloat(this.mileageTo.val()) : null;
    }

    public getMileageDuring() {
        return this.mileageDuring.val() ? parseFloat(this.mileageDuring.val()) : null;
    }

    // DialogContent
    public getTitle() {
        return "edit work details";
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback): void {
        this.actionChangeCallback = updateActions;
        this.actionChangeCallback(this.actions);
    }

    /** This must be set before modal */
    public setActions(actions: ActionState[]) {
        this.actions = actions;
    }

    public getSaveAction(save = "save", saving = "saving..."): ActionState {
        return {
            label: save,
            clickedLabel: saving,
            onClick: () => {
                return this.submitForm()
                    .catch( (e: Error) => {
                        alert("failed to save changes. " + e.toString());
                        throw e;
                    })

                    .then( () => removeDraftsFromPage())
                    .then( () => this.onFinished() )

            },
            disabled: true
        };
    }

    public getFooter() {
        return null;
    }

    public setOnFinished( onFinished: () => void) {
        this.onFinished = onFinished;
    }
    public callOnFinished() {
        this.onFinished();
    }

    private handlePrintable(property: string, $element: $.JQuery) {
        let propertyIsPrintable = this.serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.taskName,  "printCommentComponentsForClient", property);
        if (!propertyIsPrintable) {
            $element.addClass('no-print');
        }
    }
}

/**
 * Used on evidence pages where start dateTime and and end time is required.
 * This calculates the difference to be placed in minutesSpent
 */
class TookPlaceBetweenTimeControl {

    private lastDateTime: EccoDateTime;
    private lastTime: EccoTime;

    constructor(private $startDateTime: DateTimeInput, private $endTime: TimeInput, private $minsBetween: $.JQuery,
            private $durationText: $.JQuery) {

        this.$startDateTime.change( datetime => {
            this.lastDateTime = datetime;
            this.updateBetweenTime();
        });

        this.$endTime.change( time => {
            this.lastTime = time;
            this.updateBetweenTime();
        });
    }

    private updateBetweenTime() {
        if (!this.lastDateTime || !this.lastTime) {
            this.$minsBetween.val("0");
            return;
        }
        const st = new EccoTime(this.lastDateTime.getHours(), this.lastDateTime.getMinutes(), 0, 0);
        const duration = this.lastTime.subtract(st);
        this.$minsBetween.val(duration.inMinutes());

        const d = "("+duration.getHours() + " hours, " + duration.getMinutes() + " mins)";
        this.$durationText.text(d);
    }

}

export = EvidenceCommentForm;
