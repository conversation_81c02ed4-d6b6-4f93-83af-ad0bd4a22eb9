import {
    App<PERSON>ar<PERSON><PERSON>,
    AppBar<PERSON>ontextProvider,
    SidebarMenuBuilder, useAppBarContext,
    useChart,
    UserMenu,
    useServicesContext,
    useAdminModeMenu,
    useAppBarOptions
} from "ecco-components";
import {isOffline} from "ecco-dto";
import {SelectionCriteriaDto} from "ecco-dto";
import * as React from "react";
import {FC, useEffect, useState} from "react";
import {Route, Switch, useHistory, useParams, useRouteMatch} from "react-router";
import {CardsContainer} from "../components/CardsContainer";
import {DashboardReports} from "../components/DashboardReports";
import EventListSource = require("../calendar/EventListSource");
import ServiceListSource = require("../calendar/ServiceListSource");
import TaskListSource = require("../calendar/TaskListSource");
import ServiceOverview = require("../components/ServiceOverview");
import {useMediaQuery} from "@eccosolutions/ecco-mui";
import {TaskTimeline} from "../tasks/components/TaskTimeline";
import {ChartDefinition} from "ecco-reports";
import {SequenceAnalysis} from "ecco-reports";
import {ReportDataSourceFactory} from "ecco-reports";
import {lazyControlWrapper} from "../components/ControlWrapper";

// would place in entityLoadHooks, but for the amount of code being dragged across
export function useChartData(chartDefinitionUuid: string, override? : Partial<SelectionCriteriaDto>) {
    const {sessionData} = useServicesContext()
    const {chartDefinitionDto} = useChart(chartDefinitionUuid)
    const [chartDefinition, setChartDefinition] = useState<ChartDefinition>(null)
    const [chartData, setChartData] = useState<SequenceAnalysis<any>>(null)

    // be good to use promiseToSuspendedResult so we can use 'usePromise' without depending on chartDefinitionDto
    useEffect(() => {
        if (chartDefinitionDto) {
            chartDefinitionDto.definition.selectionCriteria = {...chartDefinitionDto.definition.selectionCriteria, ...override};
            const chartDef = new ChartDefinition(chartDefinitionDto, sessionData)
            setChartDefinition(chartDef)
            ReportDataSourceFactory.getDataSource(chartDef).getData().then(data => {
                setChartData(data)
            })
        }
    }, [chartDefinitionDto])
    return {chartData: chartData, chartDefinition: chartDefinition};
}


//Causes issues -> const CardsContainer = React.lazy(() => import("../components/CardsContainer"));
const Chart = React.lazy(() => import("../components/Chart"));
const Services = () => <CardsContainer sources={[new ServiceListSource()]}/>;
const AdHocTasks = () => <CardsContainer sources={[new TaskListSource()]}/>;
const AuditChart = () => <Chart chartUuid={"04500000-0000-babe-babe-dadafee1600d"}/>;
const ProjectCalendarChart = () => <Chart chartUuid={"04800000-0000-babe-babe-dadafee1600d"}/>;
const QuickLog = React.lazy(() => import("../referral/components/QuickLog"))

const MyEvents = () => <CardsContainer sources={[new EventListSource()]}/>;
// 'Tasks Incomplete' report
export const MyChartDefUuid = "04900000-0000-babe-babe-dadafee1600d"
const MyTasksChart = (props: {override?: Partial<SelectionCriteriaDto> | undefined}) =>
        <Chart override={props.override} autoRun={true} chartUuid={MyChartDefUuid}/>;
const MyChecksChart = (props: {override?: Partial<SelectionCriteriaDto> | undefined}) =>
        <Chart override={props.override} autoRun={true} chartUuid={"06100000-0000-babe-babe-dadafee1600d"}/>;

// @ts-ignore
const ReportsControl = lazyControlWrapper(() => import("../reports/charts/ChartListControl"), true);
const ReportsList: FC = () => {
    useAdminModeMenu("manage reports");
    useAppBarOptions("dashboard - management");
    return <ReportsControl/>;
};
const ChartHack = () => { // TODO: Move into Chart
    const {chartUuid} = useParams<{chartUuid: string}>();
    //const override = deriveOverrides();
    return <Chart chartUuid={chartUuid} />;
};


const MainMenuItems:FC<{base: string}> = ({base}) => {
    const {sessionData} = useServicesContext();
    const chartsEnabled = useMediaQuery("(min-width: 768px)")
    return new SidebarMenuBuilder(base)
            .addSubHeader("team", chartsEnabled)
            .addOwnRoute("management", "fa fa-dot-circle-o", "mgt/",
                sessionData.hasRoleReferralAdmin() &&  chartsEnabled)
            .addOwnRoute("operations", "fa fa-star", "ops",
                sessionData.hasRoleReferralAdmin() && chartsEnabled)
            .addOwnRoute("services", "fa fa-star", "services",
                sessionData.isEnabled("menu.welcome.link.dashboardServices")
                && sessionData.hasRoleSysAdmin())
            .addOwnRoute("events", "fa fa-calendar-o", "project-calendar/",
                sessionData.isEnabled("menu.welcome.link.projectcalendar") &&  chartsEnabled)
            .addOwnRoute("audits", "fa fa-history", "audits",
                sessionData.hasRoleReferralAdmin() && chartsEnabled)
            .addDivider("1")

            .addSubHeader("my")
            // TODO we have a cards version of 'tasks'
            .addOwnRoute("tasks", "fa fa-check-circle-o", "tasks", chartsEnabled)
            .addOwnRoute("tasks", "fa fa-check-circle-o", "tasks-timeline", !chartsEnabled)
            // for some future idea of 'my checks' - as in, my client checks
            //.addOwnRoute("checks", "fa fa-check-circle-o", "checks")
            .addOwnRoute("tasks (del)", "fa fa-check-circle-o", "tasksAdHoc",
                sessionData.hasRoleEvidenceAdmin())
            .addOwnRoute("events", "fa fa-calendar", "events")

            // TODO move these to dedicated roles on home page - as per 'care app'
            .addExternalRoute("care checks", "fa fa-calendar-o", "nav/r/care/",
                sessionData.isEnabled("menu.welcome.link.care"))
            .addExternalRoute("lone working checks", "fa fa-calendar-o", "nav/r/care/lone-working",
                sessionData.isEnabled("menu.welcome.link.care"))
        /*
        <FontIconListItem
            iconClasses="fa fa-calendar-o"
            onClick={"/nav/viewCalendar.html", true)}
            text="calendar"
        />*/
            .addDivider("2")
            .addExternalRoute("back to menu", "fa fa-arrow-left",
                "nav/secure/welcome.html",
                true, isOffline() ? "currently offline" : null, isOffline()
            )
            .build()
}

const DashboardAppBar: FC = () => {
    return <AppBarContextProvider>
        <DashboardAppBarBase/>
    </AppBarContextProvider>
}

const DashboardAppBarBase: FC = () => {
    let {path} = useRouteMatch();
    path = path == "/" ? "/" : path + "/";
    const history = useHistory();
    const wideMode = path.startsWith("/nav/w/");
    const {sessionData} = useServicesContext();
    const userId = sessionData && sessionData.getDto().userId;

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    return <AppBarBase appColour={ctx.appColour}
                       title={"dashboard overview"}
                       right={<UserMenu extraMenuItems={ctx.extraMenuItems}/>}
                       onLogoClick={() => history.push("/nav/r/welcome/")}
                       wideMode={wideMode}
                       drawerContent={<MainMenuItems base={path}/>}
    >
        <Switch>
            <Route path={`${path}ops`} component={() => <DashboardReports chartUuids={
                // 'dashboard tasks' / removed checks 06100000-0000-babe-babe-dadafee1600d
                ["05700000-0000-babe-babe-dadafee1600d"]
            }/>}/>
            <Route exact path={`${path}mgt`} component={ReportsList}/>
            <Route path={`${path}mgt/:chartUuid`}>
                <ChartHack/>
            </Route>
            <Route path={`${path}services`} component={Services}/>
            <Route path={`${path}services/:serviceId`} component={(props) => <ServiceOverview {...props}/>}/>
            <Route path={`${path}events`}>
                <MyEvents/>
            </Route>
            <Route path={`${path}tasks`} component={() => <MyTasksChart override={{userId}} />}/>
            <Route path={`${path}tasks-timeline`} component={() => <TaskTimeline override={{userId}}/>}/>
            <Route path={`${path}checks`} component={() => <MyChecksChart override={{userId}} />}/>
            <Route path={`${path}tasksAdHoc`} component={() => <AdHocTasks/>}/>
            <Route path={`${path}audits`} component={AuditChart}/>
            <Route path={`${path}project-calendar`} component={ProjectCalendarChart}/>
            <Route path={`${path}quick-log`} component={QuickLog}/>
            <Route exact path={`${path}`}>
                <div/>
            </Route>
        </Switch>
    </AppBarBase>;
}

export default DashboardAppBar;