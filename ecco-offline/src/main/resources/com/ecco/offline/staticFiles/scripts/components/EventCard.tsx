import * as React from "react";
import {FC, ReactFragment} from "react";

import {SessionData} from "ecco-dto";
import {Card, CardActions, CardHeader, Grid} from "@eccosolutions/ecco-mui";
import {EventResourceDto} from "ecco-dto/calendar-dto";
import {serviceRecipientCalendarEntry} from "../referral/ServiceRecipientAppointmentsControl";
import EvidenceDelegatingForm from "../evidence/EvidenceDelegatingForm";
import {EventBackingData, Link, useServicesContext} from "ecco-components";
import ReactDom = require("react-dom");
import {EventResourceDtoWithUserCalendar} from "ecco-dto";

/* Allow for non-jsx calls */
// export const eventCardFactory = (event: EventResourceDto) => <EventCard event={event as EventResourceDtoWithUserCalendar}/>;

export class EventCardStatic {
    // see ChartControl.ts where this gets cleared
    public static TEMP_CACHE_BUST_KEY = "EventCardCache";
}

function getCache(eventId: string) {
    const cache = localStorage[EventCardStatic.TEMP_CACHE_BUST_KEY];
    return cache ? cache.split(',').indexOf(String(eventId)) > -1 : null;
}

function addToCache(eventId: string) {
    const cache = localStorage[EventCardStatic.TEMP_CACHE_BUST_KEY];
    localStorage[EventCardStatic.TEMP_CACHE_BUST_KEY] = cache ? `${cache},${eventId}` : eventId;
}

export class EventCardWrapper {
    public static enhance($element: $.JQuery, events: EventResourceDto[], sessionData: SessionData,
                          viewingAsName?: string | undefined) {
        ReactDom.render(
            //withNavigation(<EventCard event={event} sessionData={sessionData} navigation={null}/>)
            <Grid container spacing={2}>
                {events.map(event => <Grid item className="card" lg={3} md={4} sm={6} xs={12}>
                    <_EventCard event={event} sessionData={sessionData} viewingAsName={viewingAsName}/>
                </Grid>)}
            </Grid>
            , $element[0]
        );
    }
}

interface CardProps {
    showDate?: boolean | undefined;
    event: EventResourceDto;
    viewingAsName?: string | undefined
}

interface CardState {
    evidenceWorkUuid: string;
}

class _EventCard extends React.Component<CardProps & {sessionData: SessionData}, CardState> {

    private data: EventBackingData;

    constructor(props) {
        super(props);

        this.data = EventBackingData.fromEvent(this.props.sessionData, this.props.event as EventResourceDtoWithUserCalendar, this.props.viewingAsName);
        this.state = {
            evidenceWorkUuid: (this.data.evidenceWorkUuid || getCache(this.props.event.uid))
                ? "someTransientValue" : null
        }
    }


    private completedVisit() {
        addToCache(this.data.eventId);
        this.setState({evidenceWorkUuid: "someTransientValue"});
    }

    override render() {
        const opacity = this.state.evidenceWorkUuid ? 0.6 : 1;
        const background = this.state.evidenceWorkUuid ? "#ddd" : "";
        return (
                <Card style={{marginTop: 2, marginBottom: 2, opacity, backgroundColor: background}}>
                    <CardHeader
                        title={this.data.title}
                        subheader={this.data.subtitle}
                    />
                    {/*<CardText>*/}
                    {/*</CardText>*/}
                    <CardActions>
                        {this.data.links.map(link => this.renderLink(link))}
                    </CardActions>
                </Card>
        );
    }

    // TODO: Extract to HateoasHandlerContext so it can be configured elsewhere.
    private renderLink(link: Link) : ReactFragment {
        if ((link.text == "edit-adhoc") && this.data.serviceRecipientId) {
            if (this.state.evidenceWorkUuid) {
                return <span key={link.text}>edit</span>;
            } else {
                // keep with non-jsx approach for now as we need to convert to async approach
                return <a key={link.text} className="btn btn-link"
                          onClick={() => serviceRecipientCalendarEntry(this.data.serviceRecipientId, this.data.eventId)}>edit</a>;
            }
        }

        // "visit" comes from ServiceRecipientEventDecorator but we intercept it here to allow a popup rather than a new page
        if (link.text == "visit") {
            if (this.state.evidenceWorkUuid) {
                return <span key={link.text}>{link.text}</span>;
            } else {
                return <a key={link.text}
                          className="btn btn-link"
                          onClick={() => EvidenceDelegatingForm.showInModalByIds(this.data.serviceRecipientId, "visit", "needsAssessmentReduction",
                              this.data.eventId, () => this.completedVisit())
                              }>visit</a>;
            }
        }

        // "rota visit" comes here from ServiceRecipientEventDecorator
        return <a key={link.text} className="btn btn-link" href={link.href}>{link.text}</a>;
    }
}

// called by CalendarEventReportStageControl from the project calendar report with: "stageType": "CALENDAREVENT"
// called by CalendarListLoader which seems to load all live
export const EventCard: FC<CardProps> = props => {
    const {sessionData} = useServicesContext();
    return <_EventCard {...props} sessionData={sessionData}/>;
};
