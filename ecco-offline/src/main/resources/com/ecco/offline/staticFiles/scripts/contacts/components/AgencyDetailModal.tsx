import $ = require("jquery");
import * as React from "react"
import {
    AddressLocation,
    apiClient,
    update,
    UpdateSpec
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {
    AGENCYCATEGORY_LISTNAME,
    ContactsAjaxRepository, ListDefinitionEntry,
    SessionDataAjaxRepository
} from "ecco-dto";
import {Agency} from "ecco-dto/contact-dto";
import {Alert, ControlLabel, FormGroup} from "react-bootstrap";
import {isValidEmailAddress} from "../../common/validation";
import {FieldGroup, SelectGroup} from "../../components/ComponentUtils";
import {EccoDate, StringToStringMap} from "@eccosolutions/ecco-common";

interface Props extends React.ClassAttributes<AgencyDetailModal> {
    agencyId?: number | undefined;
    show: boolean;
    contextId: number; // context of a new agency, this is the serviceRecipientId which is used to associate the Agency with a service allocation
    onSave?: ((agency: Agency) => void) | undefined;
    onCancel?: (() => void) | undefined;
}

type FormFields<V> = {
    [P in keyof Agency]?: V
}

interface State {
    agency: Agency;
    initialAddressLocationId?: number | undefined
    agencyCategories: ListDefinitionEntry[];
    errors: FormFields<string>;
    alerts: StringToStringMap;
}

class AgencyDetailModal extends React.Component<Props, State> {
    private saveTimer;
    private repository = new ContactsAjaxRepository(apiClient);
    private sessionDataRepository = new SessionDataAjaxRepository(apiClient);

    private addressLocation: AddressLocation = null;

    constructor(props) {
        super(props);

        const agency = AgencyDetailModal.getNewAgency(this.props.contextId);
        this.state = {
            agency,
            agencyCategories: [],
            errors: AgencyDetailModal.validate(agency),
            alerts: {}
        };
    }

    public override componentDidMount() {
        this.loadData(this.props.agencyId);
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        this.loadData(nextProps.agencyId);
    }

    public loadData(agencyId: number) {
        this.sessionDataRepository.getSessionData().then(sessionData => {
            if (agencyId) {
                this.repository.findOneAgency(agencyId).then(a => {
                    this.setState({
                        agency: a,
                        initialAddressLocationId: a.addressedLocationId,
                        errors: AgencyDetailModal.validate(a),
                        alerts: {warning: 'this information could be shared between clients, so editing these details will affect all occurrences'}
                    });
                });
            }
            this.setState({
                agencyCategories: sessionData.getListDefinitionEntriesByListName(AGENCYCATEGORY_LISTNAME)
            });
        });
    }

    public override componentWillUnmount() {
        window.clearTimeout(this.saveTimer);
    }

    private static getNewAgency(contextId: number): Agency {
        return {
            discriminator: "agency",
            contextId: contextId,
            outOfArea: false,
        } as Agency;
    }

    private handleAgencyChange(updater: (event) => UpdateSpec<Agency>, event) {
        const agency = update(this.state.agency, updater(event.target.value));
        const errors = AgencyDetailModal.validate(agency);

        this.setState({agency, errors});
    }

    private handleCheckboxChange(updater: (event) => UpdateSpec<Agency>, event) {
        const agency = update(this.state.agency, updater(event.target.checked));
        const errors = AgencyDetailModal.validate(agency);

        this.setState({agency, errors});
    }

    private handleAddressChange = (addressId: number) => {
        const agency = update(this.state.agency, {addressedLocationId: {$set: addressId}});
        const errors = AgencyDetailModal.validate(agency);

        this.setState({agency, errors});
    };

    private handleAddressValidChange = (valid: boolean) => {
        const errors = AgencyDetailModal.validate(this.state.agency); // create errors object
        if (!valid) {
            errors.address = 'required';
        }
        this.setState({errors});
    };

    private handleSaveClick = () => {
        this.repository.saveAgency(this.saveAddressForAgency())
        .then(response => {
            this.saveTimer = window.setTimeout(this.handleSaveTimeout, 1500); // TODO: review. this looks dubious
            const agency = update(this.state.agency, {contactId: {$set: response.id}});

            this.setState({ agency,
                alerts: {info: 'agency saved'}
            });
        }).catch(() => {
            this.setState({alerts: {danger: 'failed to save agency'}});
        });
    };

    private handleSaveTimeout = () => {
        this.props.onSave && this.props.onSave(this.state.agency);

        const agency = AgencyDetailModal.getNewAgency(this.props.contextId);
        this.setState({ agency,
            errors: AgencyDetailModal.validate(agency),
            alerts: {}
        });
    }

    private handleCancelClick() {
        this.props.onCancel && this.props.onCancel();

        const agency = AgencyDetailModal.getNewAgency(this.props.contextId);
        this.setState({ agency,
            errors: AgencyDetailModal.validate(agency),
            alerts: {}
        });
    }

    private saveAddressForAgency(): Agency {
        this.state.agency.addressedLocationId = this.addressLocation.getAddressLocationId()
        return this.state.agency
    }

    private static validate(agency: Agency) {
        const errors: FormFields<string> = {};
        if (!agency.companyName) errors.companyName = 'required';
        // if (!agency.addressedLocationId) errors.addressedLocationId = 'required';
        if (agency.email && !isValidEmailAddress(agency.email)) {
            errors.email = 'must be valid email';
        }
        return errors;
    }

    public isValid(): boolean {
        return Object.keys(this.state.errors).length == 0;
    }

    override render() {
        const inputProps = {
            labelClassName: 'col-xs-2',
            wrapperClassName: 'col-xs-10',
            autoComplete: 'off',
            hasFeedback: true};

        const AlertElement = Object.entries(this.state.alerts)
            .map(([style, message]) => <Alert bsStyle={style.toString()}>{message}</Alert>);

        let $alerts = $("div.alert");
        $alerts.length > 0 && $alerts[0].scrollIntoView();

        return possiblyModalForm(
            "agency details",
            true, this.props.show,
            () => {this.handleCancelClick()},
            // this.props.commandForm.cancelForm(),
            () => {this.handleSaveClick()},
            // this.props.commandForm.submitForm(),
            !this.isValid(),
            false,
            <>
                {AlertElement}
                <form className='form-horizontal'>
                    <SelectGroup
                        label='category'
                        value={this.state.agency.agencyCategoryId}
                        onChange={event => this.handleAgencyChange(v => ({agencyCategoryId: {$set: v}}), event)}
                        validationState={this.state.errors.agencyCategoryId ? 'error' : 'success'}
                        {...inputProps}>
                        <option value=''>-</option>
                        {this.state.agencyCategories.map(ld => {
                            return <option key={ld.getId()}
                                    value={ld.getId()}
                                    disabled={ld.getDisabled()}
                            >
                                {ld.getName()}
                            </option>
                        })}
                    </SelectGroup>
                    <FieldGroup
                        type='text'
                        label='company'
                        value={this.state.agency.companyName}
                        onChange={event => this.handleAgencyChange(v => ({companyName: {$set: v}}), event)}
                        validationState={this.state.errors.companyName ? 'error' : 'success'}
                        {...inputProps} />
                    <FieldGroup
                        type='text'
                        label='phone'
                        value={this.state.agency.phoneNumber}
                        onChange={event => this.handleAgencyChange(v => ({phoneNumber: {$set: v}}), event)}
                        validationState={this.state.errors.phoneNumber ? 'error' : 'success'}
                        {...inputProps} />
                    <FieldGroup
                        type='text'
                        label='email'
                        value={this.state.agency.email}
                        onChange={event => this.handleAgencyChange(v => ({email: {$set: v}}), event)}
                        validationState={this.state.errors.email ? 'error' : 'success'}
                        {...inputProps} />
                    <FormGroup
                        //validationState={this.state.errors.outOfArea ? 'error' : 'success'}
                    >
                        <ControlLabel>out of area</ControlLabel>
                        <div className='col-xs-10 col-xs-offset-2'>
                            <input
                                type='checkbox'
                                value={this.state.agency.outOfArea ? "true" : undefined}
                                onChange={event => this.handleCheckboxChange(v => ({outOfArea: {$set: v}}), event)}
                            />
                        </div>
                    </FormGroup>
                    {this.state.agency.contactId && <FormGroup>
                        <ControlLabel>archived</ControlLabel>
                        <div className='col-xs-10 col-xs-offset-2'>
                            <input
                                type='checkbox'
                                value={this.state.agency.archived ? "true" : undefined}
                                onChange={event => this.handleCheckboxChange(v => ({archived: {$set: v ? EccoDate.todayLocalTime().formatIso8601() : null}}), event)}
                            />
                        </div>
                    </FormGroup>}
                </form>
                <AddressLocation
                    showBuildings={false}
                    contactId={this.state.agency.contactId}
                    displayAddress={this.state.agency.address}
                    addressLocationId={this.state.initialAddressLocationId}
                    ref={c => this.addressLocation = c}
                    handleAddressValidChange={this.handleAddressValidChange}
                />
            </>
        );
    }
}

export = AgencyDetailModal;
