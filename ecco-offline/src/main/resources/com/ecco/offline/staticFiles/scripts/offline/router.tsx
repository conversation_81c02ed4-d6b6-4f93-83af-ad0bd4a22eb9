import {
    CareVisitHistoryController,
    handleLazy, LoadSRWithEntitiesContext,
    RouteFallbackWithDiagnostics,
    SvcRecPageRouter,
    useClient,
    usePromise,
    useReferralBySrId,
    useServiceRecipientWithEntities,
    useServicesContext,
    Sr2View,
    UnifiedTimeline,
    AuditHistoryController, CommandForm, TasksControlNG
} from "ecco-components";
import {TaskDto} from "ecco-dto/workflow-dto";
import * as React from "react";
import {Component, ComponentClass, FC, useState} from "react";
import {Route, Switch, useParams} from "react-router";
import AttachmentsControl from "../attachments/AttachmentsControl";
import {CalendarWithStaffOverlay} from "ecco-calendar";
import {CardsContainer} from "../components/CardsContainer";
import OfflineAppBar from "./OfflineAppBar";
import {EvidenceGroup, isOffline, ReferralSummaryDto} from "ecco-dto";
import {SelectionCriteriaDto} from "ecco-dto";
import {Box, Grid} from "@eccosolutions/ecco-mui";
import {SupportHistory} from "../evidence/support/components/SupportHistory";
import {RiskHistory} from "../evidence/support/components/RiskHistory";
import {ReferralOfflineLandingPage, ReferralOfflineOverview} from "../referral/components/ReferralOfflineLandingPage";
import {ReferralContacts} from "../referral/ReferralContacts";
import ControlWrapper, {lazyControlWrapper} from "../components/ControlWrapper";
import {CommunicationPane} from "../contacts/components/communicationControls";
import Chart from "../components/Chart";
import ReferralOnSrList from "../referral/components/ReferralOnSrList";
import {NewReferralWizard} from "../referral/components/NewReferralWizard";
import {getReferralDirectConditionalHref} from "../clientdetails/components/ClientReferralsPopup";
import GoalsControl from "../evidence/tabular/GoalsControl";
import {SystemAccess} from "../admin/account/SystemAccess";
import {SvcRecLandingPage} from "../service-recipients/SvcRecLandingPage";
import EventListSource = require("../calendar/EventListSource");
import TaskListSource = require("../calendar/TaskListSource");
import TaskCard = require("../components/TaskCard");
import ReferralListSource = require("../referral/ReferralListSource");
import OfflineSetup = require("./OfflineSetup");
import {VisitController} from "../care/Visit";
import {useRelationshipClickEventIntegration} from "../referral/chart/RelationshipsChart";
import {EmergencyDetailsForm} from "../referral/components/EmergencyDetailsForm";
import {SourceAudit} from "ecco-dto/evidence/evidence-command-dto";
import {BuildingsListControl} from "../buildings/controls/BuildingsListControl";
import WorkersListControl from "../hr/WorkersListControl";
import ReferralsListControl from "../referral/ReferralsListControl";
import ChecksEvidenceForm from "../evidence/multi-instance/ChecksEvidenceForm";
import CommandHistoryListControl from "../service-recipients/CommandHistoryListControl";
import {IncidentSchemaList} from "ecco-incidents";
import {RepairSchemaList} from "ecco-repairs";
import {NewIncident} from "../incidents/inbound/NewIncident";

// **********************
// ******* WIRING *******
// **********************
// offline referral app
    // url is http://localhost:8888/ecco-war/nav/r/main/referrals/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'referrals/:srId' is intercepted here
// new calendar page
    // url is http://localhost:8888/ecco-war/nav/r/main/sr/200182/calendar/
    // we add a specific route/page for the calendar to replace the jsp one to get the same webpack'd version
    // it just shows the jsx with a back button back to the jsp client file
// new referral page
    // url is http://localhost:8888/ecco-war/nav/r/main/sr2/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'sr2/:srId' is intercepted here
// new staff page
    // url is http://localhost:8888/ecco-war/nav/r/main/staff/job/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'staff/job/:srId' is intercepted here
    // specific menus are enabled in Sr2AppBar
// new building page
    // url is http://localhost:8888/ecco-war/nav/r/main/bldg/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'bldg/:srId' is intercepted here
    // specific menus are enabled in Sr2AppBar
// new incidents page
    // url is http://localhost:8888/ecco-war/nav/r/main/inc/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'inc/:srId' is intercepted here
    // specific menus are enabled in Sr2AppBar
// new repairs page
    // url is http://localhost:8888/ecco-war/nav/r/main/repair/200182/
    // which CommonOnlinePageController opens online/main which loads offline/main.tsx
    // which loads this... and 'repair/:srId' is intercepted here
    // specific menus are enabled in Sr2AppBar

// OFFLINE capable matching '/'

const NotOffline = props => {
    return isOffline()
            ? <Box m={2}><h3>This function is not available offline</h3></Box>
            : props.children;
}

const eventSources = [new EventListSource()];
const Events = () => <CardsContainer sources={eventSources}/>;

const taskSources = [new TaskListSource()];
const Tasks = () => {
    return <NotOffline>
        <CardsContainer sources={taskSources}/>
    </NotOffline>;
};

const referralListSources = [new ReferralListSource()]; // so that prop doesn't change each time
const Referrals = () => <CardsContainer canShowServices={true} sources={referralListSources}/>;

export const App = () => {
    return <Switch>
        {/* ONLINE new calendar page from legacy referral */}
        <Route path="/sr/:srId/calendar" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <CalendarWrapper srId={srId}/>;
        }}/>

        {/* ONLINE new wip file - menu is in SrAppBar */}
        <Route path="/sr2/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <Sr2View basepath = {`/sr2/${srId}/`} srId={srId}/>
        }}/>

        {/* ONLINE new staff file - menu is in SrAppBar */}
        <Route path="/staff/job/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <Sr2View basepath = {`/staff/job/${srId}/`} srId={srId}/>
        }}/>

        {/* ONLINE new bldg file - menu is in SrAppBar */}
        <Route path="/bldg/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <Sr2View basepath = {`/bldg/${srId}/`} srId={srId}/>
        }}/>

        {/* ONLINE new incident file - menu is in SrAppBar */}
        <Route path="/inc/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <Sr2View basepath = {`/inc/${srId}/`} srId={srId}/>
        }}/>

        {/* ONLINE new repair file - menu is in SrAppBar */}
        <Route path="/repair/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <Sr2View basepath = {`/repair/${srId}/`} srId={srId}/>
        }}/>

        {/* OFFLINE referrals - see ReferralCard.tsx */}
        {/* NB the bar opens a ReferralFrame in modal - which hides the new menu */}
        <Route path="/referrals/:srId/" component={() => {
            const srId = parseInt(useParams<{srId: string}>().srId)
            return <OfflineAppBar>
                    <LoadSRWithEntitiesContext srId={srId}>
                        <ReferralOfflineLandingPage srId={srId}/>
                    </LoadSRWithEntitiesContext>
                </OfflineAppBar>;
            /*return <Sr2AppBarProvider basepath={`/referrals/`} preview={false} srId={srId}>
                <Sr2AppContentRouter basepath={`/referrals/`} preview={false}/>
            </Sr2AppBarProvider>*/
        }}/>

        {/* OFFLINE app - menu is in AppBar */}
        <Route path="/">
            <OfflineAppBar>
                <Switch>
                    <Route path="/offline/setup"><OfflineSetup/></Route>
                    <Route path="/events" component={Events}/>
                    <Route path="/tasks" component={Tasks}/>
                    <Route path="/referrals" component={Referrals}/>
                    <Route path="/calendar">
                        <div style={{maxWidth: 1340, marginLeft: "auto", marginRight: "auto"}}>
                            <CalendarWithStaffOverlay/>
                        </div>
                    </Route>
                    <Route exact path="/" component={Events}/>
                    <RouteFallbackWithDiagnostics/>
                </Switch>
            </OfflineAppBar>
        </Route>
        <RouteFallbackWithDiagnostics/>
    </Switch>;
};




// ONLINE assumed for now

const PadInCenter: FC = (props) => {
    return <Grid container justify="center">
        <Grid item xs={12} md={8}>
            {props.children}
        </Grid>
    </Grid>
}

export const CalendarWrapper: FC<{srId: number}> = props => {
    //const {preview} = usePreviewReferralFile();
    return handleLazy(<CalendarWithStaffOverlay serviceRecipientId={props.srId} hideBackToFile={true}/>)
};

export const ContactsWrapper: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    return handleLazy(
        <PadInCenter>
            <ReferralContacts serviceRecipientId={props.srId} sessionData={sessionData} printView={false}/>
        </PadInCenter>
    )
};

export const RelationshipsWrapper: FC<{srId: number}> = props => {
    useRelationshipClickEventIntegration();
    // @ts-ignore
    const C = lazyControlWrapper(() => import("../referral/chart/RelationshipsChart").then(i => ({default: i.RelationshipsChart})), undefined, props.srId);
    return <C/>;
};

export const AppointmentsWrapper: FC<{srId: number}> = props => {
    // @ts-ignore
    const C = lazyControlWrapper(() => import("../referral/ServiceRecipientAppointmentsControl"), props.srId);
    return <PadInCenter>
            <C/>
    </PadInCenter>;
};

export const CommunicationWrapper: FC<{srId: number}> = props => {
    const {context} = useServiceRecipientWithEntities(props.srId);

    switch (context?.serviceRecipient?.prefix) {
        case "r":
            return <PadInCenter><CommunicationPane contactId={context.serviceRecipient.contactId}/></PadInCenter>
        case "m":
            return (context.repair.supportWorkerId) ? <PadInCenter><CommunicationPane contactId={context.repair.supportWorkerId}/></PadInCenter> : null
        default:
            return null;
    }

};

export const SupportHistoryWrapper: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {context} = useServiceRecipientWithEntities(props.srId);
    return <PadInCenter>
            <SupportHistory srId={props.srId} taskName={context.serviceType.getFirstSupportTaskName(sessionData)}/>
        </PadInCenter>
};

export const VisitWrapper: FC<{eventId: string}> = props => {
    return <PadInCenter>
        <VisitController eventId={props.eventId}/>
    </PadInCenter>;
};

export const VisitHistoryWrapper: FC<{srId: number}> = props => {
    return <PadInCenter>
        <CareVisitHistoryController srId={props.srId}/>
    </PadInCenter>;
};

export const RiskHistoryWrapper: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {context} = useServiceRecipientWithEntities(props.srId);
    return <PadInCenter>
            <RiskHistory srId={props.srId} taskName={context.serviceType.getFirstRiskTaskName(sessionData)}/>
        </PadInCenter>
};

// when on main/sr2/:srId/emergencyDetails (not via /tasks/ - needed CommandForm)
export const EmergencyDetailsWrapper: FC<{srId: number}> = props => {
    const [showTask, setShowTask] = useState(true)

    // react-router useLocation requires <BrowserRouter>
    // but we don't need it - we're expecting an actual address bar url
    const query = window.location.search
    //const location = useLocation();
    const source = new URLSearchParams(query).get('source') as SourceAudit;
    return <PadInCenter>
        <CommandForm onCancel={() => setShowTask(false)} onFinished={() => setShowTask(false)}>
            {showTask && <EmergencyDetailsForm serviceRecipientId={props.srId} taskHandle={null} readOnly={true} source={source}/>}
        </CommandForm>
    </PadInCenter>;
};

export const AttachmentsWrapper: FC<{srId: number}> = props => {
    return <PadInCenter>
        <AttachmentsControl srId={props.srId}/>
    </PadInCenter>;
};

// see referral-all-referrals-control for 'new referral' wizard
export const ServicesWrapper: FC<{srId: number}> = props => {
    const {referral} = useReferralBySrId(props.srId);
    const {client} = useClient(referral?.clientId);

    if (!(referral && client)) {
        return null;
    }

    // as per referral-all-referrals-control.tag
    const openOrTriggerWizard = (r: ReferralSummaryDto) => {
        if (r) {
            // taken from ReferralWizard (this would use 'environment.appRootURI')
            const referralUrl = getReferralDirectConditionalHref({referralId: r.referralId, serviceRecipientId: r.serviceRecipientId}, false);
            window.onbeforeunload = null;
            window.location.href = referralUrl;
            // taken from ReferralWizard
        } else {
            // no callback on 'selected' required, since the list of existing referrals is skipped
            NewReferralWizard.popup(client.clientId, function(rs) {}, true);
        }
    };
    return handleLazy(
        <PadInCenter>
            <ReferralOnSrList clientId={undefined} serviceRecipientId={props.srId} onChange={openOrTriggerWizard}/>
        </PadInCenter>)
};

export const IncidentsWrapper: FC<{srId: number}> = props => {
    return <PadInCenter>
        <NewIncident/>
        <IncidentSchemaList contactSrId={props.srId} />
    </PadInCenter>;
};

export const RepairsWrapper: FC<{srId: number}> = props => {
    return <PadInCenter>
        <RepairSchemaList buildingSrId={props.srId} />
    </PadInCenter>;
};

export const WorkerJobWrapper: FC<{srId: number}> = props => {
    // NewWorkerJobWizard.popup
    return null;
};

export const ReportWrapper: FC<{srId: number}> = props => {
    const SrChartDefUuid = "05800000-0000-babe-babe-dadafee1600d";

    const SrChart = (props: {override?: Partial<SelectionCriteriaDto> | undefined}) =>
            <Chart override={props.override} autoRun={true} chartUuid={SrChartDefUuid}/>;
    const filter = `serviceRecipient:${props.srId}`;
    return handleLazy(<SrChart override={{serviceRecipientFilter: filter}} />);
};

const ServiceRecipientTasks = React.lazy(() => import("../service-recipients/ServiceRecipientTasks"));

class TaskCards extends Component<{cardComponent: ComponentClass<{task: TaskDto}>; tasks: TaskDto[]}, {}> {
//class TaskCards<T> extends React.Component<{cardComponent: ComponentClass<{task: T}>; tasks: T[]}, {}> {
    override render() {
        return <div>{this.props.tasks.map(t => <this.props.cardComponent task={t}/>)}</div>
    }
}

class MaterialTask extends Component<{task: TaskDto}, {}> {
    override render() {
        return <TaskCard showDate={true} task={this.props.task} />;
    }
}

const eventAndTaskSources = [new EventListSource(), new TaskListSource()];
const EventsAndTasks = () => {
    return <NotOffline>
        <CardsContainer sources={eventAndTaskSources}/>
    </NotOffline>;
};

export {default as WelcomePage} from "../welcome/WelcomeAppBar";
export {default as SettingAppBar} from "../service-config/SettingAppBar"


export const UnifiedTimelineWrapper: FC<{srId: number}> = props => {
    return <PadInCenter><UnifiedTimeline serviceRecipientId={props.srId}/></PadInCenter>;
};

export const AuditsWrapper: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    return <PadInCenter>
        <AuditHistoryController serviceRecipientId={props.srId} showWithoutSearch={true} sessionData={sessionData}/>
        {/*<AuditHistory serviceRecipientId={props.srId}/>*/}
    </PadInCenter>;
};

export const AuditsSearchWrapper: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    return <PadInCenter>
        <AuditHistoryController serviceRecipientId={props.srId} showWithoutSearch={false} sessionData={sessionData}/>
        {/*<AuditHistory serviceRecipientId={props.srId}/>*/}
    </PadInCenter>;
};

export const AccessWrapper: FC<{srId: number}> = props => {
    const {context} = useServiceRecipientWithEntities(props.srId);
    return handleLazy(context
            ? <PadInCenter><SystemAccess contactId={context.worker.contactId} workerId={context.worker.workerId}/></PadInCenter>
            : null);
};

export const ForwardPlanWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => GoalsControl.forwardPlanControl(props.srId, "support"), []);
    // useReloadHandler(() => control.load());
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

export const ForwardRiskPlanWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => GoalsControl.forwardPlanControl(props.srId, "risk"), []);
    // useReloadHandler(() => control.load());
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

// units
export const UnitWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => BuildingsListControl.buildingsListControl(props.srId), []);
    // useReloadHandler(() => control.load());
    if (resolved) {
        resolved.load();
    }
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

export const StaffWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => WorkersListControl.fromBuildingSrId(props.srId), []);
    // useReloadHandler(() => control.load());
    if (resolved) {
        resolved.load();
    }
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

/**
 * Shows a list of residents on a building
 */
export const ResidentWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => ReferralsListControl.fromBuildingSrId(props.srId), []);
    // useReloadHandler(() => control.load());
    if (resolved) {
        resolved.load();
    }
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

export const ChecksDueWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => ChecksEvidenceForm.fromBuildingSrId(props.srId), []);
    // useReloadHandler(() => control.load());
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};

export const ChecksHistoryWrapper: FC<{srId: number}> = props => {
    const {resolved, error, loading} = usePromise(() => Promise.resolve(CommandHistoryListControl.createWithIds(props.srId, EvidenceGroup.needs, null, true)), []);
    // useReloadHandler(() => control.load());
    if (resolved) {
        resolved.load();
    }
    return resolved ? <PadInCenter><ControlWrapper control={resolved} dontLoad={true}/></PadInCenter> : null;
};




// *******************
// to see all the features, also configure:
//   - goalName or goalPlan on support for the forward-plan
//   - some items with target dates for the forward-plan
//   - relationships - has config (newMultipleReferral) or primaryReferralId
//   - reports - referralOverview.dashboard.demoware
//   - communication - referralOverview.communication
// *******************

// TODO
//  MISSING add/edit avatar
//  MISSING hact (causes) / risk flags?
//  MISSING overview, as custom form (and residing at, ReferralOverview or ReferralOverviewControl - preferred contact etc)
//  MISSING ? risk forward plan - this involves a reasonable refactor server-side (stash in dropbox risk-forward-plan)
//  CHECK death display correct
//  icons / text size / timelines (forward plan?)
//  unified timeline of history/fwd

export const ReferralOfflineOverviewLoader: FC<{srId: number}> = props => {
    const {sessionData} = useServicesContext();
    const {referral} = useReferralBySrId(props.srId);
    const {client} = useClient(referral?.clientId);
    const [section, setSection] = useState<string>(null);

    return !(referral && client)
            ? null
            : <>
                <ReferralOfflineOverview referral={referral} client={client} sessionData={sessionData} expanded={section} expand={(section) => setSection(section)} renderContacts={false}/>
            </>;
}

export const SrFileLandingPageChooser = (props: {srId: number, preview: boolean}) => {
    return props.preview
            ? <SvcRecLandingPage {...props}/>
            : <ReferralOfflineLandingPage srId={props.srId}/>;
}

export const Sr2AppContentRouter: FC<{basepath: string, preview?: boolean | undefined, eventId?: string | undefined}> = ({basepath, preview, eventId}) => {
    const {srId: srIdStr} = useParams<{ srId: string }>(); // param srId is already present from parent route
    const srId = parseInt(srIdStr);

    return <LoadSRWithEntitiesContext srId={srId}>
        <SvcRecPageRouter basepath={basepath} preview={preview} srId={srId} eventId={eventId}/>
    </LoadSRWithEntitiesContext>;
}

/* re-export items here to be part available as import {} from "ecco-offline" where allowed */
export {defaultPageComponentRegistry} from "./defaultPageComponentRegistry"
//export {FinancePage} from "../housing/FinancePage"

/* re-export items here to be part available as import {} from "ecco-offline" where allowed */
export {taskIntegrations} from "../workflow/tasklist/TasksControl";
export {auditHistoryIntegrations} from "../service-recipients/components/AuditHistory";
