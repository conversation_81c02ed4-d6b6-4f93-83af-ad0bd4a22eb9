import {Result, WebApiError} from "@eccosolutions/ecco-common";
import {
    AppBar,
    Button,
    CircularProgress,
    Dialog,
    DialogContent,
    DialogTitle,
    Grid,
    Step,
    StepButton,
    StepContent,
    Stepper,
    Too<PERSON>bar,
} from "@eccosolutions/ecco-mui";

import {applicationRootPath, resourceRootPath} from "application-properties";
import {AsyncSessionData, BuildingSearch, SchemaForm, update, UpdateSpec} from "ecco-components";
import {EccoTheme, Notifications, ErrorBoundary} from "ecco-components-core";
import {
    ApiClient,
    ObjectSchemaDto,
    SessionData,
    SessionDataAjaxRepository, SessionDataDto,
    SessionDataGlobal,
    TaskNames
} from "ecco-dto";
import {FormDefinition} from "ecco-dto/form-definition-dto";
import * as React from "react";
import * as ReactDom from "react-dom";
import {Route, Switch} from "react-router";
import {BrowserRouter} from "react-router-dom";
import {MUISchemaForm} from "../../components/MUISchemaForm";
import diff = require("json-patch-gen");
import { CustomFormFields } from "ecco-dto/evidence-dto";
import {RepairDetailsDto, RepairFields} from "../../referral/components/ReferralDetailsForm";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import {ifNeededSetDefaultGlobalEccoAPI} from "../../ifNeededSetDefaultGlobalEccoAPI";

const {add} = Notifications;


const apiClientInbound = new ApiClient(`${applicationRootPath}api/inbound/`,
    null, () => true);

const sessionDataRepository = new SessionDataAjaxRepository(apiClientInbound);

interface Props {
    buildingId: number | null;
    schema?: ObjectSchemaDto;
    schemaUri?: string;
}

type StepIndex = 0 | 1 | 2;
const StepIndexEnd = 2;

interface State {
    saved?: boolean;
    saved_id?: number;
    stepIndex?: StepIndex,
    logoPath: string,
    dto: object,
    repairDetails: RepairDetailsDto;
    formData: CustomFormFields,
    errors: Record<string, string|boolean>,
    schema: ObjectSchemaDto;
    globalConfig: SessionDataGlobal | null;
    serviceEmail: string | null;
    formDefinitionKey: FormDefinition;
    formDefinitionForm: FormDefinition;
    formDefinitionFull: FormDefinition;
    networkError: string;
}

const Entry = (props) => <Grid item sm={6} xs={12} >{props.children}</Grid>;

const repair_serviceTypeId = -600;

class Wizard extends MUISchemaForm<Props, State> {

    constructor(props: Props) {
        super(props);
        this.state = {
            saved: false,
            stepIndex: 0,
            logoPath: "",
            dto: {
                buildingId: props.buildingId,
                serviceTypeId: repair_serviceTypeId // part of the dto which maps to InboundRepairResource
            },
            repairDetails: {categoryId: null, rateId: null, priorityId: null},
            formData: {} as CustomFormFields,
            errors: {},
            schema: props.schema,
            serviceEmail: null,
            globalConfig: null,
            formDefinitionKey: null,
            formDefinitionForm: null,
            formDefinitionFull: null,
            networkError: null
        };
    }

    public override componentDidMount() {
        // NB repository has a client with: api/inbound/
        let globalConfigQ = sessionDataRepository.getGlobalConfigDto();
        let schemaQ = Promise.resolve(null);
        if (this.props.schemaUri && !this.props.schema) {
            schemaQ = this.loadSchema();
        }

        Promise.all([globalConfigQ, schemaQ])
            .then( ([globalConfig, schema] ) => {
            const logoId = globalConfig.settings["com.ecco:LOGO_FILE_ID"];
            const logoPath = logoId
                ? `${applicationRootPath}api/images/logo/${logoId}`
                : null;

            const sessionDataGlobal = new SessionDataGlobal(globalConfig);

            ifNeededSetDefaultGlobalEccoAPI(new SessionData(globalConfig as SessionDataDto));

            this.setState({
                  schema: schema || this.props.schema,
                  logoPath: logoPath,
                  globalConfig: sessionDataGlobal
            });
        });
    }

    // called on a change, not the initial render (so careful of default values needing data)
    public override componentDidUpdate(prevProps: Props, prevState: State, prevContext: any) {

        const buildingId = Number(this.state.dto["buildingId"]);

        // ignore if no bldg because we can't choose a definition without the bldg
        // error if no session data loaded because we need this - loaded on startup, so user would have to be very quick
        if (!this.state.globalConfig || !buildingId) {
            // if (!this.sessionData) {
            //     // could instead trigger below code when sessionData is loaded
            //     throw new Error("SessionData should be loaded by now");
            // }
            return;
        }

        if (prevState.dto["buildingId"] != buildingId) {

            // FIXED CONFIG
            const serviceTypeDomain = this.state.globalConfig.getServiceTypeById(repair_serviceTypeId);

            // only call setState once in componentDidUpdate

            // split the array of form defs - to specifically show them in different places
            const formDefinitionUuid = serviceTypeDomain.getTaskDefinitionSetting(TaskNames.referralDetails, "formDefinition");
            if (formDefinitionUuid) {
                const formDefKey = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 0);
                const formDefForm = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 1);
                // ensure we have one to use
                const formDefFull = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 2) ||
                                        this.state.globalConfig.findFormDefinition(formDefinitionUuid);
                if (formDefFull) {
                    // trigger a state change to render the form
                    // only call setState once in componentDidUpdate
                    const updateSpec: UpdateSpec<State> = {};
                    //updateSpec.stepIndex = {$set: 1};
                    updateSpec.formDefinitionKey = {$set: formDefKey};
                    updateSpec.formDefinitionForm = {$set: formDefForm};
                    updateSpec.formDefinitionFull = {$set: formDefFull};
                    //updateSpec.serviceEmail = {$set: service.parameters && service.parameters["email.notification"]};
                    this.setState(prevState => update(prevState, updateSpec));
                } else {
                    this.setState({
                          //serviceEmail: service.parameters && service.parameters["email.notification"]
                      });
                }
            }
        }
    }

    private loadSchema() {
        return apiClientInbound.get<ObjectSchemaDto>(this.props.schemaUri);
    }


    private handleNext = () => {
        const {stepIndex} = this.state;
        if (stepIndex < StepIndexEnd) {
            this.setState({stepIndex: stepIndex + 1 as StepIndex});
        }
        else {
            const errorMsgs: string[] = [];
            let someRequired = false;

            // this.state.errors.keys().map not valid, possibly also Object.values(this.state.agencyCategoryIndex).map
            for (const key in this.state.errors) {
                const v: boolean | string = this.state.errors[key];
                if (typeof v == "string") {
                    // some, such as postCode and serviceId set a 'required' string
                    if (v == "required") {
                        someRequired = true;
                    } else {
                        errorMsgs.push(`${key} ${v}`);
                    }
                } else {
                    if (v) {
                        someRequired = v;
                    }
                }
            }

            if (someRequired || errorMsgs.length > 0) {
                let msg = "please fix ";
                if (someRequired) {
                    msg = msg.concat("the required fields");
                }
                if (errorMsgs.length > 0) {
                    if (someRequired) {
                        msg = msg.concat(" and ");
                    }
                    msg = msg.concat("the following: " + errorMsgs.join(", "));
                }
                Notifications.add("refer-errors", msg);
            } else {
                this.save();
            }
        }
    };

    private handlePrev = () => {
        const {stepIndex} = this.state;
        if (stepIndex > 0) {
            this.setState({stepIndex: stepIndex - 1 as StepIndex});
        }
    };

    private save() {

        // this.props.schema.links.
        const uri = this.props.schemaUri.split("/$schema/")[0];
        add("save", "saving...");
        const patch = diff({}, this.state.formData);
        //const buildingId = Number(this.state.dto["buildingId"]);
        const formDefinitionUuid = this.state.formDefinitionFull?.uuid;

        const dto = {...this.state.dto,
            categoryId: this.state.repairDetails.categoryId,
            rateId: this.state.repairDetails.rateId,
            priorityId: this.state.repairDetails.priorityId
        };
        apiClientInbound.post<Result>(uri, {dto: dto, formDefinitionUuid: formDefinitionUuid, formData: patch}).then( result => {
                this.setState({
                    saved: true,
                    saved_id: parseInt(result.id)
                });
                // InboundRepair returns from RepairController#createImport the Result(repairId)
                add("save", "saved");
            })
            .catch( (error: WebApiError) => {
                this.setState({networkError: error.toString()});
            });
    }

    private renderStepActions(step) {
        return (
            <div style={{margin: '12px 0'}}>
                {step > 0 && (
                    <Button
                        variant="text"
                        disableTouchRipple={true}
                        disableFocusRipple={true}
                        onClick={this.handlePrev}
                    >
                        back
                    </Button>
                )}
                <Button
                    variant="contained"
                    disableTouchRipple={true}
                    disableFocusRipple={true}
                    color="primary"
                    //disabled={step == 3 && this.hasErrors()}
                    onClick={this.handleNext}
                    style={{marginRight: 12}}
                >
                    {step < StepIndexEnd ? "Next" : "Done"}
                </Button>
            </div>
        );
    }

    private getStepContent(step: StepIndex) {
        // const dtoStateSetter = dto => this.setState({dto});
        // const schema = implementInterface(this.state.schema).asObjectSchema();
        // const buildingId = Number(this.state.dto["buildingId"]);

        // mimic the MUISchemaForm error logic for our bootstrap fields
        // so that the submit button remains disabled if invalid

        // we don't need to set an initial error state for 'formData' because liveValidation triggers the initial state (by observation)
        //this.state.errors['formData'] = // somehow force validation check

        switch (step) {
            case 0:
                //return this.selectField(schema.properties, "serviceId", this.props.serviceId != null);
                // also needed <ServicesContextProvider> so put around Wizard
                return <BuildingSearch primaryOnly={false} buildingId={this.state.dto["buildingId"]} onChange={bldgId => this.setState({dto: {...this.state.dto, buildingId: bldgId}})}/>

            case 1:
                return (<div>
                    {/* alternative approach to pushing 'required' into ComponentUtils
                    <FormGroup validationState={this.state.dto['firstName'] == null ? "error" : "success"}>
                        {textInput("firstName", "First name", dtoStateSetter, this.state.dto)}
                    </FormGroup>
                    */}

                    <Grid container>
                        <RepairFields dto={this.state.repairDetails} setter={dto => this.setState({repairDetails: {...this.state.repairDetails, ...dto}})}/>
                    </Grid>

                    {this.state.formDefinitionKey && <SchemaForm
                            readOnly={false}
                            formData={this.state.formData}
                            formDefinition={this.state.formDefinitionKey}
                            onChange={(data, hasErrors) => {this.handleChangeForm(data, hasErrors)}}
                            resetData={false}
                    />}

                </div>);

            case 2:
                return <div>
                    <SchemaForm
                        readOnly={false}
                        formData={this.state.formData}
                        formDefinition={this.state.formDefinitionForm || this.state.formDefinitionFull}
                        onChange={(data, hasErrors) => {this.handleChangeForm(data, hasErrors)}}
                        resetData={false}
                    />
                </div>;
        }
    }

    private handleChangeForm(data: any, hasError: boolean) {
        this.setState(prevState => {
            const errorsUpdated = prevState.errors;
            errorsUpdated['formData'] = hasError;
            return update(prevState, {
                    formData: {$set: data},
                    errors: {$set: errorsUpdated}
                });
        });
    }

    override render() {
        if (!this.state.globalConfig) {
            return null;
        }
        return (
            <ErrorBoundary>
            <AsyncSessionData promiseFn={() => Promise.resolve(new SessionData(this.state.globalConfig.getDto() as SessionDataDto))}>
            <ServicesContextProvider>
                {this.state.schema
                ? <div style={{maxWidth: 760, maxHeight: 400, margin: 'auto'}}>
                    {this.state.networkError && <Dialog open={true}>
                        <DialogTitle>Something went wrong</DialogTitle>
                        <DialogContent>
                            There was an error: {this.state.networkError}
                            <p>Please 'dismiss' and try again.</p>
                            <Button
                                style={{float: "right"}}
                                onClick={() => this.setState({networkError: null})}
                            >
                                Dismiss
                            </Button>
                        </DialogContent>
                    </Dialog>}
                    {this.state.saved
                        ? <div style={{padding: "24px"}}>
                            <h2 className="md-headline">Repair saved</h2>
                            <p>Your repair has been saved and will be processed soon.</p>
                            <p>Your reference is: <b>i-id {this.state.saved_id}</b></p>
                            {this.state.serviceEmail &&
                                <p>Please send any additional information to: <a href={`mailto:${this.state.serviceEmail}?subject=ECCO New Repair (info on repair-id ${this.state.saved_id})`}>{this.state.serviceEmail}</a></p>
                            }
                            <Button
                                component={"a"}
                                // linkButton={true}
                                style={{float: "right"}}
                                disableTouchRipple={true}
                                disableFocusRipple={true}
                                onClick={() => window.location.reload()}
                            >
                                Add another
                            </Button>
                        </div>
                        : <div style={{padding: "24px"}}>
                            {this.state.logoPath ? <img style={{maxHeight: '75px'}} src={this.state.logoPath}/> : null}
                            <h3>Report a repair</h3>
                            <p>Repair details will be recorded in ECCO</p>
                            <Stepper
                                activeStep={this.state.stepIndex}
                                nonLinear={true}
                                orientation="vertical"
                            >
                                <Step>
                                    <StepButton style={{fontSize: "50px"}} onClick={() => this.setState({stepIndex: 0})}>
                                        Select the location that the repair relates to
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(0)}
                                        {this.renderStepActions(0)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 1})}>
                                        Enter key repair details
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(1)}
                                        {this.renderStepActions(1)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 2})}>
                                        Enter repair form
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(2)}
                                        {this.renderStepActions(2)}
                                    </StepContent>
                                </Step>
                            </Stepper>
                        </div>}
            </div>
            : <div key="progress" className="vertical-center">
                <div style={{width: '100%'}}>
                    <CircularProgress style={{margin: 'auto', display: 'block'}}/>
                </div>
            </div>
            }
            </ServicesContextProvider>
            </AsyncSessionData>
            </ErrorBoundary>
        );
    }
}

//type RouteParams = {serviceCategorisationId?: string};

const leftIcon =
    <img src={resourceRootPath + "themes/ecco/images/logo_white.png"} height="48"/>;

// see AppBarBase
const greenAppBar = "#36720A";

export function RepairComponent() {

    const search = window.location.search;
    const params = new URLSearchParams(search);
    //const serviceCategorisationId = useParams<RouteParams>().serviceCategorisationId;
    const buildingId = params.get("buildingId");

    return (
        <div>
            <AppBar
                position="fixed"
                style={{top: 0}}
            >
                <Toolbar
                    style={{
                        backgroundColor: greenAppBar
                    }}>
                    {leftIcon}
                </Toolbar>
            </AppBar>
            <div style={{marginTop: 64}}>
                <Wizard schemaUri="repairs/$schema/" buildingId={buildingId ? parseInt(buildingId) : null}/>
                {/*<Wizard schema={schema}/>*/}
            </div>
        </div>
    );
}

ReactDom.render((
    <EccoTheme prefix="refer">
        <BrowserRouter basename={applicationRootPath.substr(0, applicationRootPath.length - 1)}>
            <Switch>
                <Route exact path={["/p/r/repair", "/p/r/repair/:buildingId",
                    "/nav/r/repair", "/nav/r/repair/:buildingId",
                    "/nav/p/r/repair", "/nav/p/r/repair/:buildingId"]}>
                    <RepairComponent />
                </Route>
                <Route path="/">
                    <h3>incorrect wiring</h3>
                </Route>
            </Switch>
        </BrowserRouter>
    </EccoTheme> ),
    document.getElementById("appbar"));
