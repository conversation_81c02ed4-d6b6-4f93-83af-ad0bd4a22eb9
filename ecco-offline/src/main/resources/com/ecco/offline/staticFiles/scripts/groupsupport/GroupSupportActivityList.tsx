import * as React from "react";
import {FC, useMemo} from "react";
import {GroupActivityList, GroupActivityOptions} from "./GroupActivityList";
import {
    GroupSupportActivityListControl
} from "./GroupSupportActivityListControl";
import {GroupActivityCommand} from "./commands";
import {GROUPSUPPORTACTIVITY_LISTNAME} from "ecco-dto";
import {GroupPageType} from "ecco-dto";

export const groupSupportActivityOptions: GroupActivityOptions = {
    discriminator_orm: GroupActivityCommand.DISCRIMINATOR_SUPPORT,
    title: "group support",
    hasActivityType: true,
    hasVenue: true,
    hasCapacity: true,
    hasDuration: true,
    allowAttended: true,
    addService: true,
    hasCategory: false,
    hasReviewDate: false,
    activityListName: GROUPSUPPORTACTIVITY_LISTNAME,
    createListControl: (groupPageType: GroupPageType, activityTypeId: number, venueId: number, serviceId: number) => new GroupSupportActivityListControl(groupPageType, activityTypeId, venueId, serviceId)
}

/** entry point to the groups */
export const GroupSupportActivityList: FC = () => {
    const options = useMemo(() => groupSupportActivityOptions, []);
    return <GroupActivityList options={options}/>;
}
