import {
    App<PERSON>arB<PERSON>,
    AppBarContextProvider,
    CommandForm,
    SidebarMenuBuilder, useAppBarContext,
    UserMenu,
    GuidanceMenu,
    LoadSRWithEntitiesContext,
    CareVisitMenu,
    CareVisitMenuContextProvider,
    useServicesContext, QRScanMenu
} from "ecco-components";
import * as React from "react";
import Lazy = require("lazy");
import Sequence = LazyJS.Sequence;
import {FC, useState} from "react";
import {Route, Switch, useHistory, useRouteMatch} from "react-router";
import {ClientDetailEditor} from "ecco-components";
import AgencyDetailModal from "../contacts/components/AgencyDetailModal";
import TasksControl from "../workflow/tasklist/TasksControl";
import {CalendarEntryEditor} from "ecco-calendar";
import {AuditHistory} from "../service-recipients/components/AuditHistory";
import {applicationRootPath} from "application-properties";
import {lazyControlWrapper} from "../components/ControlWrapper"
import {CarePage} from "../care/CarePage";
import {DailyChecksPage} from "../dailyChecks/DailyChecksPage";
import {ReferComponent} from "../referral/inbound/refer";
import {ForwardPlanTimeline} from "ecco-components";
import {ReferralAggregateAnalysis, TableRepresentationBase} from "ecco-reports";
import {hactManagementAnalyser, HactManagementData} from "ecco-reports";
import {TaskTimeline} from "../tasks/components/TaskTimeline";
import {useChartData} from "../dashboard/DashboardAppBar";
import {
    qnAnswerWorkFromReferralAggregateAnalyser,
    qnAnswerWorkWithRefAggregateColumns
} from "ecco-reports";
import {QnAnswerWorkWithRefToReferralAggregate} from "ecco-reports";
import {MUIDataTable, Typography} from "@eccosolutions/ecco-mui";
import {
    getFieldRepresentation, RowContext
} from "ecco-reports";
import {Agency, Individual, SessionData, SourceType, TaskNames} from "ecco-dto";
import {AgencyWithProfessional} from "../contacts/AgencyWithProfessional";
import {AssociatedContactWizard} from "../contacts/AssociatedContactWizard";
import {EventPage} from "../calendar/EventPage";

// @ts-ignore - because typescript
const TeamReferralsList = lazyControlWrapper(() => import("../referral/ReferralsListControl"), false, undefined, true);

const MainMenuItems:FC<{base: string}> = ({base}) => {
    const {sessionData} = useServicesContext()
    return new SidebarMenuBuilder(base)
        .addSubHeader("test/dev (webpack only)")
        .addOwnRoute("all clients", "fa fa-user", "referrals/all",
            sessionData.hasRoleSysAdmin(), "NOTE: run staff app for testing referral features")
        .addOwnRoute("test client", "fa fa-user", "test-client",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test referral", "fa fa-user", "test-referral",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test agent", "fa fa-user", "test-agent",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test agent-prof", "fa fa-user", "test-agent-profs",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test agent-prof-wizard", "fa fa-user", "test-agent-profs-wizard",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test 'tasks'", "fa fa-check-circle-o", "test-tasks",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test event-popup", "fa fa-calendar-o", "test-event",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test tasks", "fa fa-check-circle-o", "test-tasks-dash",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test inbound", "fa fa-calendar-o", "test-inbound",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test conf-audits", "fa fa-calendar-o", "test-config-audit",
            sessionData.hasRoleSysAdmin())
        // for seeing the care cards - first assign your login to an appt today
        .addOwnRoute("test care app", "fa fa-tablet", "care",
            sessionData.hasRoleSysAdmin())
        // for seeing the daily tasks cards
        .addOwnRoute("test daily checks app", "fa fa-tablet", "dailyChecks",
            sessionData.hasRoleSysAdmin())
        .addOwnRoute("test forward plan", "fa fa-arrow-down", "test-forward-plan",
            sessionData.hasRoleSysAdmin(), "srId: 200298 - needs QL as target")
        .build()
}

const ChartHactDataDebug: FC<{chartUuid: string}> = ({chartUuid}) => { // Base must start and end in / (i.e. /r/welcome/ or / )
    const {chartData, chartDefinition} = useChartData(chartUuid)

    if (!chartData) return null

    // DEBUG HACT ANALYSER
    const data = chartData as ReferralAggregateAnalysis
    let managementData: Sequence<HactManagementData> = hactManagementAnalyser(chartDefinition, Lazy(data.getData())).getData();
    console.log(managementData.toArray());
    // const reportStagesControl = new ReportStagesControl<DATA>(chartClasses, tableClasses, undefined, badgeClasses);
    // reportStagesControl.loadWith(new RowContextImpl(props.sd), cd, items);
    // DEBUG HACT ANALYSER

    return <div>
        data loaded - see console, and debug TaskAppBar.tsx
        {/*<MUIDataTable
                columns={["bob"]}
                title={<Typography variant="h6">title</Typography>}
                data={[["Loading..."]]}
                />*/}
    </div>;
}

// http://localhost:3000/test/debug-report/questionnaire/ca1b0cd4-9f63-4ddd-79d5-19af38ff27e0/?startDate=2021-07-01&endDate=2021-09-30
const ChartQuestionnaireDataDebug: FC<{chartUuid: string}> = ({chartUuid}) => { // Base must start and end in / (i.e. /r/welcome/ or / )
    const override = {
        absoluteFromDate: "2021-07-01",
        absoluteToDate: '2021-09-30'
    }
    const {chartData, chartDefinition} = useChartData(chartUuid, override)

    if (!chartData) return null

    // DEBUG QUESTIONNAIRE ANALYSER
    const data = chartData as ReferralAggregateAnalysis
    let questionnaireData: Sequence<QnAnswerWorkWithRefToReferralAggregate> = qnAnswerWorkFromReferralAggregateAnalyser(chartDefinition, Lazy(data.getData())).getData();
    // const reportStagesControl = new ReportStagesControl<DATA>(chartClasses, tableClasses, undefined, badgeClasses);
    // reportStagesControl.loadWith(new RowContextImpl(props.sd), cd, items);
    // DEBUG QUESTIONNAIRE ANALYSER

    // see SequenceAnalysis#getRecordRepresentation
    // which translates into
    //let columnRepresentationsMap = qnAnswerWorkWithRefAggregateColumns
    //const columns = dto.tableRepresentation.columns.map((col) => getFieldRepresentation(qnAnswerWorkWithRefAggregateColumns, col));

    // see TableReportStageControl
    const ctx: RowContext = {
        getSessionData() {return {} as SessionData}
    }
    const fields = ["rid","cid","client","service","project","worker","work date","question","answer"]
    const columns = fields.map((f) => getFieldRepresentation(qnAnswerWorkWithRefAggregateColumns, f));
    const rep = new TableRepresentationBase(columns);

    //let $rowHeaders = TableMuiControl.getRowHeaders(rep, false);
    //let $rowColumns = TableMuiControl.getRowColumns(rep, questionnaireData, null, ctx);
    const rows = questionnaireData
            .map(row => {
                return rep.getColumns()
                    .map((column, columnIndex) => {
                        //let $td = TableControl.represent(row, column, ctx);
                        return column.represent(ctx, row).toString();
                        //$td.click((e) => data.emitClick(index, columnIndex, row));
                    })
            }).toArray()
    const columnsText = columns.map(c => c.getHeading())

    return <div>
        <MUIDataTable
                columns={columnsText}
                title={<Typography variant="h6">title</Typography>}
                data={rows}
                />
    </div>;
}

const ContactsWrapper: FC = () => {
    const {sessionData} = useServicesContext();
    const [sourceType, setSourceType] = useState<SourceType>();
    const [agencyId, setAgencyId] = useState<number>();
    const [professionalId, setProfessionalId] = useState<number>();
    return (<AssociatedContactWizard
            sessionData={sessionData}
            serviceRecipientId={undefined} // use global contacts
            title={"source of referral"}
            showSelf={true}
            sourceType={sourceType}
            agencyId={agencyId}
            professionalId={professionalId}
            individualId={undefined}
            onSourceTypeChange={(sourceType: SourceType) => setSourceType(sourceType)}
            onAgencyProfessionalChange={(agency: Agency, individual: Individual) => {
                setAgencyId(agency?.contactId);
                setProfessionalId(individual?.contactId);
            }}
            onIndividualChange={(individual: Individual) => {}}
    />);
}

// wired up to http://localhost:3000/test/
const TestAppBar: FC = () => {
    return <AppBarContextProvider>
        <CareVisitMenuContextProvider>
            <TestAppBarBase/>
        </CareVisitMenuContextProvider>
    </AppBarContextProvider>
}
const TestAppBarBase: FC = () => {
    let {path} = useRouteMatch();
    path = path == "/" ? "/" : path + "/";
    const history = useHistory();
    const wideMode = path.startsWith("/nav/w/");
    const {sessionData} = useServicesContext();
    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }
    // see useAppBarOptions,
    const userMenu = <UserMenu extraMenuItems={ctx.extraMenuItems} hideUsername={true}/>;
    const guidanceMenu = <GuidanceMenu uuid={"menu-uuid"}/>;
    const qrMenu = <QRScanMenu/>
    const careVisitMenu = <CareVisitMenu/>;
    const menuRight = [careVisitMenu, guidanceMenu, qrMenu, userMenu];
    return <AppBarBase appColour={ctx.appColour} title={"test stuff"}
                       right={menuRight}
                       drawerContent={<MainMenuItems base={path}/>}
                       wideMode={wideMode}
                       onLogoClick={() => history.push("/nav/r/welcome/")}>
        <Switch>
            {/* Also on main menu if enabled */}
            <Route path={`${path}care/:date?`}>
                <CarePage />
            </Route>
            <Route path={`${path}events/:date?`}>
                <EventPage />
            </Route>
            <Route path={`${path}dailyChecks`}>
                <DailyChecksPage />
            </Route>

            {/* webpack only */}
            <Route exact path={`${path}referrals/all`}><TeamReferralsList/></Route>
            <Route exact path={`${path}test-client`}>
                {/* testdomcare c-id 41 */}
                <CommandForm
                    onCancel={() => {}}
                    onFinished={() => {}}>
                    <LoadSRWithEntitiesContext srId={200002}>
                        <ClientDetailEditor serviceRecipientId={200002} taskName={TaskNames.clientWithContact} taskHandle={null} formRef={() => {}} showCode={false}/>
                    </LoadSRWithEntitiesContext>
                </CommandForm>
            </Route>
            <Route exact path={`${path}test-agent`}>
                {/* testdomcare c-id 41 */}
                <LoadSRWithEntitiesContext srId={200002}>
                    <AgencyDetailModal show={true} contextId={200002} />
                </LoadSRWithEntitiesContext>
            </Route>
            <Route exact path={`${path}test-agent-profs`}>
                {/* on test12 use 205866*/}
                <AgencyWithProfessional onChange={(a,p) => {}} serviceRecipientId={205866}/>
            </Route>
            <Route exact path={`${path}test-agent-profs-wizard`}>
                <ContactsWrapper/>
            </Route>
            <Route exact path={`${path}test-tasks`}>
                {/* testdomcare c-id 41 */}
                <LoadSRWithEntitiesContext srId={200002}>
                    <TasksControl srId={200002}/>
                </LoadSRWithEntitiesContext>
            </Route>
            <Route exact path={`${path}test-event`}>
                <CommandForm
                    onCancel={() => {}}
                    onFinished={() => {}}>
                    <CalendarEntryEditor/>
                </CommandForm>
            </Route>
            <Route path={`${path}debug-report/hact/:chartUuid`}>
                {({match}) => <ChartHactDataDebug {...match.params as { chartUuid: string }} />}
            </Route>
            <Route path={`${path}debug-report/questionnaire/:chartUuid`}>
                {({match}) => <ChartQuestionnaireDataDebug {...match.params as { chartUuid: string }} />}
            </Route>
            <Route exact path={`${path}test-tasks-dash`}>
                <TaskTimeline />
            </Route>
            <Route exact path={`${path}test-inbound`}>
                <ReferComponent />
            </Route>
            <Route exact path={`${path}test-config-audit`}>
                <AuditHistory sessionData={sessionData} resource={{links:[{
                        rel: "audit-history",
                        href: `${applicationRootPath}api/config/commands` // TODO: This should be a relation on sessionData resource
                    }]}}/>
            </Route>
            <Route exact path={`${path}test-forward-plan`}>
                {/*Note should give 404 for missing svcRec and instead returns data!
                 see https://test.eccosolutions.co.uk/testdomcare/api/service-recipients/22/evidence/needs/snapshots/latest/ */}
                <ForwardPlanTimeline srId={200298}/>
            </Route>
            {/* webpack only */}

        </Switch>
    </AppBarBase>;
}

export default TestAppBar;