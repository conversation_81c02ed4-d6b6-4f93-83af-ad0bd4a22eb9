import {
    AddressedLocationAjaxRepository,
    AddressHistoryAjaxRepository,
    BuildingAjaxRepository,
    CalendarAjaxRepository,
    ChartAjaxRepository,
    ClientAjaxRepository,
    ContactsAjaxRepository,
    ContractAjaxRepository,
    FormEvidenceAjaxRepository,
    InvoicesAjaxRepository,
    ReferralAjaxRepository,
    RiskEvidenceAjaxRepository,
    SessionData,
    SignatureAjaxRepository,
    SupportWorkAjaxRepository,
    TaskCommandAjaxRepository,
    UserAjaxRepository,
    WorkersAjaxRepository,
    ServiceRecipientAjaxRepository, QuestionnaireWorkAjaxRepository
} from "ecco-dto";
import {
    apiClient,
    EccoAPI,
    hasGlobalEccoAPI,
    setGlobalEccoAPI
} from "ecco-components";
import {CommandAjaxRepository} from "ecco-commands";
import {RotaAjaxRepository} from "ecco-rota";
import {FinanceAjaxRepository} from "ecco-finance";
import {IncidentAjaxRepository} from "ecco-incidents";
import {RepairAjaxRepository} from "ecco-repairs";
import {SessionDataService} from "./feature-config/SessionDataService";
import {getEvidenceEffectsRepository} from "ecco-offline-data";
import {taskIntegrations} from "./workflow/tasklist/TasksControl";
import {defaultPageComponentRegistry} from "./offline/defaultPageComponentRegistry";
import {auditHistoryIntegrations} from "./service-recipients/components/AuditHistory";

export function ifNeededSetDefaultGlobalEccoAPI(sessionData: SessionData) {
    if (!hasGlobalEccoAPI()) {
        const supportWorkRepository = new SupportWorkAjaxRepository(apiClient);
        const eccoAPI: EccoAPI = {
            baseURI: "TODO: it's for /offline|online/ in environment.ts",
            apiClient: apiClient,
            pageComponentRegistry: defaultPageComponentRegistry,
            auditHistoryIntegrations: auditHistoryIntegrations,
            taskIntegrations,
            getAddressRepository: () => new AddressedLocationAjaxRepository(apiClient),
            addressHistoryRepository: new AddressHistoryAjaxRepository(apiClient),
            getBuildingRepository: () => new BuildingAjaxRepository(apiClient),
            calendarRepository: new CalendarAjaxRepository(apiClient),
            chartRepository: new ChartAjaxRepository(apiClient),
            clientRepository: new ClientAjaxRepository(apiClient),
            getCommandRepository: () => new CommandAjaxRepository(apiClient),
            contactsRepository: new ContactsAjaxRepository(apiClient),
            contractRepository: new ContractAjaxRepository(apiClient),
            getEvidenceEffectsRepository: getEvidenceEffectsRepository,
            financeRepository: new FinanceAjaxRepository(apiClient),
            formEvidenceRepository: new FormEvidenceAjaxRepository(apiClient),
            incidentsRepository: new IncidentAjaxRepository(apiClient),
            repairsRepository: new RepairAjaxRepository(apiClient),
            invoicesRepository: new InvoicesAjaxRepository(apiClient),
            referralRepository: () => new ReferralAjaxRepository(apiClient),
            riskWorkRepository: new RiskEvidenceAjaxRepository(apiClient),
            rotaRepository: new RotaAjaxRepository(apiClient, SessionDataService.getFeatures),
            getSignatureRepository: () => new SignatureAjaxRepository(apiClient),
            supportWorkRepository,
            supportSmartStepsSnapshotRepository: supportWorkRepository,
            questionnaireSnapshotRepository: new QuestionnaireWorkAjaxRepository(apiClient),
            tasksRepository: new TaskCommandAjaxRepository(apiClient),
            userRepository: new UserAjaxRepository(apiClient),
            workersRepository: new WorkersAjaxRepository(apiClient),
            serviceRecipientRepository: new ServiceRecipientAjaxRepository(apiClient),
            sessionData: sessionData
        };

        // eccoAPI.sessionData = sessionData;
        setGlobalEccoAPI(eccoAPI);
    }
}