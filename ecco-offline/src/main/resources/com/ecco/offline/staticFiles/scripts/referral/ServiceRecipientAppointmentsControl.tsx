import $ = require("jquery");
import services = require("ecco-offline-data");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import {onParentTabActivated} from "../common/tabEvents";
import {EventResourceDto} from "ecco-dto/calendar-dto";
import {EventCardWrapper} from "../components/EventCard";
import {SessionData} from "ecco-dto";
import {EccoDateTime} from "@eccosolutions/ecco-common";
import * as React from "react";
import {CalendarEntryEditor} from "ecco-calendar";
import {showInCommandForm} from "../components/CommandForm";
import {CalendarEvent} from "ecco-calendar";

function compareStrings(a: string, b: string): number {
    if (a < b) return -1;
    if (a > b) return 1;
    return 0;
}

class BackingData {
    constructor(public srId: number, public calendarEvents: EventResourceDto[], public sessionData: SessionData) {}
}

/**
 * LOAD FORM - non-jsx (modal if no second arg)
 * This top level function instigates the commandform (in showInCommandForm), the session data and srId
 */
export function serviceRecipientCalendarEntry(
        serviceRecipientId: number,
        eventUuid?: string | undefined,
        onCompleted?: (() => void) | undefined,
        newEntryDateTime?: EccoDateTime | undefined,
        newEntryAllDay?: boolean | undefined
) {
    showInCommandForm(<CalendarEntryEditor
            serviceRecipientId={serviceRecipientId}
            eventUuid={eventUuid}
            newEntryDateTime={newEntryDateTime}
            newEntryAllDay={newEntryAllDay}
    />, () => {CalendarEvent.fire()});
}

/**
 * LOAD FORM - non-jsx (modal if no second arg)
 * This top level function instigates the commandform (in showInCommandForm), the session data and srId
 */
export function contactCalendarEntry(
        contactId: number,
        eventUuid?: string | undefined,
        onCompleted?: (() => void) | undefined,
        newEntryDateTime?: EccoDateTime | undefined,
        newEntryAllDay?: boolean | undefined
) {
    showInCommandForm(
        <CalendarEntryEditor
            contactId={contactId}
            eventUuid={eventUuid}
            newEntryDateTime={newEntryDateTime}
            newEntryAllDay={newEntryAllDay}
        />);
}

/** Renders a simple list of the nearby events that are in Referral.calendarEvents
 * NB This is like EventPage - and these two concerns really ought to be unified */
export class CalendarAppointmentsControl extends BaseAsyncDataControl<BackingData> {

    constructor(
        private readonly srId: number,
        private readonly calendarId?: string | undefined,
        private readonly viewingAsName?: string | undefined
    ) {
        super();
        // we are handling it, but we don't fire it...
        // appointments are edited in serviceRecipientCalendarEntry (used by legacy and new)
        // which uses commandForm.submit which fires on form onFinished or form.addPostSubmitHandler
        // but commandQueueFlushedEventBus fires on the inner command queue when all commands are saved
        // (where ReloadEvent used to fire, but this reloads the world)
        // ideally we load specific events when it matters using a promise hook which also provides a reload we can call
        // so we should be looking for the calendar event to reload, not on any command
        // see also 'Reload user after change to ACLs'
        // NB no removeHandler, so its assuming a page refresh to clear up - ideally we useEventHandler
        CalendarEvent.addHandler(() => this.load());
    }

    protected override afterAttach() {
        onParentTabActivated(this.element(), () => {
            this.load();
        });
    }

    protected fetchViewData(): Promise<BackingData> {
        // allow calendarId to be undefined
        const dataFactory = (calendarId: string) => services.getCalendarRepository().nearby(calendarId)
                .then(events => services.getFeatureConfigRepository().getSessionData()
                        .then(sd => new BackingData(this.srId, events, sd)));
        return !this.calendarId
            ? services.getServiceRecipientRepository().findOneServiceRecipientById(this.srId).then(r => dataFactory(r.calendarId))
            : dataFactory(this.calendarId);
    }
    private static renderEvents(sessionData: SessionData, events: EventResourceDto[], $container: $.JQuery, viewingAsName?: string | undefined) {
        const $elm = $("<div>");
        EventCardWrapper.enhance($elm, events, sessionData, viewingAsName);
        $container.append($elm);
    }

    protected render(data: BackingData): void {

        this.element().empty();

        const $calPopup = $("<a>").text("new appointment");
        $calPopup.click(() => serviceRecipientCalendarEntry(data.srId));
        this.element()
            .append($("<div>").addClass("text-center")
                .append($calPopup)
            );

        const $el = $("<ul>").addClass("entry-list list-unstyled");
        if (!data) {
            $el.append($("<li>").text("no referral found"));
        } else if (!data.calendarEvents || data.calendarEvents.length == 0) {
            $el.append($("<li>").text("no nearby calendar events found"));
        } else {
            const $cards = $("<div>").addClass("cards-container");
            $el.append($cards);
            const events = data.calendarEvents
                .sort((a: EventResourceDto, b: EventResourceDto) => compareStrings(a.start, b.start));
            CalendarAppointmentsControl.renderEvents(data.sessionData, events, $cards, this.viewingAsName);
        }
        this.element().append($el);
    }
}

export default CalendarAppointmentsControl;
