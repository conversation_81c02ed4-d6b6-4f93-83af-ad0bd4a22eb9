import * as React from "react";
import {FC} from "react";
import {lazyControlWrapper} from "../../../components/ControlWrapper";
import {useParams} from "react-router";


export const SupportHistory: FC<{taskName: string, srId?: number | undefined}> = props => {
    let params = useParams<{srId: string}>();
    const srId = props.srId ?? parseInt(params.srId)

    const Component = lazyControlWrapper(
        () => import("../SupportHistoryListControl"),
        // @ts-ignore - because typescript
        srId, props.taskName
    );
    return <div style={{margin: "0px -24px -12px"}}>
      <Component/>
    </div>
};
