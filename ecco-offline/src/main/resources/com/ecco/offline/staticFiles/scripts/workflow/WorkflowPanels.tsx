import {FC} from "react";
import {<PERSON>, CardContent, Grid, Typography, Divider} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {
    handleLazy,
    TasksControlNG,
    LoadSRWithEntitiesContext,
    useCurrentServiceRecipientWithEntities
} from "ecco-components";
import {lazyControlWrapper} from "../components/ControlWrapper";
import {useParams} from "react-router";
import {ProgressLineChartSmartSteps} from "ecco-evidence";
import {TaskNames} from "ecco-dto";
import {StringUtils} from "@eccosolutions/ecco-common";

// as copied from offline/router.tsx
export const Workflow = (props: {srId: number; limitToTaskNames?: string[] | undefined}) => {
    const initialActiveTaskName = useParams<{taskName: string}>().taskName;
    return <LoadSRWithEntitiesContext srId={props.srId}>
        {
            handleLazy(<TasksControlNG srId={props.srId} limitToTaskNames={props.limitToTaskNames} initialActiveTaskName={initialActiveTaskName}/>)
        }
    </LoadSRWithEntitiesContext>;
};

const WorkflowPanels: FC<{srId: number}> = (props) => {
    return <LoadSRWithEntitiesContext srId={props.srId}>
        <WorkflowPanelsWithSRE srId={props.srId}/>
    </LoadSRWithEntitiesContext>
}

const PanelTitle: FC<{label: string}> = ({label}) => {
    if (!label) {
        return null;
    }
    return (<>
            <Typography align={"center"} color="textSecondary" gutterBottom variant="body2">
                {label}
            </Typography>
            <Divider variant="middle" />
        </>);
}

// panels in the main page
const WorkflowPanelsWithSRE: FC<{srId: number}> = (props) => {
    const {resolved: serviceRecipientWE} = useCurrentServiceRecipientWithEntities()
    if (!serviceRecipientWE) {
        return <></>;
    }
    const serviceRecipient = serviceRecipientWE.serviceRecipient;
    const sessionData = serviceRecipient.features;
    const serviceType = serviceRecipientWE.serviceType // see loadSrWithEntitiesPromise

    let panelItemNames = "aboutMe,support,health,risk";
    let panelItemNamesCfg = serviceType.getTaskDefinitionSetting(TaskNames.referralView,"panelOrder");
    if (sessionData.hasRoleFileLimiting()) {
        panelItemNamesCfg = serviceType.getTaskDefinitionSetting(TaskNames.referralView,"limitedPanelOrder");
    }
    const panelItemNamesArr = StringUtils.csvToArray(panelItemNamesCfg || panelItemNames);
    let showAboutMe = panelItemNamesArr.filter(n => n === "aboutMe").length > 0;
    let showHealth = panelItemNamesArr.filter(n => n === "health").length > 0;
    let showSupport = panelItemNamesArr.filter(n => n === "support").length > 0;
    let showRisk = panelItemNamesArr.filter(n => n === "risk").length > 0;

    const aboutMeTaskNames = sessionData.getLimitedTaskNames('showOnAboutMeTab', serviceType);
    const healthTaskNames = sessionData.getLimitedTaskNames('showOnHealthTab', serviceType);
    const disableRadarSupport = serviceType.taskDefinitionSettingHasFlag("referralView", "supportTabComponents", "!radarChart");
    const scoreChartSupport = serviceType.taskDefinitionSettingHasFlag("referralView", "supportTabComponents", "scoreChart");
    const disableRadarRisk = serviceType.taskDefinitionSettingHasFlag("referralView", "riskTabComponents", "!radarChart");
    const scoreChartRisk = serviceType.taskDefinitionSettingHasFlag("referralView", "riskTabComponents", "scoreChart");
    const supportTaskNames = sessionData.getLimitedTaskNames('showOnSupportTab', serviceType);
    const riskTaskNames = sessionData.getLimitedTaskNames('showOnRiskTab', serviceType);
    const aboutMeLabel = serviceType.getTaskDefinitionSetting("referralView", "aboutMeTabLabel");
    const supportLabel = serviceType.getTaskDefinitionSetting("referralView", "supportTabLabel");
    const riskLabel = serviceType.getTaskDefinitionSetting("referralView", "riskTabLabel");
    const healthLabel = serviceType.getTaskDefinitionSetting("referralView", "healthTabLabel");

    return <>
        <Grid container>

        <Grid item xs={12} md={6}>
            {showAboutMe && aboutMeTaskNames.length > 0 && <Grid item xs={12} style={{paddingTop: "8px"}}>
                <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
                    <PanelTitle label={aboutMeLabel}/>
                    <CardContent>
                        {handleLazy(<TasksControlNG srId={props.srId} limitToTaskNames={aboutMeTaskNames}/>)}
                    </CardContent>
                </Card>
            </Grid>}
            {showSupport && supportTaskNames.length > 0 && <Grid item xs={12} style={{paddingTop: "8px"}}>
                <WorkflowSupport label={supportLabel} srId={props.srId} supportTaskNames={supportTaskNames}
                                 disableRadarSupport={disableRadarSupport} scoreChart={scoreChartSupport}/>
            </Grid>}
        </Grid>
        <Grid item xs={12} md={6}>
            {showHealth && healthTaskNames.length > 0 && <Grid item xs={12} style={{paddingTop: "8px"}}>
                <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
                    <PanelTitle label={healthLabel}/>
                    <CardContent>
                        {handleLazy(<TasksControlNG srId={props.srId} limitToTaskNames={healthTaskNames}/>)}
                    </CardContent>
                </Card>
            </Grid>}
            {showRisk && riskTaskNames.length > 0 && <Grid item xs={12} style={{paddingTop: "8px"}}>
                <WorkflowRisk label={riskLabel} riskTaskNames={riskTaskNames} disableRadarRisk={disableRadarRisk} scoreChart={scoreChartRisk}/>
            </Grid>}
        </Grid>
        </Grid>
    </>
}

const SpidergraphSupportWrapper: FC<{srId: number}> = ({srId}) => {
    // @ts-ignore
    //const CMemo = useMemo(() => lazyControlWrapper(() => import("../evidence/spider/SupportRadarChart"), srId), [srId]);
    const CMemo = lazyControlWrapper(() => import("../evidence/spider/SupportRadarChart"), srId);
    return <CMemo/>;
}

const ProgressScoreSupportWrapper: FC<{srId: number}> = ({srId}) => {
    const {resolved: serviceRecipientWE} = useCurrentServiceRecipientWithEntities()
    if (!serviceRecipientWE) {
        return <></>;
    }

    const taskName = serviceRecipientWE.serviceType.getFirstSupportTaskName(serviceRecipientWE.serviceRecipient.features);
    const scoreListDef = serviceRecipientWE.serviceType.getTaskDefinitionSetting(taskName, "scoreListName");

    // @ts-ignore
    //const CMemo = lazyControlWrapper(() => import("ecco-evidence").then(i => ({default: i.ProgressLineChartSmartSteps})), srId);
    //return <CMemo/>;
    return <ProgressLineChartSmartSteps srId={srId} scoreListDef={scoreListDef} evidenceGroup={"needs"}/>
}

const ProgressScoreRiskWrapper: FC<{srId: number}> = ({srId}) => {
    const {resolved: serviceRecipientWE} = useCurrentServiceRecipientWithEntities()
    if (!serviceRecipientWE) {
        return <></>;
    }

    const taskName = serviceRecipientWE.serviceType.getFirstRiskTaskName(serviceRecipientWE.serviceRecipient.features);
    const scoreListDef = serviceRecipientWE.serviceType.getTaskDefinitionSetting(taskName, "scoreListName");

    // @ts-ignore
    //const CMemo = lazyControlWrapper(() => import("ecco-evidence").then(i => ({default: i.ProgressLineChartSmartSteps})), srId);
    //return <CMemo/>;
    return <ProgressLineChartSmartSteps srId={srId} scoreListDef={scoreListDef} evidenceGroup={"threat"}/>
}

const WorkflowSupport: FC<{label: string, srId: number, supportTaskNames: string[], disableRadarSupport: boolean, scoreChart: boolean}>
        = (props: {label: string, srId: number, supportTaskNames: string[], disableRadarSupport: boolean, scoreChart: boolean}) => {

    return <Card elevation={2} style={{backgroundColor: "rgba(0,0,0,0.02)"}}>
            <PanelTitle label={props.label}/>
            <CardContent>
                {handleLazy(<TasksControlNG srId={props.srId} limitToTaskNames={props.supportTaskNames}/>)}
                {!props.disableRadarSupport && <SpidergraphSupportWrapper srId={props.srId}/>}
                {props.scoreChart && <ProgressScoreSupportWrapper srId={props.srId}/>}
            </CardContent>
        </Card>;
};


const WorkflowRisk: FC<{label: string, riskTaskNames: string[],  disableRadarRisk: boolean, scoreChart: boolean}>
        = ({label, riskTaskNames,  disableRadarRisk, scoreChart}) => {

    const {resolved: serviceRecipientWE} = useCurrentServiceRecipientWithEntities();

    return <Card elevation={2}>
            <PanelTitle label={label}/>
            <CardContent>
                {handleLazy(<TasksControlNG srId={serviceRecipientWE.serviceRecipient.serviceRecipientId} limitToTaskNames={riskTaskNames}/>)}
                {!disableRadarRisk && <SpidergraphRiskWrapper/>}
                {scoreChart && <ProgressScoreRiskWrapper srId={serviceRecipientWE.serviceRecipient.serviceRecipientId}/>}
            </CardContent>
        </Card>;
};

const SpidergraphRiskWrapper: FC<{}> = () => {
    const {resolved: serviceRecipientWE} = useCurrentServiceRecipientWithEntities();
    const serviceRecipient = serviceRecipientWE.serviceRecipient;
    const srId= serviceRecipient.serviceRecipientId;
    const sessionData = serviceRecipientWE.serviceRecipient.features;
    const st = sessionData.getServiceTypeByServiceCategorisationId(serviceRecipient.serviceAllocationId);
    const taskName = st.getFirstRiskTaskName(sessionData);
    const riskMatrix = st.taskDefinitionSettingHasFlag(taskName, "showActionComponents", "riskMatrix");
    // @ts-ignore
    const RAG = lazyControlWrapper(() => import("../evidence/spider/RedAmberGreenRiskChart"), srId);
    // @ts-ignore
    const MATRIX = lazyControlWrapper(() => import("../evidence/spider/MatrixRiskChart"), srId);

    // @ts-ignore
    return riskMatrix
            ? <MATRIX/>
            : <RAG/>;
};

export default WorkflowPanels;