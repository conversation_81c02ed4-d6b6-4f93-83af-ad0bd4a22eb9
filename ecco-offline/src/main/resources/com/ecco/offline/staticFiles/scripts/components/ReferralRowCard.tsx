import {EccoDate} from "@eccosolutions/ecco-common";

import {Card, CardActions, CardHeader} from "@eccosolutions/ecco-mui";
import {getGlobalEccoAPI, NavAnchor} from "ecco-components";
import {isOffline, SessionData} from "ecco-dto";
import {ReferralsListRow} from "ecco-dto/referral-dto";
import * as React from "react";

import {baseURI} from "../environment";


const isOnLegacyPage = window.location.href.indexOf("/secure") > 0;
const isOnTasksPage = window.location.href.indexOf("/tasks") > 0;

// also see ClientReferralsPopup#openReferral
function referralUrl(referral: ReferralsListRow) {
    if (isOnLegacyPage) {
        return referral.links.filter(link => link.rel = 'self')[0].href;
    }
    return `/referrals/${referral.serviceRecipientId}/`;
}

interface CardProps {
    showDate?: boolean | undefined;
    referral: ReferralsListRow;
}

interface State { // Seriously?? This is actually a stateless component
    dueTaskName: string;
    sessionData: SessionData;
}

class ReferralRowCard extends React.Component<CardProps, State> {

    private formattedDueDate: string;
    constructor(props) {
        super(props);
        const {sessionData} = getGlobalEccoAPI()
        const dueTaskName = sessionData.getTaskDefinitionById(this.props.referral.nextDueTaskId).name;
        this.state = {dueTaskName, sessionData};

        this.formattedDueDate = EccoDate.iso8601ToFormatShort(this.props.referral.nextDueSlaDate);
    }

    private getSubTitle() {
        let msg = `${this.state.sessionData.getMessages()[`referralView.${this.state.dueTaskName}`]} due ${this.formattedDueDate}`;
        if (this.props.referral.supportWorkerDisplayName) {
            msg = `${msg} (${this.props.referral.supportWorkerDisplayName})`;
        }
        return msg;
    }
    override render() {
        const {referral} = this.props;
        const url = referralUrl(referral);
        return(
                <Card style={{marginTop: 2, marginBottom: 2}}>
                    <CardHeader
                        title={referral.clientDisplayName}
                        subheader={this.getSubTitle()}
                    />
                    <CardActions>
                        {/*on /tasks we can open referral with 'self' but it seems to prefix and so duplicate the /context in the href*/}
                        {/*and a router style url isn't registered, so for now open the online referral, which is what we want anyway*/}
                        {!isOffline() && isOnTasksPage
                            ? <a key={referral.serviceRecipientId}
                                 className="btn btn-link"
                                 /*also see ClientReferralsPopup#openReferral*/
                                 href={`${baseURI}referrals/${referral.referralId}/`}
                                 target={"_blank"}
                                >open referral</a>
                            : <NavAnchor
                                key={referral.serviceRecipientId}
                                className="btn btn-link"
                                url={url}
                                >open referral</NavAnchor>
                        }
                    </CardActions>
                </Card>
        );
    }
}
export default ReferralRowCard;
