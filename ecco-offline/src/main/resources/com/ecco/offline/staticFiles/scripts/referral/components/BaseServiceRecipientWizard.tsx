import {Nav} from "react-bootstrap";
import {SessionData, TaskNames, ServiceDto} from "ecco-dto";
import {Component, ReactEventHandler} from 'react';

export type Step = "service" | "project"| "region"| "client" | "staff" | "referral" | "referralDetails" | "source" | "protection" | "consent" | "autoStart" | "complete" | "close";

interface Errors {
    [property: string]: string
}

export type FileTypeName = "referral" | "job" | "building";

export interface SRProps {
    sessionData: SessionData;
    allowInboundServices: boolean;
    subFormsAsModal: boolean;

    /** Type we're creating.  Also used as text to use to describe the file type */
    fileTypeName: FileTypeName

    /** Specify this if you want to pre-select which service they are being added to */
    serviceId?: number | undefined;

    /** clientId provided */
    clientId: number;
}

export interface SRState {
    errors: Errors;
    steps: Step[];
    stepSelected: Step;
    status?: string | undefined;
    dataProtSig?: string | undefined;
}

export class BaseServiceRecipientWizard<P extends SRProps, S extends SRState> extends Component<P, S> {

    constructor(props: P) {
        super(props);
    }

    protected handleStepClick = ((step: Step) => {
        this.setState({stepSelected: step});
    }) as any as ReactEventHandler<Nav>;

    protected isStepValid(errors?: Errors | undefined): boolean {
        errors = errors || this.state.errors;

        switch (this.state.stepSelected) {
            case 'service':
                return !errors.serviceIdTransient;
            case 'client':
                return !errors.clientId;
            case 'source':
                return !errors.referrerAgencyId && !errors.referrerIndividualId;
        }
        return true
    }

    protected hasNext(): boolean {
        const stepIndex = this.state.steps.indexOf(this.state.stepSelected);
        return stepIndex < this.state.steps.length - 1;
    }

    protected nextStep(steps?: Step[] | undefined, selected?: Step | undefined): Step {
        steps = steps || this.state.steps;
        selected = selected || this.state.stepSelected;
        const stepIndex = steps.indexOf(selected);
        return steps[stepIndex + 1];
    }

    protected isEnabledStep(step: Step): boolean {
        const stepSelectedIndex = this.state.steps.indexOf(this.state.stepSelected);
        const stepIndex = this.state.steps.indexOf(step);
        return stepIndex <= stepSelectedIndex;
    }

    protected buildSteps(service?: ServiceDto | undefined): Step[] {
        const serviceId = service?.id ?? this.props.serviceId;
        // the config on any of them on the service will be the same
        const serviceType = this.props.sessionData.getServiceTypeByServiceIdHack(serviceId);

        // have a default wizard as the real one loads and when the user has chosen a service
        let steps: Step[] = service && service.id == -200
            ? ['staff', 'complete']
            : ['service', 'source', 'complete'];


        if (serviceType) {
            steps = serviceType.getWizardTasks().map((task: string) => {
                let step: Step;
                switch (task) {
                    case TaskNames.project: // FALLTHRU
                    case TaskNames.projectRegion:
                        const projects = this.props.sessionData.getServiceCategorisationProjects(service.id, true);
                        if (projects.length > 0) {
                            step = 'project';
                        }
                        break;
                    case TaskNames.sourceWithIndividual: // FALLTHRU
                    case TaskNames.source: // from
                        step = 'source';
                        break;
                    case TaskNames.referralDetails:
                        step = 'referralDetails';
                        break;
                    case TaskNames.dataProtection:
                        step = 'protection';
                        break;
                    case TaskNames.autoStart:
                        step = 'autoStart';
                        break;
                }
                return step;
            }).filter((step: string) => (step != null));

            // 'bookend' the new list with service and complete
            steps.splice(0, 0, 'service');
            steps.push('complete');

            // push region before project (if projectRegion configured)
            for (let i = 0; i < serviceType.getTaskDefinitionEntries().length; i++) {
                const entry = serviceType.getTaskDefinitionEntries()[i];
                if (entry.getName() == TaskNames.projectRegion) {
                    steps.splice(steps.indexOf('project'), 0, 'region');
                    break;
                }
            }
        }

        return steps;
    } // ReactBootstrap does actually do (eventKey: string|number) => void but typedefs are screwed
}