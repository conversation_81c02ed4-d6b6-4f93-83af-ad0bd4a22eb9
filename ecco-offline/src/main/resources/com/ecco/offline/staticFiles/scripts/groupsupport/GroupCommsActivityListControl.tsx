import $ = require("jquery");
import GroupActivityListControl, {GroupRowControl} from "./GroupActivityListControl";
import {ClientAttendanceDto, GroupActivityDto, ResourceList, SessionData} from "ecco-dto";
import {GroupActivityCommand, GroupActivityInvitationCommand} from "./commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {StringToObjectMap} from "@eccosolutions/ecco-common";
import {ClientAttendance, GroupActivity, GroupSupportAjaxRepository} from "ecco-dto";
import {apiClient} from "ecco-components";
import moment = require("moment");

const repository = new GroupSupportAjaxRepository(apiClient);

class GroupCommsActivityListControl extends GroupActivityListControl {

    protected findActivities(page: number): Promise<ResourceList<GroupActivityDto>> {
        return repository.findCommsActivities(page, this.serviceId)
    }

    protected createRowControl(activity: GroupActivityDto): GroupRowControl {
        return new CommsRowControl(activity, this.sessionData, () => this.update(this.groupPageType, this.activityTypeId, this.venueId, this.serviceId, this.page));
    }

    protected getHeaders() { // OVERRIDES default impl
        const headings = ["date", "end", "activity"];
        headings.push("service");
        headings.push("project");
        headings.push("sent");
        if (this.sessionData.hasRoleAdmin()) {
            headings.push("-");
        }
        return headings;
    }
}

class CommsRowControl extends GroupRowControl {

    constructor(activity: GroupActivityDto, sessionData: SessionData, reload: () => void) {
        super(null, activity, sessionData, reload);
    }

    protected findClientsByActivityId(activityId: number): Promise<ClientAttendanceDto[]> {
        return repository.findInvitedClientsByActivityId(activityId);
    }
    protected findOneActivityByUuid(activityUuid: string): Promise<GroupActivity> {
        return repository.findOneActivityByUuid(activityUuid);
    }

    protected copyActivityCmd(activityNewUuid: Uuid): GroupActivityCommand {
        return new GroupActivityCommand("create", activityNewUuid, GroupActivityCommand.DISCRIMINATOR_COMMS)
    }

    protected copyActivityDetailCmds(activityNew: GroupActivity, attendees: ClientAttendance[]) {
        return attendees.map(a => {
            return new GroupActivityInvitationCommand("update",
                    activityNew.getDto().id, Uuid.parse(activityNew.getDto().uuid), a.getDto().referralSummary.referralId)
                    .changeInvited(null, a.getDto().invited)
                    .changeAttending(null, a.getDto().attending);
            //cmd.changeAttended(rowData.clientAttendance.attended, rowData.attended);
            //cmd.changeEvidenceNotes(rowData.clientAttendance.comment, rowData.evidenceNotes);
            //cmd.changeEvidenceType(rowData.clientAttendance.typeId, rowData.typeId);
        });
    }

    protected getColumnMapping(): StringToObjectMap<(dto: GroupActivityDto) => string|$.JQuery> {
        return {
            "activity": item => this.sessionData.getListDefinitionEntryById(item.activityTypeId)?.getDisplayName(),
            "service": item => item.serviceId ? this.sessionData.getServiceName(item.serviceId) : "-",
            "project": item => item.projectId ? this.sessionData.getProjectName(item.projectId) : "-",
            "sent": item => item.clientsAttending ? item.clientsAttending.toString() : "0",
            "date": item => {
                const label = moment(item.startDateTime).format("DD MMMM YYYY HH:mm");
                return $("<a>").attr("href", item.id + "/").text(label);
            },
            "-": item => this.adminButtonsFor(item)
        };
    }

}

export default GroupCommsActivityListControl;
