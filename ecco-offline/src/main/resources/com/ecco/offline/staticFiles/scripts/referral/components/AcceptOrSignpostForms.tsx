import * as React from "react"
import {ReactChild} from "react"
import {EccoDate, EccoDateTime, IdNameDisabled, StringUtils} from "@eccosolutions/ecco-common";
import {
    BaseAcceptCommand,
    CommandQueue,
    ReferralTaskAcceptOrSignpostCommand,
    ReferralTaskAppropriateReferralCommand
} from "ecco-commands";

import {
    apiClient,
    CommandSubform,
    CommandSubformProps,
    useCommandForm,
    useCurrentServiceRecipientWithEntities,
    ServiceRecipientWithEntitiesContext,
    usePromise,
    withMessages
} from "ecco-components";
import {button,
    checkBox,
    datePickerIso8601Input,
    dropdownList,
    link,
    possiblyModalForm,
    textArea} from "ecco-components-core";
import {
    AcceptedState, ConfigResolver,
    ContactsAjaxRepository, PrefixType,
    SessionData, SIGNPOSTREASON_LISTNAME,
    TaskNames
} from "ecco-dto";
import {Agency} from "ecco-dto/contact-dto";
import {getReferralRepository} from "ecco-offline-data";

import {TaskWithTitle} from "ecco-dto/workflow-dto";
import {Box, Grid} from '@eccosolutions/ecco-mui';
import AgencyDetailModal from "../../contacts/components/AgencyDetailModal";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import {SignpostedIdFields} from "ecco-dto/referral-dto";

const repository = new ContactsAjaxRepository(apiClient);

function sortName(a: IdNameDisabled, b: IdNameDisabled) {
    return (a.name || '').localeCompare(b.name);
}

function useSignpostedComment(props: {context: ServiceRecipientWithEntitiesContext}) {
    let commentQ: Promise<string>;
    switch (props.context.serviceRecipient.prefix) {
        case "r":
            commentQ = props.context.referral.signpostedCommentId
                    ? getReferralRepository().findOneReferralByServiceRecipientId(props.context.referral.serviceRecipientId)
                            .then(r => r.signpostedComment)
                    : Promise.resolve(null);
            break;
        case "i":
            commentQ = Promise.resolve(props.context.incident.signpostedExitComment);
            break;
        case "m":
            commentQ = Promise.resolve(props.context.repair.signpostedExitComment);
            break;
    }

    const output = usePromise(() => commentQ,
    []
    );

    return output;
}

export function AppropriateReferralDialog({task}: { task: TaskWithTitle }) {
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities()
    const form = useCommandForm()
    const {resolved: comment, loading} = useSignpostedComment({context})

    if (loading) {
        return <div></div>;
    }

    let dtoConverted: CommonDetailsDto = context.referral;

    return context.referral
        ? <AcceptOrSignpostForm
                dto={dtoConverted}
                taskHandle={task.taskHandle}
                taskName={TaskNames.referralAccepted}
                afterSave={reload}
                title={task.title}
                cmdFactory={ReferralTaskAppropriateReferralCommand}
                initialAcceptedDate={context.referral.decisionReferralMadeOn}
                initialAcceptedState={context.referral.appropriateReferralState}
                signpostedComment={comment}
                commandForm={form}
            />
        : <p>Tasks config error: appropriate referral is only valid on referral tasks currently</p>
}

export function AcceptOnServiceDialog({task}: {task: TaskWithTitle}) {
    const {resolved: context, reload} = useCurrentServiceRecipientWithEntities()
    const form = useCommandForm()
    const {resolved: comment, loading} = useSignpostedComment({context})

    if (loading) {
        return <div></div>;
    }

    let dtoConverted: CommonDetailsDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoConverted = {
                prefix: context.serviceRecipient.prefix,
                serviceRecipientId: context.serviceRecipient.serviceRecipientId,
                configResolver: context.serviceRecipient.configResolver,
                features: context.serviceRecipient.features,
                decisionMadeOn: context.incident.decisionMadeOn,
                acceptOnServiceState: context.incident.acceptOnServiceState,
                signpostedCommentId: context.incident.signpostedCommentId,
                signpostedReasonId: context.incident.signpostedReasonId,
                signpostedAgencyId: context.incident.signpostedAgencyId,
                signpostedBack: context.incident.signpostedBack
            }
            break;
        case "m":
            dtoConverted = {
                prefix: context.serviceRecipient.prefix,
                serviceRecipientId: context.serviceRecipient.serviceRecipientId,
                configResolver: context.serviceRecipient.configResolver,
                features: context.serviceRecipient.features,
                decisionMadeOn: context.repair.decisionMadeOn,
                acceptOnServiceState: context.repair.acceptOnServiceState,
                signpostedCommentId: context.repair.signpostedCommentId,
                signpostedReasonId: context.repair.signpostedReasonId,
                signpostedAgencyId: context.repair.signpostedAgencyId,
                signpostedBack: context.repair.signpostedBack
            }
            break;

        default:
            dtoConverted = context.referral
    }

    return <AcceptOrSignpostForm
                dto={dtoConverted}
                taskHandle={task.taskHandle}
                taskName={TaskNames.decideFinal}
                afterSave={reload}
                title={task.title}
                cmdFactory={ReferralTaskAcceptOrSignpostCommand}
                initialAcceptedDate={dtoConverted.decisionMadeOn}
                initialAcceptedState={dtoConverted.acceptOnServiceState}
                signpostedComment={comment}
                commandForm={form}
            />
}

interface CommonDetailsDto extends SignpostedIdFields {
    decisionMadeOn?: string;
    acceptOnServiceState: AcceptedState;

    //referralId: number
    prefix: PrefixType;
    serviceRecipientId: number;
    configResolver: ConfigResolver;
    features: SessionData;
}

interface Props extends CommandSubformProps {
    dto: CommonDetailsDto;
    signpostedComment?: string;
    taskName: string;
    taskHandle: string;
    title: string;
    afterSave: () => void
    cmdFactory: new(serviceRecipientId: number, taskName: string, taskHandle: string) => BaseAcceptCommand;
    initialAcceptedDate?: string | undefined;
    initialAcceptedState: AcceptedState;
}

interface State {
    autoSaving: boolean,
    acceptedDate: string;
    acceptedState: AcceptedState,
    agencies: IdNameDisabled[] | null,
    agencyDialog: boolean;
    agencyEditing: boolean;
    signpostedBack: boolean,
    signpostedComment: string,
    signpostedReasonId?: number | undefined,
    signpostedAgencyId?: number | undefined,
    readOnly: boolean
}

export class AcceptOrSignpostForm extends CommandSubform<Props, State> {

    constructor(props) {
        super(props);

        // NB decisionMadeOn/ decisionReferralMadeOn is a system (utc) datetime - see PredicateSupport.isSystemField
        // BUT it gets translated by view models into a local date
        // The display audits of a date should be local timezone (else it will look off by a day), so we then convert to UTC at the server.
        const acceptedDateLocal = this.props.initialAcceptedDate || EccoDateTime.nowLocalTime().formatIso8601()

        this.state = {
            autoSaving: false,
            acceptedDate: acceptedDateLocal,
            acceptedState: this.props.initialAcceptedState,
            signpostedBack: this.props.dto.signpostedBack,
            agencies: null,
            agencyDialog: false,
            agencyEditing: false,
            signpostedComment: this.props.signpostedComment,
            signpostedReasonId: this.props.dto.signpostedReasonId,
            signpostedAgencyId: this.props.dto.signpostedAgencyId,
            readOnly: this.props.initialAcceptedState != "UNSET"
        };

        this.findAgencies().then((agencies: IdNameDisabled[]) => {
            this.setState({
                agencies: agencies.sort(sortName),
            });
        })
    }

    private findAgencies() {
        return repository.findAllAgencies(this.props.dto.serviceRecipientId).then((agencies:Agency[]) => {
            return agencies.map(agency => ({
                id: agency.contactId,
                name: agency.companyName,
                disabled: false
            } as IdNameDisabled));
        })
    }

    public emitChangesTo(commandQueue: CommandQueue) {
        const incomingAcceptedDateLocal = this.props.initialAcceptedDate ? EccoDate.parseIso8601(this.props.initialAcceptedDate) : null;
        const acceptedDateLocal = EccoDate.parseIso8601(this.state.acceptedDate);

        const cmd = new this.props.cmdFactory(this.props.dto.serviceRecipientId, this.props.taskName, this.props.taskHandle)
            .changeAcceptedState(this.props.initialAcceptedState, this.state.acceptedState)
            // acceptDate is decision state date
            .changeAcceptedDate(incomingAcceptedDateLocal, acceptedDateLocal);

        if (this.state.acceptedState === "SIGNPOSTED") {
             cmd.changeSignpostedBack(this.props.dto.signpostedBack, this.state.signpostedBack)
                .changeSignpostedReason(this.props.dto.signpostedReasonId, this.state.signpostedReasonId)
                .changeSignpostedAgency(this.props.dto.signpostedAgencyId, this.state.signpostedAgencyId)
                .changeSignpostedComment(this.props.signpostedComment, this.state.signpostedComment)
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    /** Don't update the page whilst we are saving - visually confusing */
    override shouldComponentUpdate(nextProps, nextState) {
        return !nextState.autoSaving;
    }

    getErrors(): string[] {
        const errors: string[] = [];
        //const errors: {[P in keyof ReferralWithEntities]? : string} = {};
        if (this.state.acceptedState == "SIGNPOSTED") {
            if (!this.state.signpostedReasonId) errors.push("signposted reason is required");
            //if (!this.state.signpostedBack && !this.state.signpostedAgencyId) errors.push("signposted agency is required");
        }
        return errors;
    }

    private stateSetter = state => this.setState(state);

    private getFieldsToShow() {
        // assume all fields
        let fieldsToShow: (keyof State)[] = ["signpostedAgencyId"];
        // if specific fields selected
        if (this.props.dto.configResolver.getServiceType().taskDefinitionSettingHasFlag(this.props.taskName, "formDefinition", 'custom')) {
            let fieldsCsv = this.props.dto.configResolver.getServiceType().getTaskDefinitionSetting(this.props.taskName, "formDefinitionFields");
            fieldsToShow = fieldsCsv ? StringUtils.csvToArray(fieldsCsv) as (keyof State)[] : [];
        }

        return fieldsToShow;
    }

    render() {

        let fieldsToShow = this.getFieldsToShow();

        let showConditionally = (field: keyof State) => {
            return fieldsToShow.indexOf(field) >= 0;
        };

        const renderChangeDecision = (
            <div className="clearfix">
            <p className="pull-right">
                {/* audit as per overview/decideFinal.jsp */}
                {/*<p>{ReferralTaskListControl.summariseAcceptOnService(this.props.referral)}</p>*/}
                {button("edit decision", () => {
                    this.setState({acceptedState: "UNSET", readOnly: false});
                })}
            </p>
            </div>
        );

        // TODO does this need to re-trigger a workflow tasks timer?
        const renderAuditInfo = this.state.readOnly ? <Grid item xs={12}>
                <span>
                    decided {EccoDate.iso8601ToFormatShort(this.state.acceptedDate)}
                </span>
        </Grid> : null;

        let content: ReactChild;
        switch (this.state.acceptedState) {

            case "UNSET":
                content = (
                        <Grid item xs={12} className="text-center">
                            {datePickerIso8601Input("acceptedDate", "decided", state => this.setState(state), this.state, this.state.readOnly)}
                            <p>
                                {link("accept", () => {
                                    {/** NB setState needs to be a one-liner */
                                    }
                                    this.setState({autoSaving: true, acceptedState: "ACCEPTED"},
                                            () => this.props.commandForm.submitForm().then(this.props.afterSave)
                                    );
                                })}
                            </p>
                            <p>or</p>
                            <p>
                                {link(withMessages(messages => messages["signpost"] || "signpost"), () => {
                                    this.setState({acceptedState: "SIGNPOSTED"});
                                })}
                            </p>
                        </Grid>
                );
                break;

            case "ACCEPTED":
                content = (
                        <div style={{textAlign: "center"}}>
                            {renderChangeDecision}
                            {renderAuditInfo}
                            <p>accepted</p>
                        </div>
                );
                break;
            case "SIGNPOSTED": {

                const AgencyModal = (this.state.agencyDialog
                        && <ServicesContextProvider>
                            <AgencyDetailModal
                                    contextId={this.props.dto.serviceRecipientId}
                                    agencyId={this.state.agencyDialog && this.state.agencyEditing ? this.state.signpostedAgencyId : undefined}
                                    show={this.state.agencyDialog}
                                    onSave={agency => {
                                        this.findAgencies().then(agencies =>
                                            this.setState({
                                                agencyDialog: false,
                                                agencyEditing: false,
                                                signpostedAgencyId: agency.contactId,
                                                agencies: agencies.sort(sortName)}))
                                    }}
                                    onCancel={() => this.setState({agencyDialog: false})}/>
                        </ServicesContextProvider>)

                const signpostListName = this.props.dto.configResolver.getServiceType().getTaskDefinitionSetting(this.props.taskName, "signpostListName")
                                || SIGNPOSTREASON_LISTNAME;
                const signpostList = this.props.dto.features.getListDefinitionEntriesByListName(signpostListName, undefined, this.props.dto?.signpostedReasonId);

                content = withMessages(messages =>
                        <Grid container>
                            {AgencyModal}
                            {renderChangeDecision}
                            {renderAuditInfo}
                            <Grid item xs={12}>
                                {checkBox("signpostedBack", messages["signpostBack"], this.stateSetter, this.state, this.state.readOnly)}
                                {showConditionally("signpostedAgencyId") && <p>or</p>}
                            </Grid>
                            {showConditionally("signpostedAgencyId") &&
                            <>
                                <Grid item xs={10}>
                                    {dropdownList("signpost to", this.stateSetter, this.state, "signpostedAgencyId",
                                            this.state.agencies, {}, this.state.signpostedBack || this.state.readOnly)}
                                </Grid>
                                <Grid item xs={2}>
                                    {button("edit", () => {
                                        this.setState({agencyDialog: true, agencyEditing: true})
                                    }, undefined, !this.state.signpostedAgencyId || this.state.readOnly)}
                                    {button("new", () => {
                                        this.setState({agencyDialog: true})}, undefined, this.state.readOnly)
                                    }
                                </Grid>
                            </>
                            }
                            <Box/>
                            <Grid item xs={12}>
                                {dropdownList("reason", this.stateSetter, this.state, "signpostedReasonId",
                                      signpostList.map(ld => ({
                                          id: ld.getId(),
                                          name: ld.getName(),
                                          disabled: ld.getDisabled()
                                      } as IdNameDisabled)), {}, this.state.readOnly, !this.state.readOnly)}
                            </Grid>
                            <Grid item xs={12}>
                                {textArea("signpostedComment", messages["signpostComment"], this.stateSetter, this.state, undefined, this.state.readOnly)}
                            </Grid>
                        </Grid>
                );
                break;
            }
        }

        // TODO: Thinking ... is this much easier if we use portals for save and cancel
        //   possiblyModalForm is providng the buttons, but perhaps we could provide buttons.
        //   save button -> commandForm.submit() -> subforms.reduce(cmdQueue, emitChanges) -> cmdQueue.send -> cmdForm. onComplete or onCancel
        return possiblyModalForm(this.props.title, true, true,
            () => this.props.commandForm.cancelForm(),
            () => this.props.commandForm.submitForm().then(this.props.afterSave),
            this.state.acceptedState == "UNSET",
            this.state.readOnly || this.state.acceptedState == "UNSET",
            content);
    }

}
