import {
    apiClient,
    AsyncSessionData,
    EccoAPI,
    ServicesContext,
    setGlobalEccoAPI,
    withSessionData
} from "ecco-components";
import {componentAsElement,
    EccoTheme,
    ErrorBoundary
} from "ecco-components-core";
import {
    AddressedLocationAjaxRepository,
    AddressHistoryAjaxRepository,
    BuildingAjaxRepository,
    CalendarAjaxRepository,
    ChartAjaxRepository,
    ContactsAjaxRepository,
    ContractAjaxRepository,
    InvoicesAjaxRepository,
    TaskCommandAjaxRepository,
    UserAjaxRepository,
    WorkersAjaxRepository,
    ServiceRecipientAjaxRepository,
    QuestionnaireWorkAjaxRepository
} from "ecco-dto";
import {FinanceAjaxRepository} from "ecco-finance";
import {IncidentAjaxRepository} from "ecco-incidents";
import {RepairAjaxRepository} from "ecco-repairs";
import {RotaAjaxRepository} from "ecco-rota";
import * as React from "react";
import {FC, ReactElement} from "react";
import * as ReactDOM from "react-dom";
import {baseURI} from "../environment";
import {SessionDataService} from "../feature-config/SessionDataService";
import {
    getClientRepository,
    getCommandQueueRepository, getEvidenceEffectsRepository,
    getFormEvidenceRepository,
    getReferralRepository,
    getRiskWorkRepository,
    getSignatureRepository,
    getSupportSmartStepsSnapshotRepository,
    getSupportWorkRepository
} from "ecco-offline-data";
import {taskIntegrations} from "../workflow/tasklist/TasksControl";
import {defaultPageComponentRegistry} from "./defaultPageComponentRegistry";
import {auditHistoryIntegrations} from "../service-recipients/components/AuditHistory";

const eccoAPI: EccoAPI = {
    baseURI: baseURI.toString(),
    apiClient,
    pageComponentRegistry: defaultPageComponentRegistry,
    auditHistoryIntegrations: auditHistoryIntegrations,
    taskIntegrations, // NB this is undefined
    getAddressRepository: () => new AddressedLocationAjaxRepository(apiClient),
    addressHistoryRepository: new AddressHistoryAjaxRepository(apiClient),
    getBuildingRepository: () => new BuildingAjaxRepository(apiClient),
    calendarRepository: new CalendarAjaxRepository(apiClient), // OfflineRepository doesn't provide all methods (unsurprisingly ... can't sync whole calendar)
    chartRepository: new ChartAjaxRepository(apiClient),
    clientRepository: getClientRepository(),
    getCommandRepository: getCommandQueueRepository,
    contactsRepository: new ContactsAjaxRepository(apiClient),
    contractRepository: new ContractAjaxRepository(apiClient),
    getEvidenceEffectsRepository,
    financeRepository: new FinanceAjaxRepository(apiClient),
    formEvidenceRepository: getFormEvidenceRepository(),
    incidentsRepository: new IncidentAjaxRepository(apiClient),
    repairsRepository: new RepairAjaxRepository(apiClient),
    invoicesRepository: new InvoicesAjaxRepository(apiClient),
    referralRepository: () => getReferralRepository(),
    rotaRepository: new RotaAjaxRepository(apiClient, SessionDataService.getFeatures),
    riskWorkRepository: getRiskWorkRepository(),
    getSignatureRepository,
    supportWorkRepository: getSupportWorkRepository(),
    supportSmartStepsSnapshotRepository: getSupportSmartStepsSnapshotRepository(),
    questionnaireSnapshotRepository: new QuestionnaireWorkAjaxRepository(apiClient),
    userRepository: new UserAjaxRepository(apiClient),
    workersRepository: new WorkersAjaxRepository(apiClient),
    tasksRepository: new TaskCommandAjaxRepository(apiClient),
    serviceRecipientRepository: new ServiceRecipientAjaxRepository(apiClient),
    sessionData: null!
}

/** Shows a loading spinner while resolving sessionData then publishes access to all repositories
 * in EccoAPI via ServicesContext (for use via useServicesContext()) */
export const ServicesContextProvider: FC<{spinner?: ReactElement | undefined}> = (props) =>
    withSessionData( sessionData => {
        eccoAPI.sessionData = sessionData;

        // NB because taskIntegrations is undefined above, we set it here
        eccoAPI.taskIntegrations = taskIntegrations;
        eccoAPI.auditHistoryIntegrations = auditHistoryIntegrations;

        setGlobalEccoAPI(eccoAPI);
        return <ServicesContext.Provider value={eccoAPI}>
            {props.children}
        </ServicesContext.Provider>;
    }, props.spinner);

/**
 * If you're mounting a React component into an existing page, then you probably don't want the default
 * full screen spinner - but instead use SmallSpinner from ecco-components.
 * Then can do: useServicesContext().sessionData to get at sessionData etc.
 * @param spinner Optional ReactElement to use as the spinner instead of the default
 */
export const mountWithServices = (
    elements: ReactElement | ReactElement[],
    domElement: Element,
    spinner?: ReactElement | undefined
    ) => {
    return ReactDOM.render(
        <EccoTheme prefix="mws">
            <ErrorBoundary>
                <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
                    <ServicesContextProvider spinner={spinner}>
                        {elements}
                    </ServicesContextProvider>
                </AsyncSessionData>
            </ErrorBoundary>
        </EccoTheme>,
        domElement
        )
}

/** As componentAsElement, but
 * e.g. return componentAsElement(<DateInput defaultDate={this.date && this.date.toLocalJsDate()} />, "startDate1").element();
 * @param elementId A DOM elementId to use, so that we can unmount previous component at this node when our JQueryish code re-renders
 */
export function componentAsElementWithServices<P>(
    reactElement: ReactElement<P>,
    elementId: string,
    spinner?: ReactElement | undefined
): Element {
    return componentAsElement(<ErrorBoundary>
        <EccoTheme prefix="mws">
            <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
                <ServicesContextProvider spinner={spinner}>
                    {reactElement}
                </ServicesContextProvider>
            </AsyncSessionData>
        </EccoTheme>
    </ErrorBoundary>,
    elementId);
}
