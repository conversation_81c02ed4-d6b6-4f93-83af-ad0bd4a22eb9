import * as React from "react";
import {FC} from "react";
import {
    canStartVisit, canStopVisit,
    CareVisitRoot,
    CareVisitSummaryCard,
    LoadingSpinner, useCareOrEventCard, useCareVisitContext,
    useCareVisitMenuContext
} from "ecco-components";
import {EventCard} from "../components/EventCard";
import {HateoasResource} from "@eccosolutions/ecco-common";
import {EventResourceDto} from "ecco-dto";
import {LinkDto} from "@eccosolutions/ecco-common";
import {searchAnywhereMatchesCard} from "./Visit";


/**
 * REFERRAL (visit based): For showing the tasks to do today.
 * This is exactly the EventListSource data we're currently getting for existing rota visits/offline
 * but this provides the app-approach.
 */
interface CareEventProps {
    showDate?: boolean | undefined;
    showEventAsVisit?: boolean | undefined; // force an event to show as a visit card
    event: EventResourceDto;
}

class CardAsHateoas implements HateoasResource {
    constructor(public links: LinkDto[]) {}
}

/**
 * A calendar event such as a visit which will render rota visits as CareVisitOverview, otherwise will use EventCard
 * See careSingleVisitDataLoader transformToCareVisit.
 */
export const CareOrEventCard: FC<CareEventProps> = props => {

    const {loaded, data: visitProps} = useCareOrEventCard(props.event, props.showEventAsVisit);

    if (loaded && !visitProps && !(props.showEventAsVisit == true)) {
        return <EventCard event={props.event} />;
    }
    if (!loaded) {
        return <LoadingSpinner />;
    }

    return <CareVisitRoot careVisitInitState={visitProps}>
        <CareCard/>
    </CareVisitRoot>
}

export const CareCard: FC = () => {
    const {state, dispatch} = useCareVisitContext();
    const ctxMenu = useCareVisitMenuContext();

    const startable = canStartVisit(state);
    const stoppable = canStopVisit(state);
    const showStarted = ctxMenu.searchData.showStarted && !startable && stoppable;
    const showNotStarted = ctxMenu.searchData.showNotStarted && startable;
    // don't hide the card on 'finish' because then it shows immediately on undoing the filter
    const showFinished = (ctxMenu.searchData.showFinished && !startable && !stoppable) || state.showVisit;
    const showAdhoc = ctxMenu.searchData.showAdhoc && state.adHoc;
    if (!showStarted && !showNotStarted && !showFinished && !showAdhoc) {
        return null;
    }

    const display = searchAnywhereMatchesCard(ctxMenu.searchData.searchText, state);

    return (display && <CareVisitSummaryCard />);
}
CareOrEventCard.displayName = "CareOrEventCard"