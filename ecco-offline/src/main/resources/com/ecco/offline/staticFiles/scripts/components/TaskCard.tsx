import $ = require("jquery");
import ActionButton = require("../controls/ActionButton");
import BaseAsyncDataControl = require("../controls/BaseAsyncDataControl");
import RiskHistoryItemControl = require("../evidence/risk/RiskHistoryItemControl");
import RiskHistoryListControl = require("../evidence/risk/RiskHistoryListControl");
import SupportHistoryItemControl = require("../evidence/support/SupportHistoryItemControl");

import SupportHistoryListControl = require("../evidence/support/SupportHistoryListControl");
import {EccoDate, EccoDateTime, StringUtils} from "@eccosolutions/ecco-common";
import {Card, CardActions, CardHeader} from "@eccosolutions/ecco-mui";
import {Command, CommandAjaxRepository, EvidenceDiscriminator, RiskWork, SupportWork} from "ecco-commands";
import {apiClient} from "ecco-components";
import {showInModalDom} from "ecco-components-core";

// ** ONLINE ONLY **
import {
    handle404AsNullResult,
    ReferralAjaxRepository,
    ServiceRecipientWithEntities,
    TaskCommandAjaxRepository
} from "ecco-dto";
import {DeleteEvidenceRequestCommandDto} from "ecco-dto/evidence-dto";
import {TaskDto} from "ecco-dto/workflow-dto";
import * as React from "react";

const taskAjaxRepository = new TaskCommandAjaxRepository(apiClient);

const referralAjaxRepository = new ReferralAjaxRepository(apiClient);

const commandAjaxRepository = new CommandAjaxRepository(apiClient);
// ** ONLINE ONLY **


interface CardProps {
    showDate?: boolean | undefined;
    task: TaskDto;
}

// TODO: sort out better visual layout
// examples
// http://codepen.io/samuelmac/pen/MaRNbM
// https://material.io/guidelines/components/cards.html#cards-actions

class TaskCard extends React.Component<CardProps, {}> {

    override render() {

        const task = this.props.task;
        const startTime = EccoDateTime.parseIso8601(task.dueDate!!);
        const showDate = !startTime.toEccoDate().equals(EccoDate.todayLocalTime());
        const dateOrJustTimeStr = (showDate ? startTime.formatShort() : startTime.formatHoursMinutes());
        const title = StringUtils.camelToSpaces(task.taskName);
        const subtitle = "due " + dateOrJustTimeStr;

        const aboutLinks = task.links.filter(link => link.rel === 'about');
        const href = aboutLinks.length == 0 ? null : aboutLinks[0].href;
        const onClick = this.handleClickForHref(href);

        return(
                <Card style={{marginTop: 2, marginBottom: 2}}>
                    <CardHeader
                        title={title}
                        subheader={subtitle}
                    />
                    {/*<CardText>*/}
                    {/*</CardText>*/}
                    <CardActions>
                        {href ? <a key={task.taskDefinitionHandle} className="btn btn-link"
                           onClick={onClick}>action</a>
                            : null}
                    </CardActions>
                </Card>
        );
    }

    private handleClickForHref(href: string): () => void {
        let onClick = () => {};
        if (!href) {
            return onClick;
        }
        if (href.indexOf("entity://") > -1) {
            const parts = href.substring(9).split('/', 2); // we expect one, so stop if there is one appended on the end
            if (parts[0] === "DeleteRequestEvidenceCommand") {
                onClick = () => {
                    TaskDeleteEvidenceForm.showInModal(this.props.task, parts[1]);
                }
            }

        } else {
            onClick = () => {window.location.href = href};
        }
        return onClick;
    }

}


class BackingData {
    constructor(public deleteRequestCmd: DeleteEvidenceRequestCommandDto,
                public serviceRecipientWithEntities: ServiceRecipientWithEntities,
                public supportWork: SupportWork,
                public riskWork: RiskWork) {
    }
}

class TaskDeleteEvidenceForm extends BaseAsyncDataControl<BackingData> {

    public static showInModal(task: TaskDto, requestUuid: string) {
        const form = new TaskDeleteEvidenceForm(task, requestUuid, () => {});
        form.load();
        showInModalDom("delete evidence request", form.element()[0], () => {});
    }

    constructor(private task: TaskDto, private requestUuid: string, private onComplete: () => void) {
        super();
    }

    protected submitForm(): Promise<null> {
        return Promise.resolve(null);
    }

    fetchViewData(): Promise<BackingData | null> {
        return commandAjaxRepository.findOne(this.requestUuid, true)
            .then(cmdDto => {
                // NB possibly convert using CommandDtoConverter, or like SupportWork.fromDto
                const deleteCmdDto = cmdDto as DeleteEvidenceRequestCommandDto;

                // referral repo, but only by name - should be sr repo
                return referralAjaxRepository.findLatestEvidenceDeleteRequestForWorkUuid(deleteCmdDto.serviceRecipientId, deleteCmdDto.workUuid)
                    .then(latestRequestCommand => {

                        // its revoked, so don't bother rendering - just allow to close
                        if (latestRequestCommand && latestRequestCommand.revoke) {
                            return null;
                        }

                        switch (deleteCmdDto.discriminator) {
                            case "0": // legacy issue from DEV-479 which didn't pass the correct discriminator to the server, which is now read back
                                return this.findSupportWorkQ(deleteCmdDto);
                            case "1": // legacy issue from DEV-479 which didn't pass the correct discriminator to the server, which is now read back
                                return this.findRiskWorkQ(deleteCmdDto);
                            case EvidenceDiscriminator[EvidenceDiscriminator.SUPPORT]:
                            case EvidenceDiscriminator[EvidenceDiscriminator.FORM]:
                                return this.findSupportWorkQ(deleteCmdDto);
                            case EvidenceDiscriminator[EvidenceDiscriminator.RISK]:
                                return this.findRiskWorkQ(deleteCmdDto);
                            default:
                                throw new Error("work discriminator not known: " + deleteCmdDto.discriminator);
                        }
                    })
            })
            // NB we could delete the task with the serviceRecipient if we linked the task - see DeleteRequestEvidenceCommandHandler.
            .catch(handle404AsNullResult);
    }

    private findSupportWorkQ(deleteCmdDto: DeleteEvidenceRequestCommandDto) {
        return SupportHistoryListControl.supportHistoryQ(deleteCmdDto.serviceRecipientId, deleteCmdDto.evidenceTask, undefined, deleteCmdDto.workUuid).then(data => {
            // create a promise around the snapshot completion
            return new Promise<BackingData>((resolve, reject) => {
                data.work.getSnapshots().catch( (exception: any) => {
                        reject(exception)
                    });
                data.work.getSnapshots().then(it => {
                    it.forEach(([_, work]) => {
                        // only fulfil on the promise when we have the actual work item
                        resolve(new BackingData(deleteCmdDto, data.serviceRecipient, work, null));
                    });
                });
            });
        })
    }

    private findRiskWorkQ(deleteCmdDto: DeleteEvidenceRequestCommandDto) {
        return RiskHistoryListControl.riskHistoryQ(deleteCmdDto.serviceRecipientId, undefined, deleteCmdDto.workUuid).then(data => {
            // create a promise around the snapshot completion
            return new Promise<BackingData>((resolve, reject) => {
                data.work.getSnapshots().catch( (exception: any) => {
                    reject(exception)
                });
                data.work.getSnapshots().then(it => {
                    it.forEach(([_, work]) => {
                        // only fulfil on the promise when we have the actual work item
                        resolve(new BackingData(deleteCmdDto, data.serviceRecipient, null, work));
                    });
                });
            });
        })
    }

    render(data: BackingData) {
        this.element().empty();
        this.element().append($("<p>").css("font-weight", "bold").text("NB when actioned, the task will still show until the cache has expired"));

        const renderRequest = data != null;
        const renderEvidence = renderRequest && (data.supportWork || data.riskWork);

        // it may be the client has been deleted
        if (renderRequest) {
            // DELETE REQUEST
            this.element().append($("<p>").text("request author: " + data.deleteRequestCmd.userDisplayName));
            this.element().append($("<p>").text("request reason: " + data.deleteRequestCmd.reason));
            this.element().append($("<p>").text("client name: " + data.serviceRecipientWithEntities.displayName + " (on " + data.serviceRecipientWithEntities.features.getServiceCategorisation(data.serviceRecipientWithEntities.serviceAllocationId).serviceName + ")"));
        }


        const markDoneAndClose = () => {
            taskAjaxRepository.postMarkCompleted(this.task.taskHandle);
            this.onComplete();
        };

        // INSTRUCTION
        if (renderEvidence) {
            this.element().append($("<p>").css("font-weight", "bold").text("click the 3 dotted menu to action this task"));
            // WORK ITEM
            const content = $("<ul>").addClass("entry-list list-unstyled");
            this.element().append(content); // Must do this before render otherwise jSig doesn't init
            // if we revoke or delete we should mark the task as complete
            // NB that a revoke may want to indicate back to the author - and this can be done in a number of ways
            // either re-allocate/create a newthe task to the user
            const deleteHandler = (cmd: Command) => {
                markDoneAndClose();
            };
            // TODO we don't have data QuestionnaireWork for QuestionnaireHistoryItemControl
            //  simply using the repo 'case EvidenceDiscriminator[EvidenceDiscriminator.SUPPORT]:'
            //  which means the questionnaire's will come under the SupportHistoryItemControl, so just won't show answers
            let ctl = data.supportWork
                ? new SupportHistoryItemControl(data.serviceRecipientWithEntities, data.supportWork, deleteHandler)
                : new RiskHistoryItemControl(data.serviceRecipientWithEntities, data.riskWork, deleteHandler);
            content.append(ctl.element());
        } else {
            const markDone = new ActionButton("close task", undefined, true)
                .clickSynchronous(() => {
                    markDoneAndClose();
                });
            this.element().append($("<p>").css("font-weight", "bold").text("this work item has been deleted: ")
                .append(markDone.element()));
        }
    }

    public getFooter() {
        return null;
    }

}

export = TaskCard;
