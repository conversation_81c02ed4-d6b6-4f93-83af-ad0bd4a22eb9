import $ = require("jquery");
import _ = require("lodash");
import * as React from "react"
import {FormEvent} from "react"
import {EccoDate, IdNameDisabled, StringToStringMap} from "@eccosolutions/ecco-common";
import {CommandQueue, ServiceRecipientAssociatedContactCommand} from "ecco-commands";

import {possiblyModalForm, SelectList as SelectListMui} from "ecco-components-core";
import {
    AddressLocation,
    apiClient,
    update,
    UpdateSpec
} from "ecco-components";
import {
    ContactsAjaxRepository,
    ReferralAjaxRepository,
    ReferralRepository,
    SessionData
} from "ecco-dto";
import {Individual} from "ecco-dto/contact-dto";
import {ServiceRecipientAssociatedContact} from "ecco-dto/referral-dto";
import {Alert, Col, ControlLabel, FormControl, FormGroup, Row} from "react-bootstrap";
import {isValidEmailAddress} from "../../common/validation";
import {FieldGroup, SelectGroup} from "../../components/ComponentUtils";

import {Grid} from '@eccosolutions/ecco-mui';

type StringChangeEvent = FormEvent<any>;

interface Props extends React.ClassAttributes<IndividualDetailModal> {
    sessionData: SessionData;
    /** the parent organisation of the individual */
    organisationId?: number | undefined;  // is the organisationId from AssociatedContactCard's contact
    /** the context of the individual - if provided, allows us to indicate the associatedTypeId - eg 'GP' etc */
    serviceRecipientId?: number | undefined;
    individualId?: number | undefined; // is the contactId from AssociatedContactCard's contact
    show?: boolean | undefined;
    onSave?: ((individual: Individual, associatedTypeIds: number[]) => void) | undefined;
    onCancel?: (() => void) | undefined;
}

interface State {
    individual: Individual;
    initialAddressLocationId?: number | undefined
    allowAssociatedTypeIds: boolean;
    associatedTypeIdsInitial: number[];
    associatedTypeIds: number[];
    errors: any;
    alerts: StringToStringMap;
}

class IndividualDetailModal extends React.Component<Props, State> {
    private saveTimer;
    private repository = new ContactsAjaxRepository(apiClient);
    private srRepository: ReferralRepository = new ReferralAjaxRepository(apiClient);
    private addressLocation: AddressLocation = null;

    constructor(props) {
        super(props);

        const individual = this.getNewIndividual(); // expected not-null
        this.state = {
            individual,
            // Allow type to be selected if we are not org and have associated serviceRecipient
            allowAssociatedTypeIds: !props.organisationId && props.serviceRecipientId != null,
            associatedTypeIds: [],
            associatedTypeIdsInitial: [],
            errors: this.validate(individual),
            alerts: {}
        };
    }

    public override componentDidMount() {
        if (this.props.individualId) {
            const indQ = this.repository.findOneIndividual(this.props.individualId);
            const associatedContactsQ: Promise<ServiceRecipientAssociatedContact[]> = this.state.allowAssociatedTypeIds
                ? this.srRepository.findAssociatedContactsByServiceRecipientId(this.props.serviceRecipientId)
                : Promise.resolve([]);
            Promise.all([indQ, associatedContactsQ]).then(([i, aContacts]) => {
                const a = aContacts.filter(c => c.contactId == i.contactId).pop();
                this.setState({
                    individual: i,
                    initialAddressLocationId: i.addressedLocationId,
                    associatedTypeIds: a && a.associatedTypeIds ? a.associatedTypeIds : [],
                    associatedTypeIdsInitial: a && a.associatedTypeIds ? a.associatedTypeIds : [],
                    errors: this.validate(i),
                    alerts: i.organisationId ? {warning: 'this information could be shared between clients, so editing these details will affect all occurrences'} : {}
                });
            });
        }
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        const individual = update(this.state.individual, {organisationId: {$set: nextProps.organisationId}});
        this.setState({
            individual,
            errors: this.validate(individual)
        });
    }

    public override componentWillUnmount() {
        window.clearTimeout(this.saveTimer);
    }

    private getNewIndividual(): Individual {
        return {
            discriminator: "individual",
            organisationId: this.props.organisationId,
            preferredContactMethod: 'Unknown'
        } as Individual;
    }

    private handleIndividualChange(updater: (event) => UpdateSpec<Individual>, event) {
        const individual = update(this.state.individual, updater(event.target.value));
        const errors = this.validate(individual);

        this.setState({individual, errors});
    }
    private handleCheckboxChange(updater: (event) => UpdateSpec<Individual>, event) {
        const individual = update(this.state.individual, updater(event.target.checked));
        const errors = this.validate(individual);

        this.setState({individual, errors});
    }
    private handleFirstNameChange: (event: StringChangeEvent) => void
        = this.handleIndividualChange.bind(this, v => ({firstName: {$set: v}}));

    private handleLastNameChange: (event: StringChangeEvent) => void
        = this.handleIndividualChange.bind(this, v => ({lastName: {$set: v}}));

    private handleMultiAssociatedTypeIdChange(newValues: IdNameDisabled[]) {
        this.setState({
            associatedTypeIds: (newValues && newValues.map(l => l.id)) || []
        });
    }

    private handleAddressValidChange = (valid: boolean) => {
        const errors = this.validate(this.state.individual); // create errors object
        if (!valid) {
            errors.address = 'required';
        }
        this.setState({errors});
    };

    private handleSaveClick = () => {
        this.repository.saveIndividual(this.getIndividualWithAddress())
            .then(iResponse =>
                this.saveAssociatedTypes(this.state.individual.contactId ?? iResponse.id, !this.state.individual.contactId)
                    .then(() => iResponse.id)
            )
        .then(id => {
            const individual = update(this.state.individual, {contactId: {$set: id}});

            this.setState({
                individual,
                alerts: {info: 'individual saved'}
            });
            this.saveTimer = window.setTimeout(this.handleSaveTimeout, 1500);
        }).catch(() => {
            this.setState({alerts: {danger: 'failed to save individual'}});
        });
    };

    saveAssociatedTypes(individualId: number, isNew: boolean) {
        if (!this.props.serviceRecipientId) {
            // we don't have srId if we're creating a new referral
            return Promise.resolve();
        }
        const cmd = new ServiceRecipientAssociatedContactCommand(isNew ? "add" : "update", this.props.serviceRecipientId, individualId);

        if (this.state.allowAssociatedTypeIds) {
            const associatedTypeFrom = this.state.associatedTypeIdsInitial.length > 0 ? this.state.associatedTypeIdsInitial : [];
            const associatedTypeTo = this.state.associatedTypeIds.length > 0 ? this.state.associatedTypeIds : [];
            const addedIds = _.difference(associatedTypeTo, associatedTypeFrom);
            const removedIds = _.difference(associatedTypeFrom, associatedTypeTo);
            cmd.changeAddedAssociatedTypeIds(associatedTypeFrom.join(","), addedIds.join(","))
                .changeRemovedAssociatedTypeIds(associatedTypeFrom.join(","), removedIds.join(","));
        }

        const cmdQueue = new CommandQueue();
        if (cmd.hasChanges()) {
            cmdQueue.addCommand(cmd);
            return cmdQueue.flushCommands(false);
        } else {
            return Promise.resolve();
        }
    }

    private handleSaveTimeout = () => {
        this.props.onSave && this.props.onSave(this.state.individual, this.state.associatedTypeIds);
        const individual = this.getNewIndividual();

        this.setState({
            individual,
            errors: this.validate(individual),
            alerts: {}
        });
    };

    private handleCancelClick = () => {
        this.props.onCancel && this.props.onCancel();

        const individual = this.getNewIndividual();
        this.setState({
            individual: individual,
            errors: this.validate(individual),
            alerts: {}
        });
    };

    private getIndividualWithAddress(): Individual {
        this.state.individual.addressedLocationId = this.addressLocation.getAddressLocationId()
        return this.state.individual
    }

    private validate(individual: Individual) {
        const errors: {[P in keyof Individual]?: string} = {};
        if (!individual.firstName) errors.firstName = 'required';
        if (!individual.lastName) errors.lastName = 'required';
        // if (!individual.addressedLocationId) errors.addressedLocationId = 'required';
        if (individual.email && !isValidEmailAddress(individual.email)) {
            errors.email = 'must be valid email';
        }
        return errors;
    }

    public isValid(): boolean {
        return Object.keys(this.state.errors).length == 0;
    }

    override render() {
        const inputProps = {labelClassName: 'col-xs-2', wrapperClassName: 'col-xs-10', hasFeedback: true};

        const AlertElement = Object.entries(this.state.alerts).map( ([style, message]) => (
            <Alert bsStyle={style.toString()}>{message}</Alert>
        ));

        let $alerts = $("div.alert");
        $alerts.length > 0 && $alerts[0].scrollIntoView();

        if (this.props.organisationId) { // is an agency
            var jobTitle = <FieldGroup
                type='text'
                label='job title'
                value={this.state.individual.jobTitle}
                onChange={this.handleIndividualChange.bind(this, (v) => ({jobTitle: {$set: v}}))}
                validationState={this.state.errors.jobTitle ? 'error' : 'success'}
                {...inputProps} />;
        }
        else {
            // TODO: Define types, keyHolder, livingWithClient
        }

        return possiblyModalForm(
            "individual details",
            true, this.props.show || false,
            () => {this.handleCancelClick()},
            // this.props.commandForm.cancelForm(),
            () => {this.handleSaveClick()},
            // this.props.commandForm.submitForm(),
            !this.isValid(),
            false,
            <React.Fragment>
                {AlertElement}
                <form className='form-horizontal'>
                    {/* see currentTarget.value as https://github.com/react-bootstrap/react-bootstrap/issues/2781*/}
                    {this.state.allowAssociatedTypeIds &&
                        <Grid container style={{marginBottom:7}} // to match form-group of the rest
                        >
                            <Grid item xs={2}>associations</Grid>
                            <Grid item xs={10}>
                                <SelectListMui
                                    isMulti={true}
                                    placeholder={""}
                                    createNew={false}
                                    getOptionLabel={l => l.name}
                                    getOptionValue={l => l.id.toString()}
                                    value={this.state.associatedTypeIds && this.state.associatedTypeIds.length > 0
                                        && this.props.sessionData.getAssociatedTypesList().filter(l => this.state.associatedTypeIds.indexOf(l.id) > -1)}
                                    options={this.props.sessionData.getAssociatedTypesList()
                                        .filter(assocId => !assocId.disabled || this.state.associatedTypeIds.indexOf(assocId.id) > -1)}
                                    onChange={values => this.handleMultiAssociatedTypeIdChange(values as IdNameDisabled[])}
                                />
                            </Grid>
                        </Grid>
                    }
                    {jobTitle}
                    <FieldGroup
                        type='text'
                        label='title'
                        value={this.state.individual.title}
                        onChange={this.handleIndividualChange.bind(this, (v) => ({title: {$set: v}}))}
                        validationState={this.state.errors.title ? 'error' : 'success'}
                        {...inputProps} />
                    <FormGroup
                        validationState={(this.state.errors.firstName || this.state.errors.lastName) ? 'error': 'success'}>
                        <ControlLabel bsClass='col-xs-2'>name</ControlLabel>
                        <Col xs={10}>
                            <Row>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        value={this.state.individual.firstName}
                                        placeholder='first name'
                                        autoComplete='off'
                                        onChange={this.handleFirstNameChange} />
                                </Col>
                                <Col xs={6}>
                                    <FormControl
                                        type="text"
                                        placeholder='last name'
                                        autoComplete='off'
                                        value={this.state.individual.lastName}
                                        onChange={this.handleLastNameChange} />
                                </Col>
                            </Row>
                        </Col>
                    </FormGroup>
                    <div className="clearfix"/>
                    <FieldGroup
                        type='text'
                        label='mobile'
                        value={this.state.individual.mobileNumber}
                        onChange={this.handleIndividualChange.bind(this, (v) => ({mobileNumber: {$set: v}}))}
                        validationState={this.state.errors.mobileNumber ? 'error' : 'success'}
                        {...inputProps} />
                    <FieldGroup
                        type='text'
                        label='landline'
                        value={this.state.individual.phoneNumber}
                        onChange={this.handleIndividualChange.bind(this, (v) => ({phoneNumber: {$set: v}}))}
                        validationState={this.state.errors.phoneNumber ? 'error' : 'success'}
                        {...inputProps} />
                    <FieldGroup
                        type='email'
                        label='email'
                        value={this.state.individual.email}
                        onChange={this.handleIndividualChange.bind(this, (v) => ({email: {$set: v}}))}
                        validationState={this.state.errors.email ? 'error' : 'success'}
                        {...inputProps} />
                    <SelectGroup
                        label='preferred contact'
                        value={this.state.individual.preferredContactMethod}
                        onChange={this.handleIndividualChange.bind(this, (v) => ({preferredContactMethod: {$set: v}}))}
                        validationState={this.state.errors.preferredContactMethod ? 'error' : 'success'}
                        {...inputProps}>
                        <option value='Unknown'>-</option>
                        <option value='Mobile'>mobile</option>
                        <option value='Landline'>landline</option>
                        <option value='Email'>email</option>
                        <option value='Letter'>letter</option>
                        <option value='Sms'>sms</option>
                    </SelectGroup>
                    {this.state.individual.contactId &&
                        <FormGroup>
                        <ControlLabel>archived</ControlLabel>
                        <div className='col-xs-10 col-xs-offset-2'>
                            <input
                                    type='checkbox'
                                    value={this.state.individual.archived ? "true" : undefined}
                                    onChange={event => this.handleCheckboxChange(v => ({archived: {$set: v ? EccoDate.todayLocalTime().formatIso8601() : null}}), event)}
                            />
                        </div>
                    </FormGroup>}
                </form>
                <AddressLocation showBuildings={false} contactId={this.state.individual.contactId}
                                 displayAddress={this.state.individual.address}
                                 addressLocationId={this.state.initialAddressLocationId}
                                 ref={c => this.addressLocation = c} handleAddressValidChange={this.handleAddressValidChange}/>
            </React.Fragment>
        );
    }
}

export = IndividualDetailModal;
