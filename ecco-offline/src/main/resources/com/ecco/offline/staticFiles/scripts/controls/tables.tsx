import $ = require("jquery");
import BaseControl = require("./BaseControl");
import Element = require("./Element");
import * as React from "react";
import {reactElementContainer} from "ecco-components-core";

import {
    ColumnRepresentation,
    DataType,
    ReportMuiTableWrapper,
    RowContext, Table,
    TableRepresentationBase,
    unCamelCase
} from "ecco-reports";


export function table<TRow>(dataRows: TRow[], representation: TableRepresentationBase<TRow>): Table<TRow> {
    return new Table<TRow>(dataRows, representation);
}

export class TableControl extends BaseControl {
    protected downloadExcelFilename: string = "download";

    protected data: Table<any> = null!;

    constructor(private ctx: RowContext, downloadExcelFilename?: string | undefined) {
        super($("<div>"));
        if (downloadExcelFilename) {
            this.downloadExcelFilename = downloadExcelFilename;
        }
    }

    protected represent(row, column: ColumnRepresentation<any>) {
        const td = $("<td>").addClass(column.getCssClasses(row));
        try {

            // pass the context to the row representation
            const value = column.represent(this.ctx, row);
            if (typeof value != "object") {
                td.text(value);
            } else {
                // must be HrefData
                var obj: $.JQuery = $("<span>").text(value.display);
                if (value) {
                    const click = value.click;
                    if (click) {
                        obj = $("<span>")
                                .text(value.display)
                                .addClass("button")
                                .click(e => {
                                    e.preventDefault();
                                    click();
                                })
                    }
                    if (value.url) {
                        obj = $("<a>")
                                .text(value.display)
                                .attr("href", value.url && value.url.toString())
                                .attr("target", "blank")
                    }
                }
                td.append(obj);
            }
        }
        catch (e) {
            td.append($("<span>").attr("title", e).text("(error)"));
        }
        return td;
    }

    public clear() {
        this.element().empty();
    }

    public emitClickAll(): void {
        this.data.emitClickAll();
    }

    /**
     * Redraws the table with new data.
     * @param liveTable avoids complications (like updating the table or exporting old data) if we refresh the data
     * TODO provide a wrapper to handle the interaction of a live table, or don't use dataTables
     */
    setData(data: Table<any>, liveTable?: boolean | undefined) {
        this.data = data;

        this.element().empty();
        if (!data) {
            return;
        }

        // see ReportStageControl.getMatrixDataFor which creates an extra column
        const initialColumn = data.getInitialColumn();
        let $rowHeaders = this.getRowHeaders(data, !!initialColumn);
        let $rowColumns = this.getRowColumns(data, initialColumn);

        let $table = $("<table>").addClass("table");
        $table.append($("<thead>").append($("<tr>").append($rowHeaders))).append($("<tbody>").append($rowColumns));
        this.element()
                .append($table);

        // register a sorter based on moment that takes the format of the date and sorts it
        // a quick test shows that we don't need to register an additional datetime sorter
        $.fn.dataTable.moment('DD/MM/YYYY');
        $.fn.dataTable.moment('DD/MM/YYYY HH:mm');

        $table.dataTable({
            // dom - see https://datatables.net/reference/option/dom: (l)ength (f)iltering p(r)ocessing (t)able (i)nformation (p)agination
            dom: "<'row'<'col-sm-4'l><'col-sm-4'><'col-sm-3'f><'col-sm-1'B>>rtip",
            buttons: [{
                extend: 'excelHtml5',
                text: 'excel',
                filename: this.downloadExcelFilename
            }],
            // liveTable here avoids redrawing the table on a different page, and therefore jumping back to page 1
            lengthMenu: liveTable ? [[-1],['all']] : [[10, 25, 50, 100], [10, 25, 50, 100]] // avoid -1 'all' as often too long response to end user
        })
    }

    protected getRowHeaders(data: Table<any>, hasInitialColumn: boolean) {
        let $rowHeaders: $.JQuery[] = data.getRepresentation().getColumns().map((column) => $("<th>").text(unCamelCase(column.getHeading())));
        if (hasInitialColumn) {
            $rowHeaders.unshift($("<th>").text("from (down) / to (across)"));
        }
        return $rowHeaders;
    }

    protected getRowColumns(data: Table<any>, initialColumn: string[] | null) {
        return data.getDataRows()
                .map((row, index) => {
                    let $tr = $("<tr>");
                    if (initialColumn) {
                        $tr.append($("<td>").css("font-weight", "bold").text(initialColumn[index]));
                    }
                    $tr.append(
                            data.getRepresentation().getColumns()
                                    .map((column, columnIndex) => {
                                        let $td = this.represent(row, column);
                                        $td.click((e) => data.emitClick(index, columnIndex, row));
                                        return $td;
                                    })
                    );
                    return $tr;
                });
    }
}


// copied from TableControl, as a placeholder to refactor for a newer version
// we don't want ControlWrapper - which brings legacy into react
// we do want reactElementContainer - which brings react into legacy
export class TableMuiControl implements Element {
    private $container = $("<div>");

    protected downloadExcelFilename: string = "download";
    protected data: Table<any> = null!;

    private tableRef = React.createRef<ReportMuiTableWrapper>();
    private mountPoint: HTMLElement = document.createElement("div");

    constructor(private ctx: RowContext, downloadExcelFilename?: string | undefined) {
        if (downloadExcelFilename) {
            this.downloadExcelFilename = downloadExcelFilename;
        }

        const form = <ReportMuiTableWrapper ref={this.tableRef}/>;
        reactElementContainer(form, this.mountPoint);
        this.$container.append(this.mountPoint)
    }

    element(): $.JQuery {
        return this.$container;
    }

    // TODO column specific classes
    private static represent(row, column: ColumnRepresentation<any>, ctx: RowContext) {
        try {
            // pass the context to the row representation
            const valObj = column.represent(ctx, row);
            if (typeof valObj != "object") {
                return valObj;
            } else {
                return valObj;
            }
        }
        catch (e) {
            return "(error)"
        }
    }

    public emitClickAll(): void {
        this.data.emitClickAll();
    }

    setData(data: Table<any>, liveTable?: boolean | undefined) {
        this.data = data;
        if (!data) {
            return;
        }

        // see ReportStageControl.getMatrixDataFor which creates an extra column
        const initialColumn = data.getInitialColumn();
        let rowHeaders = TableMuiControl.getRowHeaders(data.getRepresentation(), !!initialColumn);
        let rowTypes = TableMuiControl.getRowTypes(data.getRepresentation(), !!initialColumn);
        let rowColumns = this.getRowColumns(data.getRepresentation(), data.getDataRows(), initialColumn, this.ctx);
        this.tableRef.current!.setData(rowHeaders, rowTypes, rowColumns, this.downloadExcelFilename)
    }

    public static getRowHeaders<TRow>(representation: TableRepresentationBase<TRow>, hasInitialColumn: boolean) {
        let $rowHeaders: string[] = representation.getColumns().map((column) => unCamelCase(column.getHeading()));
        if (hasInitialColumn) {
            $rowHeaders.unshift("from (down) / to (across)");
        }
        return $rowHeaders;
    }

    public static getRowTypes<TRow>(representation: TableRepresentationBase<TRow>, hasInitialColumn: boolean): DataType[] {
        let $rowTypes: DataType[] = representation.getColumns().map((column) => column.getType());
        if (hasInitialColumn) {
            $rowTypes.unshift("string");
        }
        return $rowTypes;
    }

    // TODO td onclick
    private getRowColumns<TRow>(representation: TableRepresentationBase<TRow>, dataRows: TRow[], initialColumn: string[] | null, ctx: RowContext) {
        return dataRows
            .map((row, index) => {
                let rowOne = initialColumn && initialColumn[index]
                let dataRows = representation.getColumns()
                    .map(column => {
                        //$td.click((e) => data.emitClick(index, columnIndex, row));
                        return TableMuiControl.represent(row, column, ctx);
                    })
                if (rowOne) {
                    dataRows.unshift(rowOne)
                }
                return dataRows
            })
    }
}
