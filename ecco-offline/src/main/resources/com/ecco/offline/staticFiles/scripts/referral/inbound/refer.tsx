import {NumberToObjectMap, Result, WebApiError} from "@eccosolutions/ecco-common";
import {
    AppBar,
    Button,
    CircularProgress,
    Dialog,
    DialogContent,
    DialogTitle, FormControlLabel,
    Grid, Radio, RadioGroup,
    Step,
    StepButton,
    Step<PERSON>ontent,
    <PERSON>per,
    <PERSON><PERSON><PERSON>,
} from "@eccosolutions/ecco-mui";

import {applicationRootPath, resourceRootPath} from "application-properties";
import {SchemaForm, update, UpdateSpec} from "ecco-components";
import {
    clientDetailsCommonFields,
    individualDetailsCommonFields,
    isValidPostcode,
    ninoFn,
    translateNames
} from "ecco-components";
import {emailInput, EccoTheme, Notifications, textInput} from "ecco-components-core";
import {
    ApiClient,
    FormEvidenceAjaxRepository,
    implementInterface,
    ObjectSchemaDto,
    ServiceAjaxRepository,
    ServiceTypeAjaxRepository,
    SessionDataAjaxRepository,
    SessionDataGlobal,
    TaskNames
} from "ecco-dto";
import {Client} from "ecco-dto/client-dto";
import {FormDefinition} from "ecco-dto/form-definition-dto";
import * as React from "react";
import * as ReactDom from "react-dom";
import {Route, Switch, useParams} from "react-router";
import {BrowserRouter} from "react-router-dom";
import {MUISchemaForm} from "../../components/MUISchemaForm";
import diff = require("json-patch-gen");
import {formatPostcode} from "ecco-components";
import { CustomFormFields } from "ecco-dto/evidence-dto";

const {add} = Notifications;


const apiClientInbound = new ApiClient(`${applicationRootPath}api/inbound/`,
    null, () => true);

const formDefinitionRepository = new FormEvidenceAjaxRepository(apiClientInbound);
const sessionDataRepository = new SessionDataAjaxRepository(apiClientInbound);
const serviceRepository = new ServiceAjaxRepository(apiClientInbound);
const serviceTypeRepository = new ServiceTypeAjaxRepository(apiClientInbound);


interface Props {
    serviceId: number | null;
    schema?: ObjectSchemaDto | undefined;
    schemaUri?: string | undefined;
}

type StepIndex = 0 | 1 | 2 | 3;

interface State {
    saved?: boolean | undefined;
    saved_rId?: number | undefined;
    stepIndex?: StepIndex | undefined,
    logoPath: string,
    sourceOfReferral: "self" | "individual" | "professional",
    dto: object,
    formData: CustomFormFields,
    errors: Record<string, string|boolean>,
    schema: ObjectSchemaDto;
    clientDetail: Client;
    globalConfig: SessionDataGlobal | null;
    serviceEmail: string | null;
    serviceTypeId: number | null;
    formDefinitions: NumberToObjectMap<FormDefinition>;
    postCodeType:  'NFA' | 'UNK' | 'WHLD' | 'input';
    networkError: string;
}

const Entry = (props) => <Grid item sm={6} xs={12} >{props.children}</Grid>;

class Wizard extends MUISchemaForm<Props, State> {

    private loadedFormDefinitions = false;

    constructor(props: Props) {
        super(props);
        this.state = {
            saved: false,
            stepIndex: 0,
            logoPath: "",
            dto: {
                serviceId: props.serviceId
            },
            sourceOfReferral: "professional",
            formData: {} as CustomFormFields,
            errors: {},
            schema: props.schema,
            clientDetail: {} as Client,
            serviceTypeId: null,
            serviceEmail: null,
            globalConfig: null,
            formDefinitions: {},
            networkError: null,
            postCodeType: "input"
        };
    }

    public override componentDidMount() {

        let logoQ = sessionDataRepository.getGlobalConfigDto();
        let schemaQ = Promise.resolve(null);
        if (this.props.schemaUri && !this.props.schema) {
            schemaQ = this.loadSchema();
        }

        Promise.all([logoQ, schemaQ])
            .then( ([globalConfig, schema] ) => {
            const logoId = globalConfig.settings["com.ecco:LOGO_FILE_ID"];
            const logoPath = logoId
                ? `${applicationRootPath}api/images/logo/${logoId}`
                : null;

            this.setState({
                schema: schema || this.props.schema,
                logoPath: logoPath,
                globalConfig: new SessionDataGlobal(globalConfig)
            });

        });

    }

    // called on a change, not the initial render (so careful of default values needing data)
    public override componentDidUpdate(prevProps: Props, prevState: State, prevContext: any) {

        const serviceId = Number(this.state.dto["serviceId"]);

        // ignore if no service because we can't choose a definition without the service
        // error if no session data loaded because we need this - loaded on startup, so user would have to be very quick
        if (!serviceId) {
            // if (!this.sessionData) {
            //     // could instead trigger below code when sessionData is loaded
            //     throw new Error("SessionData should be loaded by now");
            // }
            return;
        }

        // load a form definition if required
        if (!this.loadedFormDefinitions) {

            // ** getting the ServiceType domain is not that obvious **
            // 1) get the serviceTypeId, and then the dto (once getSessionData has been called)
            //    and then get the domain from a constructor
            //  const serviceTypeId = this.sessionData.getService(serviceId).serviceTypeId;
            //  const serviceTypeDto = ServiceType.getServiceType(serviceTypeId);
            //  const serviceTypeDomain = new ServiceType(serviceTypeDto);

            // 2) get the dto directly from the sessionData, then the domain
            //  const sessionDataDto = this.sessionData.getDto().serviceTypesById[serviceTypeId];
            //  const serviceTypeDomain = new ServiceType(serviceTypeDto);

            // 3) get the domain directly from an ajax call (which is cached)
            //    which does not require session data, BUT we do need the serviceTypeId
            //    and if we get that, we can't use the resulting getServiceType() since it won't be populated!

            serviceRepository.findOneServiceDto(serviceId).then(service =>
                serviceTypeRepository.findOneServiceType(service.serviceTypeId).then(serviceTypeDomain => {

                    // only call setState once in componentDidUpdate

                    const formDefinitionUuid = serviceTypeDomain.getTaskDefinitionSetting(TaskNames.referralDetails,  "formDefinition");
                    // TODO need to use globalConfig to take part in new array logic (see this commit)
                    const formDefinitionQ = formDefinitionRepository.findFormDefinition(formDefinitionUuid);
                    return formDefinitionQ.then(formDef => {

                        if (formDef) {
                            // trigger a state change to render the form
                            // only call setState once in componentDidUpdate
                            const updateSpec: UpdateSpec<State> = {formDefinitions: {}};
                            updateSpec.formDefinitions[serviceId] = {$set: formDef};
                            updateSpec.stepIndex = {$set: 1};
                            updateSpec.serviceTypeId = {$set: service.serviceTypeId};
                            updateSpec.serviceEmail = {$set: service.parameters && service.parameters["email.notification"]};
                            this.setState(prevState => update(prevState, updateSpec));
                        } else {
                            this.setState({
                                serviceTypeId: service.serviceTypeId,
                                serviceEmail: service.parameters && service.parameters["email.notification"]
                            });
                        }
                    }).then(() => {this.loadedFormDefinitions = true;}, () => {this.loadedFormDefinitions = true;});
                })
            );
        }

    }

    private loadSchema() {
        return apiClientInbound.get<ObjectSchemaDto>(this.props.schemaUri);
    }


    private handleNext = () => {
        const {stepIndex} = this.state;
        if (stepIndex < 3) {
            this.setState({stepIndex: stepIndex + 1 as StepIndex});
        }
        else {
            const errorMsgs: string[] = [];
            let someRequired = false;

            // this.state.errors.keys().map not valid, possibly also Object.values(this.state.agencyCategoryIndex).map
            for (const key in this.state.errors) {
                const v: boolean | string = this.state.errors[key];
                if (typeof v == "string") {
                    // some, such as postCode and serviceId set a 'required' string
                    if (v == "required") {
                        someRequired = true;
                    } else {
                        errorMsgs.push(`${key} ${v}`);
                    }
                } else {
                    if (v) {
                        someRequired = v;
                    }
                }
            }

            if (someRequired || errorMsgs.length > 0) {
                let msg = "please fix ";
                if (someRequired) {
                    msg = msg.concat("the required fields");
                }
                if (errorMsgs.length > 0) {
                    if (someRequired) {
                        msg = msg.concat(" and ");
                    }
                    msg = msg.concat("the following: " + errorMsgs.join(", "));
                }
                Notifications.add("refer-errors", msg);
            } else {
                this.save();
            }
        }
    };

    private handlePrev = () => {
        const {stepIndex} = this.state;
        if (stepIndex > 0) {
            this.setState({stepIndex: stepIndex - 1 as StepIndex});
        }
    };

    private save() {

        // this.props.schema.links.
        const uri = this.props.schemaUri.split("/$schema/")[0];
        add("save", "Saving...");
        const patch = diff({}, this.state.formData);
        const serviceId = Number(this.state.dto["serviceId"]);
        const formDefinitionUuid = this.state.formDefinitions[serviceId]?.uuid;

        const dto = this.state.dto;
        if (this.state.sourceOfReferral == "self") {
            dto['selfReferral'] = true
        }

        // postcode
        if (this.state.postCodeType != "input") {
            // overwrite with the 'type' which is allowed as input - all the way through to fixed bldg_addresses
            dto['postCode'] = this.state.postCodeType
            const line1 = this.state.postCodeType == 'NFA'
                    ? "No fixed abode"
                    : this.state.postCodeType == 'UNK'
                        ? "Withheld"
                        : "Unknown";
            dto["line1"] = line1;
            dto["town"] = undefined;
        }

        apiClientInbound.post<Result>(uri, {dto: dto, formDefinitionUuid: formDefinitionUuid, formData: patch}).then( result => {
                this.setState({
                    saved: true,
                    saved_rId: parseInt(result.id)
                });
                // InboundReferral returns from ReferralController#createImport the Result(referralId)
                add("save", "Saved");
            })
            .catch( (error: WebApiError) => {
                this.setState({networkError: error.toString()});
            });
    }

    private renderStepActions(step) {
        return (
            <div style={{margin: '12px 0'}}>
                {step > 0 && (
                    <Button
                        variant="text"
                        disableTouchRipple={true}
                        disableFocusRipple={true}
                        onClick={this.handlePrev}
                    >
                        back
                    </Button>
                )}
                <Button
                    variant="contained"
                    disableTouchRipple={true}
                    disableFocusRipple={true}
                    color="primary"
                    //disabled={step == 3 && this.hasErrors()}
                    onClick={this.handleNext}
                    style={{marginRight: 12}}
                >
                    {step < 3 ? "Next" : "Done"}
                </Button>
            </div>
        );
    }

    private static clientDetailOptional(state: State, field: string) {
        const serviceType = state.globalConfig.getServiceTypeById(state.serviceTypeId);
        // NB OPTIONAL fields come from the serviceType
        // NB REQUIRED fields come from the cfg_settings table EXCEPT for clientWithContact2
        // as per clientDetailAbstractRender defined optional fields
        const csvFields = serviceType && serviceType.getTaskDefinitionSetting(TaskNames.clientWithContact,  "clientDetailFields") || "";
        return csvFields.indexOf(field) > -1;
    };
    private static clientDetailRequired(state: State, field: string) {
        const requiredFields = Wizard.clientDetailRequiredFields(state);
        return requiredFields.indexOf(field) > -1;
    };
    private static clientDetailRequiredFields(state: State) {
        // NB could move config to task 'clientWithContact' - see clientDetailOptional
        // NB OPTIONAL fields come from the serviceType
        // NB REQUIRED fields come from the cfg_settings table EXCEPT for clientWithContact2
        const requiredFields = translateNames(
            state.globalConfig.getSettingAsArray("com.ecco.forms:CLIENT_DETAIL_REQUIRED_FIELDS")
        );

        const serviceType = state.globalConfig.getServiceTypeById(state.serviceTypeId);
        // as per clientDetailAbstractRender defined required fields
        const optionalFieldsStr = serviceType && serviceType.getTaskDefinitionSetting(TaskNames.clientWithContact, "clientDetailFields") || "";
        const optionalFields = optionalFieldsStr.split(',').map(i => i.trim())

        // there are default fields that are required by default - firstName, lastName, line1/town/postCode
        // there are default fields that are not required by default - birthDate
        const defaultFieldsMaybeRequired = "birthDate";

        // return optional filtered by required
        const shownFieldsMaybeRequired = optionalFields.concat(defaultFieldsMaybeRequired);
        return shownFieldsMaybeRequired.filter(o => requiredFields.indexOf(o) > -1);
    };

    private getStepContent(step: StepIndex) {
        const dtoStateSetter = dto => this.setState({dto});
        const schema = implementInterface(this.state.schema).asObjectSchema();
        const serviceId = Number(this.state.dto["serviceId"]);

        // mimic the MUISchemaForm error logic for our bootstrap fields
        // so that the submit button remains disabled if invalid
        this.state.errors['firstName'] = !this.state.dto['firstName'];
        this.state.errors['lastName'] = !this.state.dto['lastName'];
        this.state.errors['address'] = this.state.postCodeType == 'input' && (!this.state.dto['line1'] || !this.state.dto['town'] || !this.state.dto['postCode']);
        Wizard.clientDetailRequiredFields(this.state).map(f => {
            this.state.errors[f] = !this.state.dto[f];
        })

        this.state.errors['ni'] = false;
        if (this.state.dto["ni"]) {
            const result = ninoFn(false, this.state.dto["ni"]);
            if (result == "error") {
                this.state.errors['ni'] = 'invalid';
            }
        }

        this.state.errors["postCode"] = false;
        if (this.state.postCodeType == 'input') {
            const pc = this.state.dto["postCode"];
            if (!pc) {
                this.state.errors["postCode"] = 'required';
            } else if (!isValidPostcode(pc)) {
                this.state.errors["postCode"] = 'should be a valid postcode';
            }
        }

        // we don't need to set an initial error state for 'formData' because liveValidation triggers the initial state (by observation)
        //this.state.errors['formData'] = // somehow force validation check

        this.state.errors['referrerName'] = false
        this.state.errors['referrerPhoneNumber'] = false
        this.state.errors['referrerJobTitle'] = false
        this.state.errors['agencyName'] = false
        if (this.state.sourceOfReferral != "self") {
            this.state.errors['referrerName'] = !this.state.dto['referrerName']
            this.state.errors['referrerPhoneNumber'] = !this.state.dto['referrerPhoneNumber']
        }
        if (this.state.sourceOfReferral == "professional") {
            this.state.errors['referrerJobTitle'] = !this.state.dto['referrerJobTitle']
            this.state.errors['agencyName'] = !this.state.dto['agencyName']
        }

        switch (step) {
            case 0:
                return this.selectField(schema.properties, "serviceId", this.props.serviceId != null);

            case 1:
                const clientStateSetter = (clientDetail: Client) => {
                    // see InboundReferralParams
                    const dtoNew = {};
                    // TODO is "ni", "housingBenefit", "nhs" (don't forget InboundReferralController.java)
                    dtoNew["firstName"] = clientDetail.firstName;
                    dtoNew["lastName"] = clientDetail.lastName;
                    dtoNew["knownAs"] = clientDetail.knownAs;
                    dtoNew["pronounsId"] = clientDetail.pronounsId;
                    dtoNew["birthDate"] = clientDetail.birthDate;
                    dtoNew["mobileNumber"] = clientDetail.mobileNumber;
                    dtoNew["phoneNumber"] = clientDetail.phoneNumber;
                    dtoNew["email"] = clientDetail.email;
                    dtoNew["genderId"] = clientDetail.genderId;
                    dtoNew["firstLanguageId"] = clientDetail.firstLanguageId;
                    dtoNew["ni"] = clientDetail.ni;
                    dtoNew["nhs"] = clientDetail.nhs;
                    dtoNew["housingBenefit"] = clientDetail.housingBenefit;
                    dtoNew["ethnicOriginId"] = clientDetail.ethnicOriginId;
                    dtoNew["religionId"] = clientDetail.religionId;
                    dtoNew["nationalityId"] = clientDetail.nationalityId;
                    dtoNew["disabilityId"] = clientDetail.disabilityId;
                    dtoNew["sexualOrientationId"] = clientDetail.sexualOrientationId;
                    dtoNew["maritalStatusId"] = clientDetail.maritalStatusId;
                    this.setState({
                        clientDetail: clientDetail,
                        dto: {...this.state.dto, ...dtoNew}
                    });
                };
                const postCodeDtoStateSetter = (dto: any) => {
                    postCodeStateSetter(dto["postCode"]);
                };
                const postCodeStateSetter = (postCode: string | null) => {
                    const formatted = formatPostcode(postCode);
                    // see InboundReferralParams
                    const dtoNew = {};
                    dtoNew["postCode"] = formatted;
                    this.setState({
                        dto: {...this.state.dto, ...dtoNew}
                    });
                };
                return (<div>
                    {/* alternative approach to pushing 'required' into ComponentUtils
                    <FormGroup validationState={this.state.dto['firstName'] == null ? "error" : "success"}>
                        {textInput("firstName", "First name", dtoStateSetter, this.state.dto)}
                    </FormGroup>
                    */}

                    <Grid container>
                        {individualDetailsCommonFields(clientStateSetter, this.state.clientDetail,
                                                       (fld) => Wizard.clientDetailRequired(this.state, fld),
                                                       (fld) => Wizard.clientDetailOptional(this.state, fld), false,
                            this.state.globalConfig, (fld) => "preferredContact" == fld)}
                        {clientDetailsCommonFields(clientStateSetter, this.state.clientDetail,
                                                       (fld) => Wizard.clientDetailRequired(this.state, fld),
                                                       (fld) => Wizard.clientDetailOptional(this.state, fld), false,
                            this.state.globalConfig, (fld) => "preferredContact" == fld)}
                    </Grid>

                    <Grid container>
                            <Grid item xs={12} >
                                <RadioGroup row={true}
                                            name="postCodeType"
                                            value={this.state.postCodeType} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                                            onChange={(_, value: 'NFA' | 'UNK' | 'WHLD' | 'input') => this.setState({postCodeType: value})}
                                >
                                    <FormControlLabel key={"manual"} value={"input"} control={<Radio/>} label={"address known"}/>
                                    <FormControlLabel key={"NFA"} value={"NFA"} control={<Radio/>} label={"no fixed abode"}/>
                                    <FormControlLabel key={"UNK"} value={"UNK"} control={<Radio/>} label={"unknown"}/>
                                    <FormControlLabel key={"WHLD"} value={"WHLD"} control={<Radio/>} label={"withheld"}/>
                                </RadioGroup>
                            </Grid>
                            <Entry>
                                {/*{this.state.postCodeType == 'input' &&
                                    createTextInput("line1", "address", this.state.dto["address"], addressStateSetter, 'text',
                                            {placeholder: "address", maxLength: 10, size: 'small', required: this.state.postCodeType == 'input'},
                                            () => this.state.errors["address"] ? 'error' : 'success'
                                    )}*/}
                                {this.state.postCodeType == 'input' &&
                                    textInput("line1", "address", dtoStateSetter, this.state.dto, undefined, undefined, true)
                                }
                            </Entry>
                            <Entry>
                                {this.state.postCodeType == 'input' &&
                                    textInput("town", "town", dtoStateSetter, this.state.dto, undefined, undefined, true)
                                }
                            </Entry>
                            <Grid item xs={4}>
                                {this.state.postCodeType == 'input' &&
                                    textInput("postCode", "post code", postCodeDtoStateSetter, this.state.dto, undefined, undefined, this.state.postCodeType == 'input', 10, 'small')
                                    /*createTextInput("postCode", "post code", postCodeState, postCodeStateSetter, 'text',
                                        {placeholder: "enter a full UK postcode", maxLength: 10, size: 'small', required: this.state.postCodeType == 'input'},
                                        () => this.state.errors["postcode"] ? 'error' : 'success'*/
                                }
                            </Grid>
                        </Grid>
                    </div>);

            case 2:
                return <div>
                    <SchemaForm
                        readOnly={false}
                        formData={this.state.formData}
                        formDefinition={this.state.formDefinitions[serviceId]}
                        onChange={(data, hasErrors) => {this.handleChangeForm(data, hasErrors)}}
                        resetData={false}
                    />
                    {/*{this.textBox(schema.properties, "referralReason")}<br />*/}
                </div>;

            case 3:
                const SourceProf = this.state.sourceOfReferral == "professional" && <Grid container>
                    <Entry>
                        {textInput("referrerName", "Your name", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                    <Entry>
                        {emailInput("referrerEmail", "Your email", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                    <Entry>
                        {textInput("referrerPhoneNumber", "Your phone number", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                    <Entry>
                        {textInput("referrerJobTitle", "Your job title/role", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                    <Entry>
                        {textInput("agencyName", "Your organisation name", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                </Grid>;

                const SourceIndividual = this.state.sourceOfReferral == "individual" && <Grid container>
                    <Entry>
                        {textInput("referrerName", "Your name", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                    <Entry>
                        {textInput("referrerPhoneNumber", "Your phone number", dtoStateSetter, this.state.dto, undefined, undefined, true)}
                    </Entry>
                </Grid>;

                const SourceChoice = <Grid container>
                    <Entry>{`What is your relationship to ${this.state.clientDetail.firstName ?? ''} ${this.state.clientDetail.lastName ?? ''}?`}</Entry>
                    <Entry>
                        <RadioGroup name="sourceOfReferral"
                                    value={this.state.sourceOfReferral} // Must not be undefined as that would cause "uncontrolled -> controlled" issue
                                    onChange={(_, value: "self" | "individual" | "professional") => this.setState({sourceOfReferral: value})}
                                    /* style={{paddingLeft: "60px", paddingTop: "20px"}} */
                                    >
                            <FormControlLabel key={"self"} value={"self"} control={<Radio/>} label={"I am that person"}/>
                            <FormControlLabel key={"individual"} value={"individual"} control={<Radio/>} label={"I am a friend/family member"}/>
                            <FormControlLabel key={"professional"} value={"professional"} control={<Radio/>} label={"I am a professional"}/>
                        </RadioGroup>
                    </Entry>
                </Grid>;

                return <>
                    {SourceChoice}
                    {SourceIndividual}
                    {SourceProf}
                </>;
        }
    }

    private handleChangeForm(data: any, hasError: boolean) {
        this.setState(prevState => {
            const errorsUpdated = prevState.errors;
            errorsUpdated['formData'] = hasError;
            return update(prevState, {
                    formData: {$set: data},
                    errors: {$set: errorsUpdated}
                });
        });
    }

    override render() {
        return (this.state.schema
                ? <div style={{maxWidth: 760, maxHeight: 400, margin: 'auto'}}>
                    {this.state.networkError && <Dialog open={true}>
                        <DialogTitle>Something went wrong</DialogTitle>
                        <DialogContent>
                            There was an error: {this.state.networkError}
                            <p>Please 'dismiss' and try again.</p>
                            <Button
                                style={{float: "right"}}
                                onClick={() => this.setState({networkError: null})}
                            >
                                Dismiss
                            </Button>
                        </DialogContent>
                    </Dialog>}
                    {this.state.saved
                        ? <div style={{padding: "24px"}}>
                            <h2 className="md-headline">Referral saved</h2>
                            <p>Your referral has been saved and will be processed soon.</p>
                            <p>Your reference is: <b>r-id {this.state.saved_rId}</b></p>
                            {this.state.serviceEmail &&
                                <p>Please send any additional information to: <a href={`mailto:${this.state.serviceEmail}?subject=ECCO New Referral (info on r-id ${this.state.saved_rId})`}>{this.state.serviceEmail}</a></p>
                            }
                            <Button
                                component={"a"}
                                // linkButton={true}
                                style={{float: "right"}}
                                disableTouchRipple={true}
                                disableFocusRipple={true}
                                onClick={() => window.location.reload()}
                            >
                                Add another
                            </Button>
                        </div>
                        : <div style={{padding: "24px"}}>
                            {this.state.logoPath ? <img style={{maxHeight: '75px'}} src={this.state.logoPath}/> : null}
                            <h3>Create a referral</h3>
                            <p>Enter details here to create a referral in our ECCO system</p>
                            <Stepper
                                activeStep={this.state.stepIndex}
                                nonLinear={true}
                                orientation="vertical"
                            >
                                <Step>
                                    <StepButton style={{fontSize: "50px"}} onClick={() => this.setState({stepIndex: 0})}>
                                        Select service
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(0)}
                                        {this.renderStepActions(0)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 1})}>
                                        Enter person's details
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(1)}
                                        {this.renderStepActions(1)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 2})}>
                                        Enter referral details
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(2)}
                                        {this.renderStepActions(2)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 3})}>
                                        Your details
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(3)}
                                        {this.renderStepActions(3)}
                                    </StepContent>
                                </Step>
                            </Stepper>
                        </div>}
            </div>
            : <div key="progress" className="vertical-center">
                <div style={{width: '100%'}}>
                    <CircularProgress style={{margin: 'auto', display: 'block'}}/>
                </div>
            </div>
        );
    }
}

type RouteParams = {serviceId?: string | undefined};

const leftIcon =
    <img src={resourceRootPath + "themes/ecco/images/logo_white.png"} height="48"/>;

export function ReferComponent() {

    const serviceId = useParams<RouteParams>().serviceId;

    return (
        <div>
            <AppBar
                position="fixed"
                style={{top: 0}}
            >
                <Toolbar>{leftIcon}</Toolbar>
            </AppBar>
            <div style={{marginTop: 64}}>
                <Wizard schemaUri="referrals/$schema/" serviceId={serviceId ? parseInt(serviceId) : null}/>
                {/*<Wizard schema={schema}/>*/}
            </div>
        </div>
    );
}

ReactDom.render((
    <EccoTheme prefix="refer">
        <BrowserRouter basename={applicationRootPath.substr(0, applicationRootPath.length - 1)}>
            <Switch>
                <Route exact path={["/p/r/refer", "/p/r/refer/:serviceId",
                    "/nav/r/refer", "/nav/r/refer/:serviceId",
                    "/nav/p/r/refer", "/nav/p/r/refer/:serviceId"]}>
                    <ReferComponent />
                </Route>
                <Route path="/">
                    <h3>incorrect wiring</h3>
                </Route>
            </Switch>
        </BrowserRouter>
    </EccoTheme> ),
    document.getElementById("appbar"));
