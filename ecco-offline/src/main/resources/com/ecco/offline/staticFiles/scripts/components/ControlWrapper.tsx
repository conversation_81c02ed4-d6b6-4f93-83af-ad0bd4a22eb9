import * as React from "react";
import {ComponentType, LazyExoticComponent, useEffect, useMemo} from "react";
import {resourceRootPath} from "application-properties"
import {useReloadHandler} from 'ecco-components';
import {DomElementContainer} from 'ecco-components-core';

export type LoadableControl = {load?: (() => void) | undefined, domElement: () => Element };

type Control = LoadableControl & {new(args:any): LoadableControl};

interface ControlFactory<C extends Control> {
    new(args: ConstructorParameters<C>);
}

/**
 * Pass in a control instance and this will load it within a div
 * <pre>
 *     render() {
 *         const control = useMemo(() => new ReportsListControl(), [])
 *         return <ControlWrapper control={control}/>;
 *     }
 * </pre>
 *
 * @deprecated Prefer useControl()
 */
function ControlWrapper<CONTROL extends LoadableControl>(props: {control: CONTROL, dontLoad?: boolean | undefined, className?: string | undefined, deps?: any[] | undefined}) {
    useEffect(() => {
        !props.dontLoad &&
            props.control.load && props.control.load();
            // Clear the content when we unmount - assumes that load renders everything
            return () => {
                props.control.domElement().innerHTML = ""; // TODO: Dangerous?? - why isn't load enough?

                // clear datepicker that get added to <body>
                const pickerEls = document.getElementsByClassName("ui-datepicker");
                for (let i = 0; i < pickerEls.length; i++) {
                    pickerEls[i].innerHTML = "";
                }
            };
        },
        props.deps || []);

    return <div className={props.className || "container-fluid v-gap-15"}>
        <link media="screen, projection" type="text/css" href={`${resourceRootPath}css/jqueryui/jqueryui.css`} rel="stylesheet"
              title="For jquery-datepicker"/>
        <DomElementContainer content={props.control.domElement()}/>
    </div>;
}

/**
 * <b>WARNING: You'll have to use @ts-ignore at the call site if tsc complains about the wrong number of args</b>
 *
 * If you want to lazy load an ecco BaseControl the React.lazy() way, then this
 * turns an ecco BaseControl into a lazy loaded React component
 * <pre>
 *     render() {
 *         const control = new ReportsListControl();
 *         return <ControlWrapper control={control}/>;
 *     }
 * </pre>
 */
function lazyControlWrapperInner<C extends Control, T extends ControlFactory<C>, R extends ComponentType<any>>(
    factory: () => Promise<{ default: T }>,
    className: string | undefined,
    ...args: ConstructorParameters<T>
): LazyExoticComponent<any> {

    let elementFactory: () => Promise<{default: React.ComponentType<any>}> = () => {
        return factory()
            .then(module => {
                console.assert(module.default != undefined); // Module must have default export
                // @ts-ignore
                const control = new module.default(...args);
                return {default: () => <ControlWrapper control={control} className={className}/>};
            });
    };
    return React.lazy(elementFactory);
}
export function lazyControlWrapper<C extends Control, T extends ControlFactory<C>, R extends ComponentType<any>>(
        factory: () => Promise<{ default: T }>,
        ...args: ConstructorParameters<T>
): LazyExoticComponent<any> {
    return lazyControlWrapperInner(factory, undefined, ...args);
}
export function lazyControlWrapperNoPadding<C extends Control, T extends ControlFactory<C>, R extends ComponentType<any>>(
        factory: () => Promise<{ default: T }>,
        ...args: ConstructorParameters<T>
): LazyExoticComponent<any> {
    return lazyControlWrapperInner(factory, "-dummy-", ...args);
}


export function useControl<C extends Control, T extends ControlFactory<C>>(
    controlConstructor: ControlFactory<Control>, args: ConstructorParameters<T>, className?: string) {

    // @ts-ignore FOR TESTING
    const control = useMemo(() => new controlConstructor(...args), args);
    useReloadHandler(() => control.load());
    return useMemo(() => () => <ControlWrapper control={control} className={className} deps={args}/>, [control, ...args])
}

export default ControlWrapper;
