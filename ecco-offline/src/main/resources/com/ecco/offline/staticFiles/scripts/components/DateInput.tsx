import moment = require('moment');
import * as React from 'react';
import {SparseArray} from "@eccosolutions/ecco-common";

interface Props extends React.Props<DateInput> {
    name: string;
    label?: string | undefined;
    labelCssName?: string | undefined;
    standalone?: boolean | undefined;
    value?: Date | undefined;
    /** Should be styled as error (red) */
    required?: boolean | undefined;
    onChange: (date: Date | null) => void;
}

interface State {
    day?: number | undefined;
    month?: number | undefined;
    year?: number | undefined;
    days: number[];
    months: number[];
    years: number[];
    error: boolean;
}

function intFromHtmlInput(eventTarget: EventTarget): number | undefined {
    let value = (eventTarget as HTMLInputElement).value;
    return value ? parseInt(value, 10) : undefined;
}

function get1toN(length: number) {
    const days: number[] = [];
    for (let i = 0; i < length; i++) {
        days.push(i + 1);
    }
    return days;
}

const dayOptions: SparseArray<number[]> = {
    28: get1toN(28),
    29: get1toN(29),
    30: get1toN(30),
    31: get1toN(31)
};

function getDaysInDate(day?: Date | undefined): number[] {
    const length = day && moment.utc(day).daysInMonth() || 31;
    return dayOptions[length];
}

function getYears(): number[] {
    const year = moment.utc().year();

    const years: number[] = [];
    for (let i = 0 ; i < 120 ; i++) {
        years.push(year - i);
    }
    return years;
}

const yearOptions = getYears();

// FIXME: This is seriously complex and broken in what it is trying to achieve
class DateInput extends React.Component<Props, State> {

    constructor(props: Props) {
        super(props);

        this.state = {
            day: this.props.value && this.props.value.getDate(),
            month: this.props.value && this.props.value.getMonth(),
            year: this.props.value && this.props.value.getFullYear(),
            days: getDaysInDate(this.props.value),
            months: [0,1,2,3,4,5,6,7,8,9,10,11],
            years: yearOptions,
            error: false
        };
    }

    public override UNSAFE_componentWillReceiveProps(nextProps: Props) {
        if (nextProps.value) {
            this.setState({
                day: nextProps.value.getDate(),
                month: nextProps.value.getMonth(),
                year: nextProps.value.getFullYear(),
                days: getDaysInDate(nextProps.value)
            });
        }
    }

    override render() {
        const m = moment().utc();
        let labelCss = this.props.labelCssName || 'col-xs-2 control-label';
        let dayCss = (this.props.label ? '' : 'col-xs-offset-2 ') + 'col-xs-3 col-md-2';
        return (
            <div className={this.props.standalone ? undefined : 'form-group' + (this.state.error ? ' form-group-error has-error' : '')}>
                {(this.props.label) && <label className={labelCss}>{this.props.label}</label>}
                <div className={dayCss}>
                    <select
                        name={this.props.name + '.day'}
                        value={this.state.day}
                        onChange={(event) => this.handleDateChange('day', event.target)}
                        onBlur={(event) => this.handleDateChange('day', event.target)}
                        className="form-control"
                    >
                        <option value="">day</option>
                        {this.state.days.map( day => (
                            <option value={day} key={day}>
                                {day}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="col-xs-3 col-md-2">
                    <select
                        name={this.props.name + '.month'}
                        value={this.state.month}
                        onChange={(event) => this.handleDateChange('month', event.target)}
                        onBlur={(event) => this.handleDateChange('month', event.target)}
                        className="form-control"
                    >
                        <option value="">month</option>
                        {this.state.months.map( month => (
                            <option value={month} key={month}>
                                {m.month(month).format('MMM')}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="col-xs-4 col-md-3">
                    <select
                        name={this.props.name + '.year'}
                        value={this.state.year}
                        onChange={(event) => this.handleDateChange('year', event.target)}
                        onBlur={(event) => this.handleDateChange('year', event.target)}
                        className="form-control"
                    >
                        <option value="">year</option>
                        {this.state.years.map( year => (
                            <option value={year} key={year}>
                                {year}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
        );
    }

    private handleDateChange(part: 'day' | 'month' | 'year', target: EventTarget) {
        let day = this.state.day;
        let month = this.state.month;
        let year = this.state.year;
        let days = this.state.days;

        switch (part) {
            case 'day':
                // @ts-ignore
                day = intFromHtmlInput(target);
                break;
            case 'month':
                // @ts-ignore
                month = intFromHtmlInput(target);
                days = this.getDaysInMonth(year, month);
                break;
            case 'year':
                // @ts-ignore
                year = intFromHtmlInput(target);
                days = this.getDaysInMonth(year, month);
                break;
        }

        // NOTE: month can be zero, hence == null rather than falsey
        const error = this.props.required && (!day || month == null || !year);

        this.setState({
            day: day,
            month: month,
            year: year,
            days: days,
            error: error || false
        });

        if (day && month != null && year) {
            const date = new Date(Date.UTC(year, month, day));
            this.props.onChange(date);
        } else { // if (this.props.value) { TODO: probably want initial date
            this.props.onChange(null);
        }
    }

    private getDaysInMonth(year?: number | undefined, month?: number | undefined): number[] {
        // If we don't know the month, then just return 31 for now
        if (!month) {return dayOptions[31]};

        const date = new Date();
        date.setUTCDate(1); // So we don't wrap over if today is 31st

        if (year) {
            date.setUTCFullYear(year);
        }
        if (month) {
            date.setUTCMonth(month);
        }
        return getDaysInDate(date);
    }
}
export default DateInput;