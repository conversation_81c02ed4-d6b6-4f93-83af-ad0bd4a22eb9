/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    id("org.eccosolutions.querydsl-java-library")
}

dependencies {
    implementation(project(":ecco-buildings"))
    api(project(":ecco-calendar-core"))
    implementation(project(":ecco-calendar-cosmo"))
    api(project(":ecco-config"))
    api(project(":ecco-contacts"))
    implementation(project(":ecco-dao"))
    api(project(":ecco-dom"))
    implementation(project(":ecco-evidence"))
    implementation(project(":ecco-infrastructure"))
    api(project(":ecco-int-api-default"))
    implementation(project(":ecco-security-core"))
    implementation(project(":ecco-service-config"))
    api(project(":ecco-servicerecipient"))
    api(project(":ecco-workflow"))
    implementation(project(":ecco-upload-dom"))
    api(project(":ecco-upload-web"))

    testImplementation(project(":test-support"))

    implementation("com.github.eccosolutions.clickstream:clickstream-core:1.1.0-rc")
    api("joda-time:joda-time:2.10.8")
    implementation("org.apache.commons:commons-lang3")
    implementation("org.bouncycastle:bcprov-jdk15on:1.69")
    implementation("org.springframework:spring-websocket")
    implementation("org.springframework.security:spring-security-web")
    implementation("com.google.guava:guava")
}

description = "ecco-service"
