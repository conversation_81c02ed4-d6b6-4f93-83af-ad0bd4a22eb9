package com.ecco.calendar.cosmo;

import com.ecco.calendar.core.*;
import com.ecco.calendar.event.RotaDemandPreChangeEvent;
import com.ecco.infrastructure.annotations.WriteableTransaction;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.infrastructure.time.JodaToJDKAdapters;
import com.ecco.infrastructure.util.EccoTimeUtils;
import com.ecco.service.exceptions.RotaException;
import com.google.common.collect.Range;
import com.ecco.calendar.core.Recurrence.RecurrenceHandle;
import com.ecco.calendar.core.RecurringEntry.RecurringEntryHandle;
import lombok.Getter;
import lombok.Setter;
import net.fortuna.ical4j.model.Calendar;
import net.fortuna.ical4j.model.DateList;
import net.fortuna.ical4j.model.Property;
import net.fortuna.ical4j.model.Recur;
import net.fortuna.ical4j.model.parameter.PartStat;
import net.fortuna.ical4j.model.property.Status;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NonNull;
import org.joda.time.*;
import org.osaf.cosmo.calendar.RecurrenceExpander;
import org.osaf.cosmo.model.*;
import org.osaf.cosmo.model.filter.EqualsExpression;
import org.osaf.cosmo.model.filter.EventStampFilter;
import org.osaf.cosmo.model.filter.NoteItemFilter;
import org.osaf.cosmo.model.hibernate.HibItem;
import org.osaf.cosmo.service.ContentService;
import org.osaf.cosmo.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEvent;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import org.jspecify.annotations.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Transient;
import java.net.URI;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ecco.infrastructure.time.JodaToJDKAdapters.instantFromJoda;
import static com.ecco.infrastructure.time.JodaToJDKAdapters.localDateTimeToJoda;
import static java.time.Instant.ofEpochMilli;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static lombok.AccessLevel.NONE;

@WriteableTransaction
@Component
public class CosmoCalendarRecurringService extends CosmoCalendarBase implements CalendarRecurringService {

    @PersistenceContext
    protected EntityManager entityManager;

    @Transient
    @Getter(NONE)
    @Setter(NONE)
    protected transient MessageBus<ApplicationEvent> messageBus;

    @Override
    public Integer allocateAsyncAfterDays() {
        return 14;
    }

    private static Function<net.fortuna.ical4j.model.Date, Recurrence> droppedDateToRecurrenceForItem(NoteItem item,
                                                                                                      CosmoConverter converter) {
        return date ->
                new NoteOccurrenceToRecurrenceAdapter(NoteOccurrenceUtil.createNoteOccurrence(date, item), converter) {
                    @Override
                    public Status getStatus() {
                        return Status.DROPPED;
                    }
                };
    }

    private Predicate<Recurrence> matchesStatus(Recurrence.Status status) {
        return input -> status == null || input.getStatus() == status;
    }

    private static final Logger log = LoggerFactory.getLogger(CosmoCalendarRecurringService.class);
    protected final CosmoConverter converter;
    protected final EntityFactory entityFactory;

    /** For CGLIB only */
    public CosmoCalendarRecurringService() {
        this(null, null, null, null, null, null);
    }

    public CosmoCalendarRecurringService(ContentService contentService, UserService userService, EntityFactory entityFactory,
                                         EntityUriMapper entityUriMapper, MessageBus<ApplicationEvent> messageBus,
                                         final CosmoConverter entryConverter) {
        super(contentService, userService, entityUriMapper, messageBus);
        this.contentService = contentService;
        this.entityFactory = entityFactory;
        this.entityUriMapper = entityUriMapper;
        this.converter = entryConverter;
        this.messageBus = messageBus;
    }

    @Override
    public Instant recurrenceToInstant(Recurrence.RecurrenceHandle recurrence) {
        var mod = new ModificationUid(recurrence.toString());
        return mod.getRecurrenceId().toInstant();
    }

    @Override
    public RecurringEntry getRecurringEntry(RecurringEntryHandle handle) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(handle.toString());
        if (item == null) {
            throw new CalendarException("No entry with handle " + handle + " exists.");
        }
        return converter.itemToRecurringEntry(item);
    }

    /**
     * Called currently by:
     *  - CalendarEntryCommandHandlerSupport addRecurringCalendarEntry
     */
    @Override
    public RecurringEntry createRecurringEntryNonRota(String calendarId, final RecurringEntryDefinition entry) {
        return this.createRecurringEntryInternal(calendarId, entry, false);
    }

    /**
     * Get the correct handle for concrete and virtual (NoteOccurrence) recurrences
     */
    @Override
    public RecurringEntryHandle getEntryHandleFromRecurrenceHandle(RecurrenceHandle recurrenceHandle) {
        return getEntryHandleFromAnyHandle(recurrenceHandle.toString());
    }

    @Override
    public RecurringEntryHandle getSeriesHandleFromAnyHandle(String handle) {
        return getEntryHandleFromAnyHandle(handle);
    }

    @Override
    public RecurringEntryHandle getEntryHandleFromAnyHandle(String handle) {
        String ref = handle.contains(ModificationUid.RECURRENCEID_DELIMITER)
                ? StringUtils.substringBefore(handle, ModificationUid.RECURRENCEID_DELIMITER)
                : handle;
        return RecurringEntryHandle.fromString(ref);
    }


    /**
     * Called currently by:
     *  - no callee, testing only
     */
    @Override
    public Recurrence findFirstRecurrence(RecurringEntryHandle handle, Interval interval) {
        var items = findRecurrenceItems(handle, instantFromJoda(interval.getStart()), instantFromJoda(interval.getEnd()))
                .map(converter::itemToRecurrence)
                .sorted(Comparator.comparing(Recurrence::getStart));
        return items.findFirst().orElse(null);
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl.addDemandWithStatuses
     *  - /schedule/{eventRef}/confirmed RotaController.findRecurrenceItems
     *  - here.. allocateActivitiesInRange
     */
    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Range<Instant> interval, Recurrence.@Nullable Status status) {
        var items = findRecurrenceItems(handle, interval.lowerEndpoint(), interval.upperEndpoint());
        var unfiltered = items.map(converter::itemToRecurrence);
        return status == null ? unfiltered : unfiltered.filter(matchesStatus(status));
    }

    /**
     * Called currently by:
     *  - DemandSchedule removeRecurringCalendarEvent & allocateRecurrencesToResource
     *  - CalendarRecurringService findDroppedRecurrences
     *
     * Return events but not the master/parent entries - {@link CosmoCalendarRecurringService#findRecurrenceItems}.
     * This converts the internal items into Recurrence's.
     * TODO refactor with findRecurrences(calendarId, Interval)
     */
    @Override
    public Stream<Recurrence> findRecurrences(RecurringEntryHandle handle, Interval interval, final Recurrence.Status status) {
        var items = findRecurrenceItems(handle, ofEpochMilli(interval.getStart().getMillis()), ofEpochMilli(interval.getEnd().getMillis()));
        var unfiltered = items.map(converter::itemToRecurrence);
        return status == null ? unfiltered : unfiltered.filter(matchesStatus(status));
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl getRota, addRelevantRecurrences
     *  Note: Interval is specified as equiv to Range.closedOpen i.e. excludes end instant.
     */
    @Override
    public Stream<Recurrence> findRecurrencesFromCalendar(String calendarId, Interval interval) {
        // load the items - but not the recurring item (parent) itself, since INCLUDE_MASTER above is false
        // findItems returns a mix of concrete items (NoteItem) and recurrences (via processMasterNote calling createNoteOccurrence)
        // TODO performance - this is not great, only because we do a findItems excluding the master, only to load each master in convertItemsToRecurrences
        var items = findNoteItems(calendarId, interval);

        // Filter for recurring, as review dates seem to throw us: see ECCO-936
        // A 6w,13w review dates cycle would appear to have a master item, but not be recurring
        var recurringItems = items.filter(input -> {
            // EventStamp represents recurring and non-recurring calendar event, but a stamp may not exist for this item?
            EventStamp stamp = StampUtils.getEventStamp(input);
            if (stamp == null) {
                return true; // FIXME: Please stop my brain hurting... why is true the answer that makes things work?
            }
            return stamp.isRecurring();
        });

        return convertItemsToRecurrences(recurringItems);
    }

    // Used only in testing
    @Override
    public List<Recurrence> findRecurrencesList(String calendarId, Interval interval) {
        return findRecurrencesFromCalendar(calendarId, interval).collect(toList());
    }
    @Override
    public Set<Attendee> findRecurrencesListAttendees(String calendarId, Interval interval, String calendarIdResourceUserReferenceUri) {
        var events = findRecurrencesFromCalendar(calendarId, interval);
        // NB We force the attendees here to populate (by cloning the code), to help with the tests
        return events
                .map(Recurrence::getAttendees)
                .flatMap(Collection::stream)
                .filter(a -> calendarIdResourceUserReferenceUri.equals(a.getCalendarIdUserReferenceUri()))
                .collect(Collectors.toSet());
    }
    @Override
    @Deprecated
    public Set<Attendee> findRecurrenceAttendees(Recurrence recurrence) {
        return recurrence.getAttendees();
    }
    // Used only in testing

    @Override
    public RecurringEntryHandle getRecurringEntrySeriesHandle(RecurringEntryHandle recurringEntryHandle) {
        return recurringEntryHandle;
    }

    @Override
    public void clearRelevantConcreteAndExDateEntries(RecurringEntryHandle newEntry, RecurringEntryHandle oldEntry) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(newEntry.toString());
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(item);
        this.clearRelevantConcreteAndExDateEntries(oldEntry, stamp.getStartDate().toInstant());
    }

    @Override
    public void transferRelevantConcreteAndExDateEntries(RecurringEntryHandle newEntry, RecurringEntryHandle oldEntry) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(newEntry.toString());
        final NoteItem oldItem = (NoteItem) contentService.findItemByUid(oldEntry.toString());
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(item);

        // clear rota appts in the old series
        messageBus.publishNow(new RotaDemandPreChangeEvent(this, stamp.getStartDate().toInstant(), oldEntry));

        // the copy's recurrence rule is updated to begin at the detachment
        // point (or the next occurrence after the detachment point, if that is
        // a modification)
        // NB called 'detachOccurrences' to keep as much cosmo as possible
        this.detachOccurrence(oldItem, item, new net.fortuna.ical4j.model.DateTime(stamp.getStartDate()));

    }

    @Override
    public void clearRelevantConcreteAndExDateEntries(RecurringEntryHandle entry, Instant from) {
        messageBus.publishNow(new RotaDemandPreChangeEvent(this, from, entry));
        final NoteItem item = (NoteItem) contentService.findItemByUid(entry.toString());
        this.detachOccurrence(item, null, new net.fortuna.ical4j.model.DateTime(Date.from(from)));
    }

    // COPIED FROM COSMO - cosmo-core DetachedItemCollectionAdapter#detachOccurrence (eg, at 36fe4729)
    // detaches on occurrence from a recurrence master:
    // - master is the original recurring item
    // - copy is a copy of the master with changes applied
    // - occurrence is the "detachment point"; may be a modification (in which **
    //   case it has its own changes to the original master which don't apply **
    //   to the copy) or an occurrence
    // * all modifications of the original master that occur after the
    //   detachment point are moved to the copy
    // * the recurrence rule of the master is updated to end at the latest
    //   occurrence before the detachment point
    // * the copy's recurrence rule is updated to begin at the detachment
    //   point (or the next occurrence after the detachment point, if that is
    //   a modification)
    // * the copy is added to all of the master's collections / parents
    private NoteItem detachOccurrence(NoteItem master, @Nullable NoteItem copy, net.fortuna.ical4j.model.Date lastOccurrenceDate) {

        // can't expand what doesn't exist
        if (master == null) {
            return null;
        }

        ThisAndFutureHelper tafHelper = new ThisAndFutureHelper();
        var updates = new LinkedHashSet<ContentItem>();

        // need to update master, create copy
        updates.add(master);

        // keep here to preserve the order of the 'add', just in case
        if (copy != null) {
            updates.add(copy);
        }

        // get all modifications to save (remove/add)
        // the old 'master' turns them off - setIsActive false)
        updates.addAll(tafHelper.breakRecurringEvent(master, copy, lastOccurrenceDate, master.getParents()));

        // transfer relevant exception dates that match the new/copy schedule
        // and remove them from the master
        // this directly adds to the stamp, not needed on 'updates'
        if (copy != null) {
            transferExDate(master, copy);
        } else {
            clearExDatesFrom(master, lastOccurrenceDate);
        }

        // This service call will update/remove/create items in one transaction
        // Any new items will be added to all specified parents.
        // updates are those that have been modified
        // master.getParents != updates[3] ((NoteItem) content).getModifies().getParents();

        // collects the updates, where new items have no creationDate, so it calls:
        //      this.contentDao.createContent(parents, content);
        //      which checks each 'content' .getModifies parents equals 'parents'
        //      if (!note.getModifies().getParents().equals(parents))
        //      BUT this means we're checking the modification parents, not our own??
        // SO - we either want to expose addParent for ThisAndFutureHelper
        //      OR we add into createContent
        //      BUT the createContent does add the 'parents' to the content.addParent()
        //      BUT we're just not sure that is even what we want!?

        // getParents gets the maters 'cosmo_collection_item' and returns the collections
        // this appears to be a blanket-update for everything in the master to the copy,
        // when surely we need to do this on a per-event basis?

        // getParents will be one, the client
        contentService.updateContentItems(master.getParents(), updates);

        return copy;
    }

    private void clearExDatesFrom(NoteItem master, Date from) {
        final EventStamp oldStamp = StampUtils.getEventStamp(master);
        if (oldStamp.getExceptionDates() == null || oldStamp.getExceptionDates().size() == 0) {
            return;
        }
        final DateList oldExDates = oldStamp.getExceptionDates();
        DateList oldExDatesRemaining = new DateList();
        oldExDates.forEach(ex -> {
            if (ex.before(from)) {
                oldExDatesRemaining.add(ex);
            }
        });

        oldStamp.setExceptionDates(oldExDatesRemaining);
    }

    private void transferExDate(NoteItem master, NoteItem copy) {
        final EventStamp oldStamp = StampUtils.getEventStamp(master);
        if (oldStamp.getExceptionDates() == null || oldStamp.getExceptionDates().size() == 0) {
            return;
        }

        // ecco-feature: we want to transfer exdate also
        final EventStamp newStamp = StampUtils.getEventStamp(copy);
        final DateList oldExDates = oldStamp.getExceptionDates();

        // If modification matches an occurrence in the new series
        // then add it to the list
        DateList newExDates = new DateList();
        DateList oldExDatesRemaining = new DateList();
        RecurrenceExpander expander = new RecurrenceExpander();
        EventStamp newEvent = StampUtils.getEventStamp(copy);
        Calendar newEventCal = newEvent.getEventCalendar();
        oldExDates.forEach(ex -> {
            if (expander.isOccurrence(newEventCal, ex)) {
                newExDates.add(ex);
            } else {
                oldExDatesRemaining.add(ex);
            }
        });

        newStamp.setExceptionDates(newExDates);
        oldStamp.setExceptionDates(oldExDatesRemaining);
    }

    /**
     * Return events but not the master/parent entries - so
     *      "occurrences and modifications of a recurring event, but not the master event item."
     *      See EventStampFilter.
     */
    private Stream<Item> findRecurrenceItems(RecurringEntryHandle handle, Instant startInclusive, Instant endExclusive) {
        EventStampFilter eventFilter = new EventStampFilter();
        eventFilter.setTimeRange(new Date(startInclusive.toEpochMilli()), new Date(endExclusive.toEpochMilli()));
        eventFilter.setExpandRecurringEvents(true);

        NoteItemFilter itemFilter = new NoteItemFilter();
        itemFilter.setIcalUid(new EqualsExpression(handle.toString())); // Recurrences all share a UID
        itemFilter.getStampFilters().add(eventFilter);
        itemFilter.setFilterProperty(EventStampFilter.PROPERTY_INCLUDE_MASTER_ITEMS, "false"); // Only return the expanded events

        return contentService.findItems(itemFilter).stream();
    }

    private Stream<Item> findConcreteRecurrenceItems(RecurringEntryHandle handle) {
        EventStampFilter eventFilter = new EventStampFilter();
        // we don't want to supply an end date, so we just return everything to be filtered
        //eventFilter.setTimeRange(start, end);
        eventFilter.setExpandRecurringEvents(false);

        NoteItemFilter itemFilter = new NoteItemFilter();
        itemFilter.setIcalUid(new EqualsExpression(handle.toString())); // Recurrences all share a UID
        itemFilter.getStampFilters().add(eventFilter);
        // Can't use PROPERTY_INCLUDE_MASTER_ITEMS as it is ignored without a time range filter.
        //itemFilter.setFilterProperty(EventStampFilter.PROPERTY_INCLUDE_MASTER_ITEMS, "false"); // Only return the expanded events

        return contentService.findItems(itemFilter).stream()
                .filter(input -> StampUtils.getEventExceptionStamp(input) != null);
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl resetRecurrencesFrom / deallocateActivitiesFrom
     *
     * Find concrete items - which are modifications to the recurring schedule.
     * TODO performance: this finds all occurrences as there is no date range
     */
    @Override
    public Stream<Recurrence> findModifiedRecurrences(RecurringEntryHandle handle, LocalDate equalOrAfter) {
        var items = findConcreteRecurrenceItems(handle);
        return convertItemsToRecurrences(items)
                .filter(input -> !(new LocalDate(input.getStart()).isBefore(equalOrAfter)));
    }

    @NonNull
    private Stream<Recurrence> convertItemsToRecurrences(Stream<Item> recurringItems) {
        return recurringItems.map(item -> {
            final NoteItem noteItem = (NoteItem) item;
            // TODO do we need to check for the modification having an eventStamp - see cosmo-core ThisAndFutureHelper#getModificationsToMove (event==null)
            /*EventExceptionStamp event = StampUtils.getEventExceptionStamp(noteItem);
            if (event == null) {
                return null;
            }*/
            // If modifies is null and we're not a recurrence (NoteOccurrence), then we must be a recurring entry and so use the
            // iCal uid (which should be shared across all recurrences and the parent) to query for the event.
            if (!(noteItem instanceof NoteOccurrence) && noteItem.getModifies() == null) {
                // Need to find the recurring entry this relates to; it must be on someone else's calendar?
                // If it conforms with the iCal spec, it will share an iCal UID.
                // without 'getRecurringEntry' here we fail 'shouldFindRecurrencesFromEventWithMasterInOtherCalendar'
                return converter.itemToRecurrence(noteItem, getRecurringEntryByIcalUid(noteItem.getIcalUid()).getHandle());
            }
            // If modifies is null and we're a recurrence (we're a NoteOccurrence).
            // If modifies is not null, then its a concrete recurrence with a link to the parent recurring entry via modifies.uid.
            return converter.itemToRecurrence(noteItem);
        });
    }

    // NB is this the same as getRecurringEntry?
    private RecurringEntry getRecurringEntryByIcalUid(String icalUid) {
        NoteItemFilter itemFilter = new NoteItemFilter();
        itemFilter.setIcalUid(new EqualsExpression(icalUid));
        itemFilter.setIsModification(false);

        final Set<Item> items = contentService.findItems(itemFilter);
        if (items.isEmpty()) {
            throw new CalendarException("No entry with ical uid " + icalUid + " exists.");
        }

        NoteItem item = (NoteItem) items.iterator().next();
        return converter.itemToRecurringEntry(item);
    }

    /**
     * Called currently by:
     *  - no callee, test only
     */
    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Interval interval) {
        return findRecurrenceExceptions(handle, Range.closedOpen(ofEpochMilli(interval.getStartMillis()), ofEpochMilli(interval.getEndMillis())));
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl addDemandWithStatuses
     */
    @Override
    public Stream<Recurrence> findRecurrenceExceptions(RecurringEntryHandle handle, Range<Instant> interval) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(handle.toString());
        if (item == null) {
            throw new CalendarException("No entry with handle " + handle + " exists."); // Check appointmentschedule for e.g. startDate > endDate
        }
        final EventStamp eventStamp = StampUtils.getEventStamp(item);
        final DateList exDates = eventStamp.getExceptionDates();
        if (exDates == null || exDates.isEmpty()) {
            return Stream.empty();
        }

        // TODO: For completeness, we ought to check that these dates would be expanded from the recurrence rule before returning them as dropped recurrences.
        // But for now, let's just assume they would as that's the only way they could be created.
        final Date startDate = new Date(interval.lowerEndpoint().toEpochMilli());
        final Date endDate = new Date(interval.upperEndpoint().toEpochMilli());
        return exDates.stream()
                .filter(
                        input -> !input.before(startDate) && input.before(endDate) // i.e. in range excluding end
                )
                .map(droppedDateToRecurrenceForItem(item, converter));
    }

    /**
     * Called currently by:
     *  - DemandSchedule entryHandle
     */
    @Override
    public RecurringEntry createRecurringEntry(String calendarId, final RecurringEntryDefinition entry) {
        return this.createRecurringEntryInternal(calendarId, entry, true);
    }

    private RecurringEntry createRecurringEntryInternal(String calendarId, final RecurringEntryDefinition entry, boolean asRota) {
        NoteItem noteItem = entityFactory.createNote();
        noteItem.setName(entry.getTitle());
        noteItem.setDisplayName(entry.getTitle());
        noteItem.setBody(entry.getDescription());
        converter.setRecurringItemEventStamp(noteItem, entry, asRota); // create a TENTATIVE event (if rota)
        try {
            CollectionItem calendar = findCalendar(calendarId);
            noteItem.setOwner(calendar.getOwner());
            noteItem = (NoteItem) contentService.createContent(calendar, noteItem);
            if (!asRota) {
                // as per as-was ItemIntegrationAdvice.postEntry - createContent is followed by syncAttendees
                CosmoHelper.syncAttendeesWithCalendars(noteItem, entityUriMapper);
                contentService.updateContent(noteItem);
            }
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not add entry " + entry, e);
        }
        return converter.itemToRecurringEntry(noteItem);
    }

    /**
     * Called currently by:
     *  - DemandSchedule entryHandleManagedBy
     */
    @Override
    public void updateRecurringEntryManagedBy(RecurringEntryHandle handle, final URI managedByUri) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(handle.toString());
        converter.setManagedBy(item, managedByUri);
    }

    /**
     * Called currently by:
     *  - DemandSchedule verify (on calling truncate)
     *  - CalendarEntryCommandHandlerSupport updateRecurringEntryBounds
     * NB validation is also done in these (above) methods
     */
    @Override
    public void updateRecurringEntryBoundsEnd(RecurringEntryHandle handle, LocalDate endDate) {
        this.updateRecurringEntryBounds(handle, null, endDate, true);
    }

    /**
     * Called by moveToStartOfUpperBound, part of series calendar calculations
     */
    @Override
    public void updateRecurringEntryBoundsStart(RecurringEntryHandle handle, LocalDate startDate) {
        this.updateRecurringEntryBounds(handle, startDate, null, false);
    }

    @Override
    public void updateRecurringEntryBounds(RecurringEntryHandle handle, @Nullable LocalDate startDate, @Nullable LocalDate endDate) {
        updateRecurringEntryBounds(handle, startDate, endDate, true);
    }

    @Override
    public void updateRecurringEntryBounds(RecurringEntryHandle handle, @Nullable LocalDate startDate, @Nullable LocalDate endDate, boolean setEndDate) {
        final NoteItem item = (NoteItem) contentService.findItemByUid(handle.toString());
        if (item == null) {
            throw new CalendarException("No entry with handle " + handle + " exists.");
        }
        final BaseEventStamp stamp = StampUtils.getBaseEventStamp(item);
        final LocalDate previousStartDate = new DateTime(stamp.getStartDate()).toLocalDate();
        final Date previousEndDateTime = stamp.getRecurrenceRules().get(0).getUntil();
        final LocalDate previousEndDate = previousEndDateTime != null ? new DateTime(previousEndDateTime).toLocalDate() : null;
        startDate = startDate != null ? startDate : previousStartDate;
        endDate = setEndDate ? endDate : endDate != null ? endDate : previousEndDate;

        // Check for concrete recurrences derived from recurrences outside the new bounds.
        // These will be modified entries against the schedule, and therefore we would be deleting user data.
        // If an end date is null, then we are extending the bounds, so there are none outside the new bounds
        if (startDate.isAfter(previousStartDate) || endDate != null && previousEndDate == null || endDate != null && endDate.isBefore(previousEndDate)) {

            // clear any snapshots also - from start, but we could also restrict to endDate
            messageBus.publishNow(new RotaDemandPreChangeEvent(this, JodaToJDKAdapters.localDateToJDk(startDate).atStartOfDay(EccoTimeUtils.LONDON).toInstant(), handle));

            var recurrences = findConcreteRecurrenceItems(handle);

            LocalDate finalStartDate = startDate;
            LocalDate finalEndDate = endDate;
            recurrences.forEach(recurrence -> {
                var exceptionStamp = StampUtils.getEventExceptionStamp(recurrence);
                final LocalDate recurrenceId = new LocalDate(exceptionStamp.getRecurrenceId()); // recurrenceId is actually a date!
                if (recurrenceId.isBefore(finalStartDate) || recurrenceId.isAfter(finalEndDate)) {

                    log.info("Removing recurrence: {}. May fail if we have recurrenceRules here: {}",
                            recurrenceId,
                            exceptionStamp.getRecurrenceRules().stream().map(Recur::toString).collect(joining(", ")));

                    removeRecurrence((NoteItem) exceptionStamp.getItem());
                }
            });
        }

        // Remove any exception dates falling outside the new bounds
        // These are exclusion dates to the recurrence rule (non-concrete exceptions).
        if (stamp.getExceptionDates() != null && !stamp.getExceptionDates().isEmpty()) {
            final DateList exDates = stamp.getExceptionDates() != null? stamp.getExceptionDates() : new DateList();
            for (var iterator = exDates.iterator(); iterator.hasNext(); ) {
                final LocalDate recurrenceId = new LocalDate(iterator.next());
                if (recurrenceId.isBefore(startDate) || (endDate != null && recurrenceId.isAfter(endDate))) {
                    iterator.remove();
                }
            }
            stamp.setExceptionDates(exDates);
        }

        // Update the bounds.
        final DateTime rescheduledStartDate = startDate.toDateTime(new DateTime(stamp.getStartDate()).toLocalTime());
        final var duration = stamp.getDuration();
        stamp.setStartDate(new net.fortuna.ical4j.model.DateTime(rescheduledStartDate.toDate()));
        stamp.setDuration(duration); // Keep the length the same as it was originally.
        var rules = stamp.getRecurrenceRules();
        rules.get(0).setUntil(endDate == null ? null : new net.fortuna.ical4j.model.DateTime(endDate.toDateTime(rescheduledStartDate.toLocalTime()).toDate()));
        contentService.updateContent(item);
    }

    /** Remove from parent calendars and remove this recurrence */
    private void removeRecurrence(NoteItem recurrence) {
        // Remove it from calendars as required
        for (var calendar : recurrence.getParents()) {
            ((HibItem) recurrence).removeParent(calendar);
        }
        // Update it to tentative with attendees matching the calendars
        CosmoHelper.syncAttendeesWithCalendars(recurrence, Status.VEVENT_TENTATIVE, PartStat.TENTATIVE, entityUriMapper);
        contentService.removeContent(recurrence);
    }

    /**
     * Called currently by:
     *  - no callee
     *  - here..
     *  TODO should be used by CalendarEntryCommandHandlerSupport
     */
    @Override
    public void confirmRecurringEntry(RecurringEntryHandle recurringEntryHandle, Recurrence.Status eventStatus,
                                      Attendee.Status attendeeStatus, URI updatedBy, String... calendarIds) {
        // safeguard against calendarIds=[null] which is possible with the new 'advisor' allocation in the ui
        if (calendarIds == null || calendarIds.length == 0 || (calendarIds.length == 1 && calendarIds[0].equals(null))) {
            return;
        }
        // Find the recurrence
        NoteItem recurringEntry = (NoteItem) contentService.findItemByUid(recurringEntryHandle.toString());
        Assert.notNull(recurringEntry, "could not find recurrence for handle:" + recurringEntryHandle);

        // update the updatedBy
        converter.setUpdatedBy(recurringEntry, updatedBy);

        // Add it ready to apply to all the other calendars [item (many)<->(many) calendar]
        for (String calendarId : calendarIds) {
            ((HibItem) recurringEntry).addParent(findCalendar(calendarId));
        }
        // Update the attendees matching the calendars (without setting any status)
        // addParent above is used to add attendees to the rota
        Status icalEventStatus = eventStatus != null && eventStatus.equals(Recurrence.Status.CONFIRMED) ? Status.VEVENT_CONFIRMED : null;
        PartStat icalAttendeeStatus = attendeeStatus != null && attendeeStatus.equals(Attendee.Status.ACCEPTED) ? PartStat.ACCEPTED : null;
        CosmoHelper.syncAttendeesWithCalendars(recurringEntry, icalEventStatus, icalAttendeeStatus, entityUriMapper);
        contentService.updateContent(recurringEntry);
    }

    // mimics unConfirmRecurrence, but for an entry
    @Override
    public void unConfirmRecurringEntry(RecurringEntryHandle recurringEntryHandle, URI updatedBy, String... calendarIds) {
        NoteItem recurringEntry = (NoteItem) contentService.findItemByUid(recurringEntryHandle.toString());
        Assert.notNull(recurringEntry, "could not find recurrence for handle:" + recurringEntryHandle);

        // update the updatedBy
        converter.setUpdatedBy(recurringEntry, updatedBy);

        // Remove it from calendars as required
        for (String calendarId : calendarIds) {
            ((HibItem) recurringEntry).removeParent(findCalendar(calendarId));
        }
        CosmoHelper.syncAttendeesWithCalendars(recurringEntry, Status.VEVENT_TENTATIVE, PartStat.TENTATIVE, entityUriMapper);
        contentService.updateContent(recurringEntry);
    }

    /**
     * Called currently by:
     *  - DemandSchedule.delete
     *  NB The handlers actually call 'drop' for non-rota recurring entries, with non-recurring using older style persistAndSync.
     */
    @Override
    public void deleteRecurringEntry(RecurringEntryHandle handle, boolean checkNotAllocated) {
        try {
            if (checkNotAllocated) {
                RecurringEntry entry = getRecurringEntry(handle);
                // fail early when found allocated appointments, but adjustments (concrete but unallocated) get deleted
                findRecurrences(handle, new Interval(entry.getStart(),
                                entry.getScheduleEndDate().toDateTimeAtStartOfDay().plusDays(1)),
                        Recurrence.Status.CONFIRMED)
                        .findFirst().ifPresent(recurrence -> {
                    throw new IllegalStateException("schedule has confirmed recurrences and so cannot be deleted - first one is: " + recurrence.getRecurrenceHandle());
                });
            }
            contentService.removeItem(contentService.findItemByUid(handle.toString()));
        } catch (DataAccessException e) {
            logDebugMsgAndThrow("Could not delete entry with id " + handle, e);
        }
    }

    @Override
    public void recreateRecurrencesInRange(RecurringEntry.RecurringEntryHandle recurringEntryHandle, @NonNull Range<Instant> range, URI updatedBy) {
        // see CalendarRecurringSeriesDecorator
    }

    /**
     * Called currently by:
     *  - AppointmentRecurringActionCommandHandler allocateAppointments (rota)
     *  - RotaServiceImpl addOneAppointmentSchedule
     *
     *
     * Allocate activities to a resource, possibly rescheduling them.
     * This turns a recurring entry into concrete recurrences with the resource as the attendee. It also
     * reschedules each recurrence if required (ensuring the reschedule does not exceed the rescheduleBounds).
     */
    @Override
    public int confirmRecurrencesInRange(RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                         @Nullable DaysOfWeek matchDays, @Nullable URI updatedBy,
                                         @Nullable Integer allocateRescheduledMins, Range<LocalDate> rescheduleBounds,
                                         String allocateToResourceCalendarId) {

        AtomicInteger count = new AtomicInteger();
        Stream<Recurrence> recurrences = findRecurrences(recurringEntryHandle, range, Recurrence.Status.TENTATIVE);
        recurrences.forEach(r -> {
            var matchRecurrence = matchDays == null || matchDays.isCalendarDayISO(r.getStart().dayOfWeek().get());
            if (matchRecurrence) {
                if (allocateRescheduledMins != null) {
                    var adjustedStartJoda = r.getStart().withFieldAdded(DurationFieldType.minutes(), allocateRescheduledMins);
                    java.time.LocalDateTime adjustedStart = JodaToJDKAdapters.dateTimeToJdk(adjustedStartJoda).toLocalDateTime();
                    rescheduleRecurrence(r.getRecurrenceHandle(), null, localDateTimeToJoda(adjustedStart), null, updatedBy, rescheduleBounds);
                    entityManager.flush(); // Otherwise we end up allocating at the old start time
                }

                confirmRecurrence(r.getRecurrenceHandle(), updatedBy, allocateToResourceCalendarId);

                count.getAndIncrement();
            }
        });
        return count.get();
    }

    /**
     * Deallocate all concrete entries in the schedule - which could be single or recurring allocations.
     * Ideally we leave user modified events alone (via some MANAGED_BY_PROPERTY_NAME for system modified ones), but
     * we can also ask users to use ad-hoc for specifics they want to remain allocated - this schedule is being amended.
     * see ServiceRecipientAppointmentScheduleCommandHandler#resetRecurrences
     * NB Choosing all concrete instances avoids any weird logic on checking things upto a year, when end dates etc can change.
     *
     * @return number of deallocations
     */
    @Override
    public int unConfirmRecurrencesInRange(RecurringEntryHandle recurringEntryHandle, Range<Instant> range,
                                           @Nullable DaysOfWeek matchDays, URI updatedBy,
                                           String deallocateFromResourceCalendarId) {
        AtomicInteger count = new AtomicInteger();
        // NB legacy implementation just gets all applicableFrom without an upper boundary
        // NB also this doesn't do aysnc
        var applicableFromJoda = JodaToJDKAdapters.localDateToJoda(range.lowerEndpoint().atZone(ZoneId.of("UTC")).toLocalDate());
        // we only have one schedule time per day, so find anything on the same day or after
        var entries = findModifiedRecurrences(recurringEntryHandle, applicableFromJoda);
        entries.forEach(recurrence -> {
            count.getAndIncrement();
            // TODO if <= today check for evidence, as safe to assume after today has no evidence
            //UUID workUuid = supportWorkRepository.findUuidByEventId(recurrence.getHandle().toString());
            //if (workUuid != null) {
            //    throw new IllegalStateException("Cannot split a schedule which has evidence attached: " + workUuid.toString());
            //}
            unconfirmRecurrence(recurrence.getRecurrenceHandle(), updatedBy,true, deallocateFromResourceCalendarId);
        });
        return count.get();
    }

    /**
     * Called currently by:
     *  - RotaService rescheduleActivity & allocateAndMaybeReschedule
     */
    @Override
    public void rescheduleRecurrence(RecurrenceHandle recurrence, @Nullable String newTitle, @Nullable LocalDateTime newDateTime,
                                     @Nullable Integer newDurationMins, URI updatedBy, @Nullable Range<LocalDate> recurrenceBounds) {
        if (newDateTime == null && newDurationMins == null && newTitle == null) {
            throw new IllegalArgumentException("One argument (start time or duration or title) must be provided in order to reschedule");
        }
        if (newDateTime != null) {
            final LocalDate newDate = newDateTime.toLocalDate();
            assert recurrenceBounds != null;
            if (recurrenceBounds.lowerEndpoint().isAfter(newDate) || (recurrenceBounds.hasUpperBound() && recurrenceBounds.upperEndpoint().isBefore(newDate))) {
                // If you ever relax this restriction, you need to think about how to reimplement addUnallocatedActivities().
                throw new IllegalArgumentException("Cannot reschedule an activity outside of the date range of the appointment schedule: " +
                        recurrenceBounds.lowerEndpoint().toString() + " to " + recurrenceBounds.upperEndpoint().toString());
            }
        }

        try {
            DateTime newDateTimeTz = newDateTime != null ? newDateTime.toDateTime() : null;
            editRecurrence(recurrence, newTitle, newDateTimeTz, newDurationMins, updatedBy);
        } catch (CalendarException cal) {
            throw new RotaException("Could not reschedule activity " + recurrence + " to " + (newDateTime != null ? newDateTime.toString() : null));
        }
    }

    /**
     * Called currently by:
     *  - DemandSchedule allocateRecurrencesToResource
     *  - RotaServiceImpl allocateAndMaybeReschedule
     *
     * Sets the recurrence status to confirmed and add it to the resource's calendar too.
     * If the recurrence is not concrete (i.e. was from a NoteOccurrence) then it needs to be created in the client calendar as well.
     * Will also add attendee(s) to the recurrence based on the calendar owner(s)
     */
    @Override
    public void confirmRecurrence(RecurrenceHandle recurrenceHandle, URI updatedBy, String... calendarIds) {
        // Find the recurrence
        NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        Assert.notNull(recurrence, "could not find recurrence for handle:" + recurrenceHandle);
        recurrence = ensureConcreteRecurrence(recurrence, updatedBy);
        // Add it ready to apply to all the other calendars [item (many)<->(many) calendar]
        for (String calendarId : calendarIds) {
            ((HibItem) recurrence).addParent(findCalendar(calendarId));
        }
        // Update the event to confirmed with attendees as accepted
        // addParent above is used to add attendees to the rota
        CosmoHelper.syncAttendeesWithCalendars(recurrence, Status.VEVENT_CONFIRMED, PartStat.ACCEPTED, entityUriMapper);
        contentService.updateContent(recurrence);
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl deallocateActivitiesFrom
     *  - AppointmentActionCommandHandler deallocateAppointment
     * @param reset false (update) if single deallocate, true (delete) if unconfirmRecurrenceInRange is used
     *      TODO - clarify reset
     */
    @Override
    public void unconfirmRecurrence(RecurrenceHandle recurrenceHandle, URI updatedBy, boolean reset, String... calendarIds) {
        // Find the recurrence
        final NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        if (recurrence instanceof NoteOccurrence) {
            log.warn("Recurrence being unconfirmed was not concrete anyway: " + recurrenceHandle);
            return;
        }
        // Remove it from calendars as required
        for (String calendarId : calendarIds) {
            ((HibItem) recurrence).removeParent(findCalendar(calendarId));
        }

        converter.setUpdatedBy(recurrence, updatedBy);

        // Update the event to tentative with attendees as tentative
        CosmoHelper.syncAttendeesWithCalendars(recurrence, Status.VEVENT_TENTATIVE, PartStat.TENTATIVE, entityUriMapper);
        if (reset) {
            contentService.removeContent(recurrence);
        } else {
            contentService.updateContent(recurrence);
        }
    }

    /**
     * Called currently by:
     *  - CalendarEntryCommandHandlerSupport updateRecurringCalendarEntry
     *  - here.. rescheduleActivity
     */
    @Override
    public void editRecurrence(RecurrenceHandle recurrenceHandle, @Nullable String title,
                               @Nullable DateTime rescheduledTime, @Nullable Integer durationMins,
                               URI updatedBy) {
        // Find the recurrence
        NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        recurrence = ensureConcreteRecurrence(recurrence, updatedBy);
        // Update the start date
        final EventExceptionStamp stamp = (EventExceptionStamp) recurrence.getStamp(EventExceptionStamp.class);
        final var duration = durationMins != null ? java.time.Duration.of(durationMins, ChronoUnit.MINUTES)
                : stamp.getDuration();
        final Date msDate = rescheduledTime != null ? rescheduledTime.toDate() : stamp.getStartDate();
        stamp.setStartDate(new net.fortuna.ical4j.model.DateTime(msDate));
        stamp.setDuration(duration); // Ensure we set the duration to override the old end date.
        // iCalendar description or summary could be used
        // we set the NoteItem.setDisplayName which goes to the database through HibItem.displayName
        // (as per createRecurringEntry above) which is the "Item's human readable name" - although it also uses setName
        // we load into RotaAppointmentViewModel which can use entry.getTitle() from NoteItemRecurrenceAdapter.getTitle()
        // which itself uses noteItem.getDisplayName() (and nothing on the event)
        final String description = title != null ? title : recurrence.getDisplayName();
        // we don't need to set the name, and could be useful to leave as the type (eg one-to-one)
        //recurrence.setName(description);
        recurrence.setDisplayName(description);

        contentService.updateContent(recurrence);
    }

    /**
     * Called currently by:
     *  - SupportCommentCommandHandler
     */
    @Override
    public void ensureConcreteRecurrence(RecurrenceHandle recurrenceHandle, URI updatedBy) {
        NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        ensureConcreteRecurrence(recurrence, updatedBy);
    }

    private NoteItem ensureConcreteRecurrence(NoteItem recurrence, URI updatedBy) {
        if (recurrence instanceof NoteOccurrence) {
            NoteOccurrence noteOccurrence = (NoteOccurrence) recurrence;
            // There isn't a real entry for this, so we will need to create one now.
            final NoteItem recurringEntry = noteOccurrence.getMasterNote();
            recurrence = (NoteItem) recurringEntry.copy(); // performs setOwner using getOwner (which is the user)
            // Swap the event stamp for an event exception stamp
            recurrence.removeStamp(StampUtils.getEventStamp(recurrence));
            final EventExceptionStamp stamp = entityFactory.createEventExceptionStamp(recurrence);
            // Need to copy Calendar, and indexes
            try {
                stamp.setEventCalendar(new Calendar(StampUtils.getEventStamp(recurringEntry).getEventCalendar()));
            } catch (Exception e) {
                throw new RuntimeException("Cannot copy calendar", e);
            }
            stamp.setRecurrenceId(noteOccurrence.getModificationUid().getRecurrenceId());
            // Set the start date to match the recurrence ID, and keep the duration off the master
            stamp.setStartDate(stamp.getRecurrenceId());
            stamp.setDuration(StampUtils.getEventStamp(recurringEntry).getDuration());
            // Make sure the recurrence isn't itself recurring (no rules or dates)
            stamp.setRecurrenceRules(Collections.emptyList());
            stamp.getEvent().getProperties(Property.EXDATE).clear();
            stamp.setExceptionDates(new DateList());
            stamp.setRecurrenceDates(new DateList());
            recurrence.addStamp(stamp);
            recurrence.setUid(noteOccurrence.getModificationUid().toString());
            recurrence.setName(recurrence.getUid());
            recurrence.setModifies(recurringEntry);

            // NB this did get the first parent as ownerParentCollection below, which is a user's calendarId from the master
            // because it was assumed there was only one on the master until its made concrete and allocated to a resource
            // however, the option now is to have allocate to the master, we need another approach - either we have to
            // to ask via an external service, or we find the parent of the owner which is used in '.copy' a few above
            // but in doing so we stumble upon a problem of getting the parent, which is solved in getOwnerParent.

            // add one parent - this is not the owner (user) as per .copy a few lines above, but the owner's calendar/collectionItem
            var ownerParentCollection = CosmoCalendarService.getOwnerParent(recurringEntry);
            recurrence = (NoteItem) contentService.createContent(ownerParentCollection, recurrence);
        }

        // Update the concrete recurrence with the latest action.
        // NB updateRecurringEntryManagedBy is called on DemandSchedule to set the MANAGED_BY_PROPERTY_NAME on the recurring entry.
        // This is like a 'updateRecurrenceUpdatedBy' method which sets the UPDATED_BY_PROPERTY_NAME that is updating a recurrence.
        // e.g. X-ECCO-UPDATED-BY:entity://AppointmentRecurringActionCommand/{uuid}
        converter.setConcreteUpdatedBy(recurrence, updatedBy);

        return recurrence;
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl resetRecurrencesFrom
     */
    @Override
    public void resetRecurrence(RecurrenceHandle recurrenceHandle) {
        NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        contentService.removeContent(recurrence);
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl dropActivity (rota)
     *   - CalendarEntryCommandHandlerSupport removeRecurringCalendarEntry (non rota)
     * The result in the database is that cosmo_event_stamp has icaldata with the EXDATE's in it.
     */
    @Override
    public void dropRecurrence(RecurrenceHandle recurrenceHandle) {
        final NoteItem recurrence = (NoteItem) contentService.findItemByUid(recurrenceHandle.toString());
        Assert.notNull(recurrence, "No recurrence found for supplied recurrenceHandle");
        final NoteItem recurringEntry;
        final net.fortuna.ical4j.model.Date recurrenceId;
        if (recurrence instanceof NoteOccurrence) {
            recurringEntry = ((NoteOccurrence) recurrence).getMasterNote();
            recurrenceId = ((NoteOccurrence) recurrence).getModificationUid().getRecurrenceId();
        } else {
            final EventExceptionStamp exceptionStamp = StampUtils.getEventExceptionStamp(recurrence);
            recurrenceId = exceptionStamp.getRecurrenceId();
            contentService.removeContent(recurrence); // does a flush
            recurringEntry = recurrence.getModifies();
        }
        final EventStamp stamp = StampUtils.getEventStamp(recurringEntry);
        final DateList exDates = stamp.getExceptionDates() != null? stamp.getExceptionDates() : new DateList();
        exDates.add(recurrenceId);
        stamp.setExceptionDates(exDates);
        contentService.updateContent(recurringEntry);
    }

    /**
     * Called currently by:
     *  - RotaServiceImpl reinstateActivity
     */
    @Override
    public void reinstateRecurrence(RecurrenceHandle recurrenceHandle) {
        final ModificationUid uid = new ModificationUid(recurrenceHandle.toString());
        final NoteItem recurringEntry = (NoteItem) contentService.findItemByUid(uid.getParentUid());
        final Date recurrenceId = uid.getRecurrenceId();

        final EventStamp stamp = StampUtils.getEventStamp(recurringEntry);
        final DateList exDates = stamp.getExceptionDates() != null? stamp.getExceptionDates() : new DateList();
        exDates.remove(recurrenceId);
        stamp.setExceptionDates(exDates);
        contentService.updateContent(recurringEntry);
    }

}
