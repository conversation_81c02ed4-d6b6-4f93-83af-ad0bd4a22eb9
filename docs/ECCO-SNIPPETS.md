
Liquibase Offline
=================
Caused by: liquibase.exception.ChangeLogParseException: Error parsing line 5 column 139 of classpath:sql/2023/2023-onwards-changeLog.xml: schema_reference.4: Failed to read schema document 'http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd', because 1) could not find the document; 2) the document could not be read; 3) the root element of the document is not <xsd:schema>.
    at liquibase.parser.core.xml.XMLChangeLogSAXParser.parseToNode(XMLChangeLogSAXParser.java:120) ~[liquibase-core-3.10.3.jar:?]
    at liquibase.parser.core.xml.AbstractChangeLogParser.parse(AbstractChangeLogParser.java:15) ~[liquibase-core-3.10.3.jar:?]

https://forum.liquibase.org/t/failed-to-read-schema-document-http-www-liquibase-org-xml-ns-dbchangelog-dbchangelog-3-5-xsd/3716/11
    Using xsd version which is present in the liquibase-core jar liquibase.parser.core.xml package.
    Example: In case your liquibase-core version is 3.5.5 make sure to use xsd of 3.5 version. Using xsd of higher then 3.5 will cause this issue as it is not available in the jar.

A similar error also occurs if the schema URI is specified as https instead of http.

 * **Correct example**: `xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"`
 * **Incorrect example**: `xsi:schemaLocation="https://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd"`


Snippets
========
These are notes (often historical) about setting up modules/features in ECCO.

The expectation is that most of the info is maintained or moved to manuals, base-data and tests.


ESCROW
----
NB codekeeper
    Seems the read/write is the user profile only (ie email and follows) but there is still a read:user permission that
    could be used I'm sure (renovate do at bottom of this page https://docs.renovatebot.com/security-and-permissions/#user-permissions).
    However, it seems github are really bad at providing fine-grained read access, quick search reveals https://github.com/orgs/community/discussions/7891.
    For now, allowing their bot to read/write to my profile - which is fairly limited in functionality, albeit with a write.

The escrow ones are the private repos which aren't archived:
    ecco-crypto, ecco, ecco-common

BUILD
-----
For building, we have some quirks:
    - mbassador is in http://eccosolutions.github.io (under /mbassador/maven)
      this is a REPO itself, not github's clone of npm!
      so we've put that version in there because it has no public repo itself
    - ehcache-sizeofengine-hibernate - 4 commits behind upstream, may not be deleted?
    - jasypt-hibernate5 - few commits behind upstream, could be deleted?
    ? useful still on component library https://github.com/eccosolutions/ecco-ui
    ? useful still on auth0 https://github.com/eccosolutions/identity/
    ? useful still on s3 buckets https://github.com/eccosolutions/secure-files
    NB these repos' commits get buried in bumps, these are the useful commits:
        https://github.com/eccosolutions/ecco-crypto/commits/master?after=a5b50dc996c2023487f988110261295083ffd78c+564&branch=master&qualified_name=refs%2Fheads%2Fmaster
        https://github.com/eccosolutions/renovate-config/commits/main?after=7c0b886ebdce71a2a4a2183e9cec4a1764424762+1470&branch=main&qualified_name=refs%2Fheads%2Fmain

GITHUB
github finds root/.github/workflows ci.yml 'build-and-test' and calls mvn verify on root/pom.xml
mvn does standard java things, but also includes front end code described here
    NB mbassador-snapshots exists because not in maven central
    NB jitpack is used to get a specific version from a private repo (eg cosmo-core)

LOCAL
    clear:
sudo find . -type d -name .gradle -exec rm -rf {} \;
sudo find . -type d -name .node -exec rm -rf {} \;
sudo find . -type d -name build -exec rm -rf {} \;
sudo find . -type d -name target -exec rm -rf {} \;
sudo find . -type d -name node_modules -exec rm -rf {} \;
sudo find . -type d -name build-tsc -exec rm -rf {} \;
sudo find . -type d -name types -exec rm -rf {} \;
sudo find . -type d -name debug -exec rm -rf {} \;
sudo find . -type d -name dist -exec rm -rf {} \;
        -- NB node_modules removes ecco/node_modules/.cache/nx/ which could be helpful on its own
        -- NB dist removes jszip source files that needs reverting
        -- NB don't remove yarn, as we have specifically set versions
            -- sudo find . -type f -name yarn.lock -exec rm -rf {} \;

    build:
        cd top level
        nvm use 18.20.8 (see .tool-versions)
        yarn
    build all:
        mvn clean install -DskipTests=true -DskipITs=true
        yarn gulp emit && yarn gulp pre-tomcat

FRONT-END

see offline/router.tsx for various mappings
    the .env setting PUBLIC_URL shows where the apps are mounted used in index.html and wired in at WelcomeAppBar /r/app
        - eg  _welcome_ r/app, ecco-test-app r/test, ecco-staff-app r/app

ecco-offline runs frontend-maven-plugin running 'yarn install' then 'yarn build' on root/package.json (which runs the front end for the whole project)
    NB cd ecco-offline, 'npm root' reveals the root is ecco/ where ecco/.npmrc, node_modules and package.json are found
        see also 'yarn config list --verbose' and https://docs.npmjs.com/cli/v9/using-npm/config
        .npmrc specifies a private (github) registry for @eccosolutions
        .npmrc specifies link-workspace-packages, and package.json specifies workspaces
'yarn install' installs the dependencies
'yarn build' runs the defined script - tsc on ecco-ui (where all the modern front end is) then yarn nx on ecco-ui/ apps, then yarn workspace ecco-offline gulp copy-files (where all the old front end is)
    NB tsc does not appear to run on ecco-offline
        however, ecco-offline package.json has 'build' and 'emit' which both do a 'gulp emit' which does tsc
        and nx run goes through its workspace.json, including ecco-offline which is set to run 'emit'
    NB tsc uses tsconfig.json, nx uses nx.json and workspace.json
    NB yarn build in each package.json specifies the use of webpack
        NB webpack args are --config-name=dev for dev which picks up the relevant file/config (also search devMode)
            NB provided with 'yarn emit' - which is what nx workspace.json uses to put in /dist or /debug)
            eg, a /dist ecco-components is requested at http://localhost:8888/ecco-war/ui/build/dist/ecco-components.js
            eg, a /debug ecco-components is requested at
            NB RCSFRP addScript /debug uses BaseStaticFileResourceProvider which sets e-tag as 10mins
        ? map files are added by ? but served via OfflineResourceController (search below)
    NB yarn specifies workspaces locations in package.json, where 'ecco-offline' is named in ecco-offline/src/main/resources/com/ecco/offline/staticFiles
'yarn workspace ecco-offline gulp copy-files' runs gulp copy-files in ecco-offline/package.json
'gulp copy-files' moves everything back to ecco-offline
        - root/node_modules libs code to ecco-offline/src/main/resources/com/ecco/offline/staticFiles/scripts/lib
            - /lib is for non-typescript files, put in src so that they will be built into target
        - root/node_modules ecco code to ecco-offline/build/resources/main | ecco-offline/target/classes/com/ecco/offline/staticFiles/build
            - /build is for our typescript files
        - root/node_modules/ecco-staff-app/build/** to ecco-offline/build/resources/main | ecco-offline/target/classes/com/ecco/offline/staticFiles/build/dist/ecco-app
        - root/node_modules/ecco-test-app/build/** to ecco-offline/build/resources/main | ecco-offline/target/classes/com/ecco/offline/staticFiles/build/dist/ecco-test
        NB gulp is an ecco-offline devDependency, with config in the default file gulpfile.js

on startup, ecco-offline is used to serve frontend resources
    NB dotenv package is used to pick up the app/.env which hosts eg PUBLIC_URL=_CONTEXT_PATH_/r/app
    - require-boot.js shows the paths constructed
        NB although the paths in init.jsp are provided
        NB devMode is set from -Denv= in ConfigurableEccoEnvironment, and requirejs_devMode from it in init.tag (eg, from rich-client-page.tag)
    - RarelyChangingStaticFileResourceProvider
        - maps client requests to files, including dist/ecco- and debug/ecco-, css, icons, libs/ etc
    - ResourcesWebMvcConfig (is a controller in that is returns AbstractHandlerMapping)
        - puts files classpath:/com/ecco/offline/staticFiles/ behind /noCache and /cachebust
        - puts files classpath:/com/ecco/offline/staticFiles/build/dist/ ecco-app or ecco-test behind /app and /test
        - intercepts service-worker.js (see service-worker.ts) and index.html and replaces vars inside - such as _CONTEXT_PATH_
CONTROLLERS:
    - OfflineResourceController (for ecco-offline)
        - getResource strips /ui/ and tries to match in responseEntitiesByPath, else serves request if map/ts/tsx
            NB /ui is what the apps use as resourceRootPath - see index.html, its supplied for non-app code (ecco-offline) from ApplicationProperties
            NB also see ecco-components and ecco-offline environment.ts
    - OnlineResourceController
        - getResource returns data as a file for ${ecco.mvc.resourcesPath:}/{cacheBust}/scripts/ application-properties.js and messages.js

NB web.xml servlets:
    ecco-offline /ui/ for OfflineServletConfig
    ecco-web-api /api/ for WebApiServletConfig
    resources /r/ for ResourcesServletConfig
    ecco-war /p, /nav, /dynamic, /online, /auth2, /login for WebServletConfig
    spring-boot /p, /nav, /dynamic, /ui, /r for WebSecurityConfigurer

Example investigation into no debug code for <Building in normal tomcat running...
    /r/cachebust gives 404
    which means /r/cachebust/scripts/common/require-boot.js is not loaded, so no real code is loaded
    it is served by servlet ResourcesServletConfig -> ResourcesWebMvcConfig, but only maps to a known cacheBust path (see cacheBustRequestHandler)
    and the cacheBust path is passed to client using ApplicationPropertiesImpl resourceRootPath, where the server throws
            new FileNotFoundException("Missing offline static resource: " + path);
    which can only be because the frontend resources are not in ecco-offline
    so, an example file: http://localhost:8888/ecco-war/r/18852c42700/bootstrap/css/bootstrap.min.css
    (also see missing scripts at <log.debug("No matching resource found - returning 404");>)
    is loaded using: addCss("bootstrap/css/bootstrap.min.css");
    which looks in staticFiles/bootstrap/css/bootstrap.min.css
    and sure enough, it exists in src but not in target
    so a mvn compile -pl ecco-offline puts this in the right place
    although perhaps because that will do a 'yarn build' (for /dist), not 'yarn emit' (for /debug)
    however...
    nav/r/welcome still got /dist not /debug packages
    because its being rendered by buildings.hbs, via material-ui-page.hbs
    which is bringing in the variables devMode=devMode but seemingly not working


INTEGRATION
-----------
See ql/mapping.properties


HACT - config
----
- see HactAPITests


SMS - implementation
---
- sms can be sent on me/team via 'communication' button (no config? see ReferralActionsCard)
-  or completing a workflow - which looks for an smsTemplate field to send.
- see TwilioSmsService
- NB audits are saved, regardless of the sms success (eg invalid number still saves)

SMS - config
---
- provide args
  - TWILIO="-DTWILIO_ACCOUNT_SID=<account> -DTWILIO_AUTH_TOKEN=<token> -DTWILIO_PHONE_NUMBER=+44..."
- enable referralOverview.communication to see 'communications' menu on client file (if tabOrder allows)
- ISSUES
  - phone number without spaces(?), starts with +447 or 07
  - inbound sms will go to all files where it matches the number (ignoring the first 0 or +44)
  - twilio: set up the inbound address - https://console.twilio.com/us1/develop/phone-numbers/manage/incoming
    - NB ignore webhook bit at the end, just put anything in, eg https://..../api/contacts/sms/********-0000-babe-babe-dadafee1600d


AUTHENTICATION
--------------
See AUTH-FLOW.md


AZURE - implementation
--------------
- 365 login (see WebSecurityConfigurer and oauth2Login, and AADComponentConfig)
- Pull in calendar events (see MsGraphCalendarService in EventController)


	DEBUG
	check browser debug under 'Issues' in the bottom pane
	there is somewhere that lists the allowed callbacks, and one is https-localhost-cant-be-used-with-personal-account os something like that?
	? "The account needs to be added as an external user in the tenant first. Please use a different account"

	portal needs configuring with callback address (known as 'reply url' or 'redirect url')
	needs
		-Dcookie.insecure=false
		-Dcookie.samesite=none
		To have a session your system needs to be able to store the cookie, which a browser won't do if it is expecting SSL, so you need to override params

	http://localhost:8888/ecco-war/api/graph-client/
	http://localhost:8888/ecco-war/api/admin-client/
	http://localhost:8888/ecco-war/api/msgraph/calendarView/
		Access token has expired or is not yet valid
		No doubt this relates to Calendars.Read, which the admin@ecco account doens't have currently

	CONFIG
	I've added redirect URIs to QL and UAT - in the portal
	azure.client-id=disabled and hide login bits as a quick hack

	REVOKE
	https://account.live.com/consent/Manage


AZURE - config
--------------
- see dropbox ms-graph-app_reg.txt
- see context.xml where we need -Dcookie.samesite=none (sign-in requires none for 3rd party)
- AZURE="-Dazure.activedirectory.client-id=<azure application id> -Dazure.activedirectory.client-secret=<azure secret VALUE>"
- -Dcookie.samesite=none
- NB Certificates do expire - tend to get login error 'invalid_token_response' and server logs AuthenticationFailureProviderNotFoundEvent
  - put the new cert as key-id, not client-id
- Open incognito, ensure a user in the db with an email
- Sign in with that email
- login to ecco with 365


INBOUND REFERRAL - implementation
----------------
	OnlinePageController
		/r/{routeBase}/**
		online/refer.jsp

	referral/router.tsx
		refer.tsx
		<Wizard schemaUrl="inbound/referrals/$schema/"/>
		save calls post on uri with state.dto which is a collection of properties of InboundReferralResource

	InboundReferralController
		describe
			JsonSchema of InboundReferralResource
				maps all jsonschema stuff
				looks for {"allowExternalReferrals":true} on services
		createReferral (POST)


INBOUND REFERRAL - config
----------------
- eg http://localhost:8080/ecco-war/p/r/refer
- update services set parameters='{"allowInboundReferrals":true}' where id=1;
- create custom form and configure into 'details of referral' for the desired service
- email trigger if configured on services.parameters with 'email.notification'


EMAILS - implementation
------
Emails can be sent for tasks due and overdue (see EmailAlertController and EmailScheduleController):
- see scheduled/alerts -DenableEmailAlerts=true -DenableEmailSchedule=true
- set ecco.websiteUrl to get emails with correct links

Emails can be sent for checklists (eg buildings) (see EmailCommandHandler)
- sends email to one email in services.parameters 'email.notification' with info in SendEmailCommandViewModel, which comes from client

Emails can be sent for assigned support workers to referrals (see WorkerAllocatedNotificationAgent)
- OutboundEmailAgent: sends email to worker when assigned (based on a template)
- subscribes to WorkerAllocatedEvent (which uses StartOnServiceCommandViewModel) and sends email

Emails can be sent for upcoming appointments, but this is just via getter (see CalendarNotificationAgent, and EventController /notify)

Emails can be sent for new inbound referrals (see CommandEmailNotificationAgent)


EMAILS - config
------
    new customer setup:
        - instance settings:
            -Decco.email.enabled=true [GLOBAL at DelegatingEmailService]
            - select * from cfg_settins where namespace like '%mail%';
            - update cfg_settings set keyvalue='56...' where id=44;
            - update cfg_settings set keyvalue='mail1.eccosolutions.co.uk' where id=45;

        - "ecco.email" feature (was triggerEmail feature) [GLOBAL]

        - select * from templates;
        - tomcat:
            - update templates set body=REPLACE(body, 'http://localhost:8080/ecco-war/nav', 'https://app.eccosolutions.co.uk/testgu/nav');
        - boot:
            - update templates set body=REPLACE(body, 'http://localhost:8080/ecco-war/nav', 'https://app.eccosolutions.co.uk/testgu/nav/r/main/sr2');

    task config CommandEmailNotificationAgent handleTaskEventEmailManager(event):
        - service email: update services set parameters='{"email.notification": "..email.."}' where id=x;
        - OR config on triggerEmail and triggerEmailTo

		create template in templates (search DEV-611-createReferral-template), and ensure the rootEntity exists in Template
        templates: https://demo.eccosolutions.co.uk/housing/nav/secure/templates/editor/
            Email Worker Assigned
            replace template text
                update templates set body=REPLACE(body, 'new referral has been received', 'new referral has been received on: {serviceName} {projectName}') where name='ReferralCreated';
            don't edit until params are avail:
                UPDATE templates SET body = REPLACE(body, '{projectName}', '{projectName} {urlServletBase}') where name='ReferralCreated';

    inbound referral config CommandEmailNotificationAgent handleNewReferralEventEmailManager(event):
        update services set parameters='{"allowInboundReferrals": true, "email.notification": "<EMAIL>"}' where id >= 216794;
        template ui (tweak list for ReferralCreated), https://demo.eccosolutions.co.uk/ql/nav/secure/templates/editor/
        template: select * from templates where name="ReferralCreated";
        update templates set body=REPLACE(body, 'A new referral has been received', 'New referral: {serviceName} {projectName}') where name='ReferralCreated';
        update templates set body=REPLACE(body, 'http://localhost:8080/ecco-war/nav', 'https://app.eccosolutions.co.uk/<instance>/nav') where name='ReferralCreated';

    AppointmentNotificationEvent in CalendarNotificationAgent:
        NB this is only triggered manually via EventController.triggerNotificationEvent

    WorkerAllocatedEvent in WorkerAllocatedNotificationAgent:
        -DenableEmailAllocated=true
        template "WorkerAssigned"

    EmailEventController:
        -DenableEmailEvent=true
        hardcoded template - see CalendarNotificationAgent AppointmentNotificationEvent

    EmailOverdueController [tasks share EmailBaseController]:
        -DenableEmailAlerts=true
        hardcoded template

    EmailScheduleController [tasks share EmailBaseController]:
        -DenableEmailSchedule=true



	DelegatingEmailService chooses between:

		JavaMailSender
            -Dspring.mail.host using JavaMail if mailgun not configured
			specify 'spring.mail.host' to trigger JavaMailService: see MailSenderAutoConfiguration
			domain optional - uses mailgun setting

		eg MailTrap to trap mail, but setting these knocks out the use of mailgun as the provider (unless we put the mailgun API details in the settings)
			see mailtrap.io inbox setting for username, pwd
			MAIL_OPTS="-Dspring.mail.host=smtp.mailtrap.io -Dspring.mail.username=7ced1d0d2a8f10 -Dspring.mail.password=<>"
			this gets picked up by spring (for JavaMailSender?)
			and just simple message sent to smtp.mailtrap.io
        Setup:
            demo (ql): https://mailtrap.io/inboxes/284173/messages
            register as general user: https://mailtrap.io/register/signup?ref=header
            then can configure an inbox and add the user to it, so can see stuff
            settings in the setting table for ENABLED only
            cmd line params to send via mailtrap

		MailGunService
			(we don't want a more formal, local, smtp server like jason)
			see settings table - select * from setting where namespace='com.ecco.mail.mailgun';
			<EMAIL> has api key
				select * from setting where id=44;
				update setting set keyvalue='<api-key>' where id=44;
				update setting set keyvalue='mail1.eccosolutions.co.uk' where id=45;
        Setup:
			added payment to avoid authorisation
			added DNS setings for mail1.eccosolutions.co.uk to allow sending from our domains - see https://help.mailgun.com/hc/en-us/articles/*********-How-do-I-pick-a-domain-name-for-my-Mailgun-account-
				followed instructions for DNS - https://app.mailgun.com/app/domains/mail1.eccosolutions.co.uk
				NB USE THE 'check DNS records now' - it seems a refresh doesn't just do it!
            5 day retention for free account
                settings in the setting table for api and domain
                ensure https to mailgun

		SendGrid
			? just another option, not chosen?


ROTA - config
-------------
also search for configSetup in API tests
BASE-DATA START - run RotaReferralAPITests.setup()
- possibly run with 'endDates'
- create referral on demo-all-workflow
- 'staff' menu needs role hr and evangelist
- add worker with user and staff

CLEAN SITE START (new customer with some base-data cleared)
- see 20160822_hfmind_buildings.txt
- see 20150811_enable_bldgs.txt
- see 20160830_domcare_sortout.txt
- add appointment type - so can create entries
    - insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (1, 0, 0, 'get up', 100690, 60, 0, '{"colourCssValue":"red"}');
    - insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (4, 0, 0, 'shared hours', -100, 60, 0, '{"colourCssValue":"red"}');
- create skills
    - update setting set keyvalue=concat(keyvalue, ',skills') where id=23;
- clear cache for skills?
- create a service type...
    - 'service agreements'
    - 'rota visit' with previewAndSign
- ensure there is a worker in 'hr' and 'link' the user to a username
    - 'back to old menu' is the key here (for the time being). Then 'hr' and 'add worker' and complete basic details
  reminded to click top 3 lines on hr staff page (link / building / joined date)
  click on 'linked user' if you want them to take part in recording start/stop etc - choose a username for them and click 'create user from worker'
    - the worker's user must have 'staff' permissions to be seen on the rota
- the worker in 'hr' can indicate a 'skills' freetext which shows on the rota
- clear cache
- add the 'rota report' from reports-definitions.txt
- enable offline: update cfg_module set enabled=true where name='offline'


ROTA - basic usage
------------------
- for user guides in zendesk (also uat quick guide)
- https://demo.eccosolutions.co.uk/domcare/nav/index.html
- create client, create service agreements
- check a couple of client demands on the rota
- create availability for staff member, eg jimstaff
- allocate one to worker
- check login as worker to see appointment on welcome page
- click appt, fulfil the visit and look at the rota report
- try offline


ROTA - event snapshot (was lone worker)
------------------
Originally, this snapshot was for lone working to determine uncompleted visits, then it was pre-populated (DEV-2443)
to allow us to determine missed/late visits easily for alerting. Now, its being considered as a way to cache data which
could be used for EvidenceView and ScheduleView (replacing their findAllUninvoiced), but could even be used for the rota itself.

CalendarEventSnapshot (cal_eventstatus) stores the workUuid with the start/end/plannedMins/status of a visit.
The EvidenceAssociatedContactCommandViewModel is used to manipulate the snapshot in the handler.
The 'start' and 'end' generate the commands, in careVisitReducer's loneWorkerUpdate - passed from CareCalendarEventCard (generateLoneWorkerUpdate).
Following through, createLoneWorkerCommand creates the command but ignores the plannedMins, as does EvidenceAssociatedContactCommandViewModel.

When the rota loads it calls useRotaLiveEventStatuses to get all snapshot data starting today. On the RotaDayView the workerJob
rows are drawn referring to eventStatusMissed and eventStatusLate to determine whether the colour changes.

When the appointments load, CareVisitOverview.tsx (careVisitReducer.tsx "showVisit") and from the rota also, shows the start/stop status
based on the server's eventStatus provided from SupportEvidenceController using EvidenceSupportWorkViewModel.java to BaseOutcomeBasedWorkSecretFields.ts eventStatus.
The eventStatus snapshot only stays for 3 days (see below), so it should perhaps be loaded from an audit, eg EvidenceAssociatedContactCommandViewModel
but it is additional information only, available whilst the snapshot does, and we use workDate to determine if a visit is 'startable ='
and keep 'editable =' for when a eventStatus exists (therefore a 'start' could be done again after the snapshot expires and no work has been done).

A scheduled task (CalendarEventSnapshotPruneController#prune) is scheduled per day to clear 3 day old data.

ROTA - billing
--------------
NB creating a contract - then can manage rate cards
    select * from fin_contracts;
    select * from fin_contracts_ratecards;
    select * from fin_ratecards;
    select * from fin_ratecardentries;
    select max(id) from servicerecipients;
    select * from hibernate_sequences where sequence_name='svc_recipient';
    update hibernate_sequences set next_val=200004 where sequence_name='svc_recipient';
    insert into servicerecipients (id, version, discriminator_orm, created, currentTaskId, currentTaskIndex, serviceAllocationId)
    values (200003, 0, 'cont', now(), 32, 4, -300);
    insert into fin_contracts (id, version, name, startInstant, endInstant, servicerecipientId)
    values (1, 0, 'default', '2023-01-01', '2099-01-01', 200003);
    -- then update cards for end fix
    update fin_ratecards set matchingEndTime='23:59:59';

TESTING
    See RotaReferralAPITests#
    See ClientInvoicingTests#setupWholeRotaBasicScenarios

DOCUMENTATION
    RateCard.java

The data flow starts with unverified visits, which are allocated apts currently - but should include dropped/unallocated.
Unverified visits get verified, and get added to a draft invoice which can be adjusted and finalised and 'post'ed.
    NB finalised is just a state to help the user know what may have been checked - similar to 'verify'.
    NB invoice state is draft/final/posted
        but a search reveals "POSTED isn't a back end concept, so should either be introduced there or removed here (it's possible POSTED means FINAL)"
The adjustments, finalising, and posting are not implemented. The server does the value using the rate cards etc.
    NB adjustments should be extra invoice lines, rather than adjusting the existing one (is that what the now hidden 'reverse' does?)
The payroll should load the posted invoice data and calculate the hours and pay according to the job's rate card. Currently,
it simply uses the invoices to calculate the time for the workers client-side to then export.

unverified - EvidenceView:
    'load' -> getDataByDate -> getUninvoicedLines -> setRows -> EvidenceVerificationRow
        each row in unverified is an InvoiceLineDto
            since getUninvoicedLines calls RotaActivityInvoiceController#findAllUninvoicedWorkInBuilding
            which gathers direct allocations and calls ClientSalesInvoiceDetailResourceLineAssembler#appointmentToLine (CSIDRLA)
                which does the calculations on netAmount using rate cards etc
                NB rate card documentation can be found on RateCardEntryDto, RateCardCalculation, ClientSalesInvoiceDetailResourceLineAssembler#recordedWorkToLine
                    NB fundamentally, a rate can be applied to the schedule as fixed rateCardId or flexible rateCardName
                    the CSIDRLA calls the schedule.allowableRateCards or looks up the contract (matching rateCardName if provided)
                    the rate cards (currently) are matched by date/time, and the entries within are the charges
                NB taxRate appears not to be calculated anywhere currently
            then findUnverified is called to load all rotaVisits evidence in range and add more information
                including the description as 'workMinutesSpent + " minutes on " + workDate'
    'verify' -> verifySelectedItems -> sequentialMapAll per work item -> getOrCreateInvoice then CreateInvoiceLineCommand in addLineItemToInvoice
        where addLineItemToInvoice calls RotaActivityInvoiceController - with workUuid, eventId, rateCardId, netAmount, taxRate, description
            NB this is actually passing back information the server already calculated in getUninvoicedLines
                "Note: planned,actual mins/date are populated on back end from work/event"

invoices - InvoicesList:
    getInvoices called immediately -> RotaActivityInvoiceController#findAllInvoices which gets all fin_invoices (ClientSalesInvoice)
        expander loads the NestedInvoiceTable
    each line is 'FINALISE'd individually, which can then be posted together
    'posted' -> UNIMPLEMENTED - see passSelectedInvoices
        NB a 'locked' sales line is when its posted, and designed not to be edited (nor should the appointment)
        NB we can show all invoices for the time range because each month will be different

payroll - PayrollView:
    getInvoices called immediately
        expander loads the NestedInvoiceTable
    getPayrollRows groups the lines by resource calendarId
        calculatePayrollItem adds up the mins spent
    each line is 'FINALISE'd individually, but UNIMPLEMENTED - just a copy of invoices
    'paid' -> UNIMPLEMENTED - just a copy of invoices




BUILDINGS - config`
------------------
- if not base data
    - insert into servicetypes (id, version, name) values (-100, 0, 'buildings-default');
    - insert into servicetypes (id, version, name) values (-200, 0, 'hr-default');
    - insert into services (id, version, name, servicetypeid) values (-100, 0, 'buildings', -100);
    - insert into services (id, version, name, servicetypeid) values (-200, 0, 'hr', -200);
    - update servicetypes set type='buildings' where id=-100;
    - update servicetypes set type='hr' where id=-200;
- update cfg_module set enabled=1 where name='buildings';
- clear cache
- create buildings (ui when on 'buildings')

- checks tab
    - enable in features: buildings.overview.tabs.support


- IGNORE below - 'emergency eq', 'safety and hygiene' are in the base data


    Bin Stores
    group1
    Is the bin store locked?
    Has any rubbish been dumped?
    Is recycling area tidy and safe?


    Electrical
    group1
    Has equipment been tested and labelled? Date?
    Are there any damaged electrical cables?
    Are there any damaged sockets or plugs?
    Are there any double adaptors or extension leads in use in the communal areas?
    Are there any cables loose or trailing?
    Are any light bulbs missing or defective?
    Is there any personal untested electrical equipment in use in any communal areas?
    Are electrical equipment ventilation ports clear of any blockages?


    Fire Equipment / Safe Evacuation
    group1
    Have all extinguishers been tested on an annual basis?
    Do fire extinguishers have wall fixings?
    Have any fire extinguishers been moved from their designated points?
    Are any fire doors propped open?
    Are fire procedure notices displayed?
    Are there adequate fire escape signs and fire action signs throughout the scheme?
    Are fire escape signs in their designated positions?
    Have you tested the fire alarm?
    Has the monthly testing of the emergency lighting been completed?


    First Aid
    group1
    Is the first aid kit complete and up to date?

- servicetype
    - add needs/reduction and checklist:
        - order 1
            - /servicetype/-100/needsAssessment/
            - configure outcomesById - 'emergency eq', 'safety and hygiene'
            - tabular
        - order 2
            /servicetype/-100/needsReduction/
            - enable 'add goal'
            - configure outcomes
        - order 3
            - 'check list' is the all-at-once checklist
            - /servicetype/-100/needsChecklist/
            - configure outcomes
- 'checks' tab shows the list where can individually record
    - enable in features: buildings.overview.tabs.support
    - best not to add this checking process until rota established
- 'check list' is the all-at-once checklist
- defects?
    ?? buildings.overview.tabs.defects
- if not rota - remove 'agreements' buildings.overview.tabs.agreements


BUILDINGS ROTA - config
-----------------------
- see rota - config
- create worker login
    - put the worker to have 'ROLE_STAFF' and a standard pwd
    - ensure worker has link user (for correct calendar?)
    - pick jim staff and create worker, then assign ROLE_STAFF
- add optional 'skills' field into setting table: WORKER_OPTIONAL_FIELDS, id 23
    - update setting set keyvalue=concat(keyvalue, ',skills') where id=23;
- clear cache
    - use admin on buildings
- then use ui admin mode to create buildings
- then create worker + ensure link user
- then assign to building
    - select w.id, w.primarylocationId, c.firstname, c.lastname from workers w inner join contacts c on w.contactsId=c.id;
    - select * from bldg_fixed;
    - -- update workers set primaryLocationId=1001 where id=;
    - update workers set primaryLocationId=1020 where id=300125; -- jim staff
- assign a client to a building
    - now part of assigning a client address as: building '@' address, was:
        - turn on feature 'referralOverview.clientResidingAt'
        - (this does the job of: update clientdetails set residenceId=1001 where id=482)
- add agreement [THIS ENABLES 'rota']
    - turn on feature 'buildings.overview.tabs.agreements'
    - use admin on buildings
- add appointment type - so can create entries
    - insert into appointmenttypes (id, version, disabled, name, serviceId, recommendedDuration, isDefault, parameters) values (4, 0, 0, 'shared hours', -100, 60, 0, '{"colourCssValue":"red"}');
- clear cache


BUILDING ROTA - basic usage
---------------------------
- see https://eccosolutions.atlassian.net/browse/ECCO-1475
- see hfmind 'propery checks.pdf' manual p5
- is there a specific agreement for a building that creates demand? its blank? even in admin mode\
- currently just '+' on building rota and choose building
- worker sickness/holidays come through in grey
- BUG - deallocate - drops away! without refresh
- BUG - doesn't appear on fudged url for day view
- FEATURE - no link through from appointments to shared hours
- FEATURE - can allocate multiple staff to appt


DAY CENTRE
----------
- day centre scenario: https://docs.google.com/presentation/d/1qk5SqdO3D_BBSg4MIsmihX_X_Sh_xRYc0rD0NakAu-A/edit#slide=id.g9bbfe8db2_0_0
- see https://demo.eccosolutions.co.uk/daycentre/online/groupsupport/64290/
- features of a day centre service:
    - allocation to one or more day centres for regular visits
    - attendance tracking for planned and actual against different activities
    - attendance register is triggered where there is an end date (so for CDO, just creating a year log GS activity will do)
    - "my support" contact log against each service used
    - various summary reports (some refinement needed against rolled out usage - which has been disrupted by changes in management)
    - needs 'activity interest' for scheduling a new GS activity is hidden. (also removed when adding/editing a smart step in the graphical support)
        - that's done on cdo/daycentre/test3.
    - the 4 top reports are notable, such as the
        - author breakdown one can be sorted by total minutes
        - latest work date so you can see who's been doing what


SIGNATURE BLURB - config
------------------------
- TasksControl.tsx -> signedAgreements.tsx -> has a default text, or uses 'DataProtection' - to load from templates
- save first into templates editor (eg Referral - Markdown example]
- select * from templates;
- see where to put it...
- select * from evidenceguidance;
- update evidenceguidance set guidanceMarkdown=(select filecontent from templates where name='ReferralSummary') where id=-28;
- delete from templates;
