{"private": true, "packageManager": "yarn@1.22.22", "scripts": {"prepare": "husky install", "clean": "yarn workspaces run clean", "emit": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build --verbose ecco-ui/tsconfig.json && yarn dev", "dev": "yarn nx run-many --target=emit --parallel --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "build": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build ecco-ui/tsconfig.json && yarn nx run-many --target=build --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "affected": "yarn nx affected --target=build --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "resume": "yarn nx run-many --only-failed --target=build --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app && yarn workspace ecco-offline gulp copy-files", "lint": "yarn nx run-many --target=lint --parallel --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app", "test": "yarn nx run-many --target=test-parallel-safe --parallel --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app && yarn nx run-many --target=test-sequential --with-deps --projects=ecco-staff-app,ecco-portal-app,ecco-test-app", "e2e": "yarn nx run-many --target=test-parallel-safe --parallel --with-deps --projects=ecco-ui-e2e && yarn nx run-many --target=test-sequential --with-deps --projects=ecco-ui-e2e", "gulp": "yarn workspace ecco-offline gulp", "dep-graph": "yarn nx dep-graph", "tsc": "tsc --build --clean ecco-ui/tsconfig.json && tsc --build ecco-ui/tsconfig.json && yarn workspace ecco-offline tsc --build --verbose scripts/tsconfig.json", "pre-commit": "precise-commits --whitelist=\"ecco-ui/**/*\""}, "workspaces": {"packages": ["ecco-ui/application-properties", "ecco-ui/ecco-admin", "ecco-ui/ecco-dto", "ecco-ui/ecco-commands", "ecco-ui/ecco-offline-data", "ecco-ui/ecco-calendar", "ecco-ui/ecco-components-core", "ecco-ui/ecco-components", "ecco-ui/ecco-evidence", "ecco-ui/ecco-finance", "ecco-ui/ecco-forms", "ecco-ui/ecco-incidents", "ecco-ui/ecco-repairs", "ecco-ui/ecco-math", "ecco-ui/ecco-mui", "ecco-ui/ecco-mui-controls", "ecco-ui/ecco-reports", "ecco-ui/ecco-rota", "ecco-ui/ecco-spa-global", "ecco-ui/ecco-test-app", "ecco-offline/src/main/resources/com/ecco/offline/staticFiles", "ecco-ui/ecco-staff-app", "ecco-ui/ecco-portal-app", "ecco-ui/_welcome_", "ecco-ui/ecco-ui-e2e", "ecco-ui/ecco-webpack-config"], "nohoist": ["**/@nrwl/jest", "**/@nrwl/jest/**", "**/ecco-webpack-config", "**/ecco-webpack-config/*", "**/ecco-webpack-config/**/tapable", "**/ecco-webpack-config/**/enhanced-resolve", "**/ecco-webpack-config/**/workbox-webpack-plugin", "**/ecco-webpack-config/**/workbox-webpack-plugin/*", "**/@types/webpack"]}, "dependencies": {}, "devDependencies": {"@nrwl/cli": "^12.10.0", "@nrwl/nx-cloud": "^12.3.14", "@nrwl/react": "^12.10.0", "@nrwl/tao": "^12.10.0", "@nrwl/workspace": "^12.10.0", "@softwareventures/precise-commits": "^4.0.21", "@softwareventures/prettier-config": "^4.0.0", "husky": "^7.0.4", "prettier": "^3.6.1", "typescript": "5.8.3"}, "prettier": "@softwareventures/prettier-config", "resolutions": {"@softwareventures/date": "^3.0.2", "@softwareventures/format-timestamp": "^1.1.1", "@softwareventures/time": "^1.0.0", "@softwareventures/timestamp": "^1.4.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}}