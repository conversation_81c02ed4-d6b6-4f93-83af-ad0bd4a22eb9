import org.springframework.boot.gradle.plugin.SpringBootPlugin
import org.springframework.boot.gradle.tasks.run.BootRun

plugins {
    id("org.eccosolutions.owasp-check")
}

apply<SpringBootPlugin>()

tasks.named<BootRun>("bootRun") {
    systemProperty("user.timezone","UTC")
    systemProperty("env", "dev")
    systemProperty("liquibase", "CREATE")
    systemProperty("db.schema", "acctest")
    systemProperty("db.port", "3307")
    systemProperty("db.extraContexts", "acceptanceTests")
    systemProperty("cookie.insecure", "true")
    systemProperty("cookie.samesite", "strict")
    systemProperty("azure.activedirectory.client-id", "asdf")
    systemProperty("azure.activedirectory.client-secret", "asdf")

}


dependencies {
    // TODO: Should be developmentOnly - see https://stackoverflow.com/a/76138565/1998186
    runtimeOnly("org.springframework.boot:spring-boot-devtools")

    implementation(project(":ecco-infrastructure"))
    implementation(project(":ecco-offline"))
    implementation(project(":ecco-web"))
    implementation(project(":ecco-web-api"))

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-configuration-processor")

    testImplementation(project(":test-support"))

    testImplementation("com.azure.spring:azure-spring-boot-starter-active-directory")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

description = "ecco-webapi-boot"
