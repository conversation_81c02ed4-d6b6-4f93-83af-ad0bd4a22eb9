package com.ecco.data.client.dataimport.support;

import com.ecco.data.client.model.evidence.QuestionnaireBasedImportViewModel;
import com.ecco.dom.EvidenceGroup;
import com.ecco.dto.ChangeViewModel;
import com.ecco.evidence.EvidenceTask;
import com.ecco.serviceConfig.viewModel.QuestionViewModel;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;
import com.ecco.webApi.evidence.*;
import org.apache.commons.lang3.StringUtils;
import com.ecco.webApi.featureConfig.SessionDataViewModel;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.Map.Entry;

/**
 * Handles the import of a spreadsheet row which includes evidence information.
 * (We tried mapping the csv header straight to command view models, but this required
 * a public constructor and getter/setters).
 * Although we have our own EvidenceImportViewModel, its possible we could use the
 * out-bound EvidenceViewModel.
 */
public class QuestionnaireEvidenceHandler extends BaseEvidenceHandler<QuestionnaireBasedImportViewModel> {

    public QuestionnaireEvidenceHandler(RestTemplate restTemplate) {
        super(restTemplate);
    }

    @Override
    void processEvidence(ImportOperation<QuestionnaireBasedImportViewModel> operation, UUID workUuid, ReferralViewModel rvm, SessionDataViewModel sessionData) {

        QuestionnaireBasedImportViewModel input = operation.record;

        if (!input.commentConcat.isEmpty()) {
            applyCommentConcat(input, rvm, workUuid);
        }
        EvidenceTask task = EvidenceTask.fromTaskName(input.getEvidenceTask());
        // questionnaires use their own name and id, so just find the taskDef
        var taskDef = getTaskDefinition(task.getTaskName());
        EvidenceGroup group = EvidenceGroup.fromGroupIdName(taskDef.name, taskDef.id);
        // group for data-import
        CommentCommandViewModel cc = createCommentCommand(input, group, task, rvm, workUuid);
        syncCommentCommandToServer(operation.baseUri.concat(apiPath), cc);

        if (!input.answers.isEmpty()) {
            createAndSyncAnswerCommandsToServer(operation.baseUri.concat(apiPath), group, task, input, rvm, sessionData, workUuid);
        }

    }

    private void createAndSyncAnswerCommandsToServer(String baseUri, EvidenceGroup evidenceGroup, EvidenceTask task, QuestionnaireBasedImportViewModel input,
                                                     ReferralViewModel rvm, SessionDataViewModel sessionData, UUID workUuid) {

        var svcCat = serviceActor.getServiceCategorisation(sessionData, rvm.serviceAllocationId);
        var serviceTypeId = svcCat.getServiceTypeId();

        ServiceTypeViewModel st = getServiceType(rvm.serviceTypeId.intValue());

        for (Entry<String, String> entry : input.answers.entrySet()) {
            Integer questionDefId = getQuestionId(entry.getKey(), st);
            if (questionDefId == null) {
                continue;
            }

            String answerIn = entry.getValue();
            QuestionAnswerCommandViewModel answer = new QuestionAnswerCommandViewModel(workUuid,
                    rvm.serviceRecipientId,
                    evidenceGroup,
                    task,
                    questionDefId);

            answer.operation = BaseCommandViewModel.OPERATION_ADD;
            answer.answerChange = ChangeViewModel.create(null, answerIn);

            // sensible to only save a new goal if there is a status
            if (StringUtils.isNotBlank(answerIn)) {
                syncAnswerCommandToServer(baseUri, answer);
            }
        }
    }

    private long syncAnswerCommandToServer(String uri, QuestionAnswerCommandViewModel input) {
        com.ecco.webApi.viewModels.Result result = executeCommand(uri, input);
        return Long.parseLong(result.getId());
    }

    private Integer getQuestionId(String name, ServiceTypeViewModel st) {
        // with our question name, check it has ':' to indicate we should match on all the questions separated by :
        // this allows the file format to use the same column - by simply letting this code find the first question in the group
        List<String> namesCase = new ArrayList<>();
        if (StringUtils.contains(name, ":")) {
            namesCase.addAll(Arrays.asList(StringUtils.split(name, ":")));
        } else {
            namesCase.add(name);
        }
        List<String> names = namesCase.stream().map(String::toLowerCase).toList();

        // find questions across all questiongroups
        List<QuestionViewModel> allQuestions = st.questionGroups.stream()
            .flatMap(qg -> getQuestionGroup(qg.id).questions.stream())
            .toList();

        // find questions matching names
        var s = new HashSet<>(names);
        var foundQuestions = allQuestions.stream()
                .filter(q ->
                        q.name != null && s.contains(q.name.toLowerCase())
                )
                .toList();

        if (foundQuestions.isEmpty()) {
            return null;
        }
        //Assert.state(foundQuestions.size() == 1, "Question [" + name + "] found " + foundQuestions.size() + " times for this servicetype " + st.id);

        return foundQuestions.get(0).id;
    }
}
