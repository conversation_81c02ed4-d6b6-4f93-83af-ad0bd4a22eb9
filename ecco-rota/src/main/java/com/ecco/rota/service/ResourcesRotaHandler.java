package com.ecco.rota.service;

import com.ecco.buildings.dom.BookableResource;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.dao.DemandSchedulePredicates;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.agreements.ResourceSchedule;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.service.EventService;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.joda.time.DateTime;
import org.joda.time.Interval;

import java.util.EnumSet;
import java.util.List;
import java.util.stream.StreamSupport;

import static com.ecco.buildings.dom.Container.RESOURCETYPE_LISTNAME;
import static com.ecco.calendar.core.Recurrence.Status.DROPPED;
import static com.ecco.calendar.core.Recurrence.Status.TENTATIVE;

/**
 * Resources can be considered assets in the sense that they are always available unless booked.
 * This handler shows the resources (bookable-resources, so buildings or resource-beds or meeting room) as 'workers' on the rota, where
 * the demand is shown from entities with DemandResource agreements - so, referrals.
 * NB Should probably limit to those of the same resourceTypeName.
 */
public class ResourcesRotaHandler extends BaseRotaHandler {

    private static final String DEMAND_PREFIX = "resources:"; // as in a FixedContainer (bookable resource)

    private final FixedContainerRepository bookableResourceRepository;

    private final ListDefinitionRepository resourceTypeRepository;

    protected ResourcesRotaHandler() {
        this(null,null,null,null,null,null);
    }

    public ResourcesRotaHandler(DemandScheduleRepository demandScheduleRepository,
                                FixedContainerRepository bookableResourceRepository,
                                ListDefinitionRepository resourceTypeRepository,
                                ServiceAgreementRepository serviceAgreementRepository,
                                RotaService rotaService,
                                EventService eventService) {
        super(rotaService, demandScheduleRepository, serviceAgreementRepository, eventService);
        this.bookableResourceRepository = bookableResourceRepository;
        this.resourceTypeRepository = resourceTypeRepository;
    }

    @Override
    public boolean canHandle(String resourceFilter, String demandFilter) {
        return resourceFilter.startsWith(DEMAND_PREFIX);
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        Rota rota = new Rota(params.getStartDate(), params.getEndDate(), params.getResourceFilter(), params.getDemandFilter(), params.getLoadResource(), params.getLoadDemand());

        var resources = getResources(rota);
        return StreamSupport.stream(resources.spliterator(), false).map(FixedContainer::getServiceRecipientId).toList();
    }

    @Override
    public void populateRota(Rota rota) {
        List<FixedContainer> resources = getResources(rota);
        // just add all resources to the rota - don't check availabilities
        addResourceAvailabilities(rota, resources);
        // get the agreements 'appointmentschedules' for the period and look in its calendar_entry_handle for recurrences with the right status.
        // adding unallocated demand (tentative) and dropped demand (just exceptions to the recurring entry)
        addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.getDemandScheduleWherePredicate(rota));
        // adding allocated demand (confirmed) from a resource
        addAllocatedResources(rota, resources);
    }

    private List<FixedContainer> getResources(Rota rota) {
        String resourceTypeName = rota.getResourceFilter().substring(DEMAND_PREFIX.length());

        // Look up not strictly necessary, but does verify that the code is defined
        List<FixedContainer> resources = "all".equals(resourceTypeName)
                ? bookableResourceRepository.findAllByOrderByResourceTypeNameAsc()
                : bookableResourceRepository.findAllByResourceTypeId(
                        resourceTypeRepository.findOneByListNameAndName(RESOURCETYPE_LISTNAME, resourceTypeName)
                                .orElseThrow(() -> new IllegalArgumentException(resourceTypeName + " is not a defined resource type"))
                                .getId());
        return resources;
    }

    @Override
    protected BooleanExpression getDemandScheduleWherePredicate(RotaParams rota) {
        return DemandSchedulePredicates.getWherePredicate(rota.getStartDate(), rota.getEndDate(), ResourceSchedule.class);
    }

    @Override
    public String getResourceCalendarId(int resourceId) {
        final BookableResource resource = bookableResourceRepository.findById(resourceId).orElseThrow();
        return resource.getCalendarId();
    }

    private void addResourceAvailabilities(Rota rota, final Iterable<? extends BookableResource> resources) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().plusDays(1).toDateTimeAtStartOfDay();
        log.info("addResourceAvailabilities {} -> {}", start.toString(), end.toString());
        for (BookableResource resource : resources) {
            RotaResourceViewModel entry = rota.createOrFindResourceEntry(resource.getDisplayName(), resource.getServiceRecipientId(), "TODO resource");
            // a blanket policy that the resource is always available to be allocated
            entry.addAvailability(start.toDateTime(), end.toDateTime());
        }
    }

    /**
     * Allocated resources are modifications of recurring events in CONFIRMED status, which correspond to an resource schedule and have a worker attendee.
     *
     * @param rota the rota to which to add the allocated demands
     * @param resources the resources who need their allocated demands populating
     */
    private void addAllocatedResources(final Rota rota, final Iterable<? extends BookableResource> resources) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().plusDays(1).toDateTimeAtStartOfDay();
        for (BookableResource resource : resources) {
            RotaResourceViewModel rotaEntry = rota.createOrFindResourceEntry(resource.getDisplayName(), resource.getServiceRecipientId(), "TODO resource");
            rotaEntry.setResourceId(resource.getId().longValue());

            final String calendarId = resource.getCalendarId();
            rotaService.findRecurrencesFromCalendar(calendarId, new Interval(start, end)).forEach(recurrence -> {
                rotaService.addRelevantRecurrences(recurrence, rotaEntry, ResourceSchedule.class, null);
            });
        }
    }
}
