package com.ecco.rota.service;

import com.ecco.buildings.dom.BuildingServiceRecipient;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dao.DemandSchedulePredicates;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.rota.webApi.dto.*;
import com.ecco.calendar.core.Availability;
import com.ecco.calendar.core.AvailableInterval;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.jspecify.annotations.NonNull;

import java.util.EnumSet;
import java.util.List;
import java.util.stream.StreamSupport;

import static com.ecco.dom.agreements.QDemandSchedule.demandSchedule;
import static com.ecco.calendar.core.Recurrence.Status.*;

/**
 * Handles fixed location resource (building as a care run) to building demand (shift).
 * eg a care-run to a shift.
 * NB This is PoC really, and does not represent a live scenario in the system (note the canHandle is false).
 * NB This is largely copied to BuildingCareRunRotaHandler, but we're avoiding cleaning up/refactoring for now until good clarity emerges.
 */
public class ShiftCareRunArchiveRotaHandler implements RotaHandler {

    private static final String DEMAND_HANDLER_PREFIX = "buildings:"; // shifts
    private static final String RESOURCE_HANDLER_PREFIX = "careruns:";

    private final FixedContainerRepository fixedContainerRepository;
    private final BuildingWorkerRotaHandler shiftRotaHandler;
    private final RotaService rotaService;
    private final DemandScheduleRepository demandScheduleRepository;

    public ShiftCareRunArchiveRotaHandler(@NonNull final FixedContainerRepository fixedContainerRepository,
                                          @NonNull final BuildingWorkerRotaHandler shiftRotaHandler,
                                          @NonNull final RotaService rotaService,
                                          @NonNull final DemandScheduleRepository demandScheduleRepository) {
        //super(null, buildingWorkerRotaHandler.demandScheduleRepository);
        this.fixedContainerRepository = fixedContainerRepository;
        this.shiftRotaHandler = shiftRotaHandler;
        this.rotaService = rotaService;
        this.demandScheduleRepository = demandScheduleRepository;
    }

    @Override
    public boolean canHandle(String resourceFilter, String demandFilter) {
        return false;
        //return resourceFilter.startsWith(RESOURCE_HANDLER_PREFIX) && serviceRecipientDemandFilter.startsWith(DEMAND_HANDLER_PREFIX);
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        var resources = getResources();
        return StreamSupport.stream(resources.spliterator(), false).map(FixedContainer::getServiceRecipientId).toList();
    }

    @Override
    public void populateRota(Rota rota) {
        FixedContainer shift = shiftRotaHandler.getDemandBuilding(rota);

        // add care run resources to allocate to shifts
        addCareRunResources(rota);
        // add shift demands
        rotaService.addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), getWherePredicateForDemandSchedulesBuilding(rota));
    }

    @Override
    public String getResourceCalendarId(int resourceId) {
        FixedContainer careRun = this.fixedContainerRepository.findOne(resourceId);
        return careRun.getCalendarId();
    }

    @Override
    public List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params) {
        throw new UnsupportedOperationException();
    }

    @Override
    public List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params) {
        throw new UnsupportedOperationException();
    }

    // TODO don't load all buildings - restrict to those with a parent of the building shift demand
    private void addCareRunResources(final Rota rota) {
        List<FixedContainer> careRuns = getResources();

        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().plusDays(1).toDateTimeAtStartOfDay();
        for (FixedContainer careRun : careRuns) {
            final String calendarId = careRun.getCalendarId();

            if (calendarId != null) {
                RotaResourceViewModel rotaEntry = createOrFindRotaEntry(rota, careRun);
                rotaEntry.setResourceId(careRun.getId().longValue());
                rotaEntry.setCalendarId(calendarId);

                populateCareRunEntry(start, end, careRun, rotaEntry, calendarId);
            }
        }
    }

    @NonNull
    private List<FixedContainer> getResources() {
        List<FixedContainer> careRuns = fixedContainerRepository.findAll();
        return careRuns;
    }

    private RotaResourceViewModel createOrFindRotaEntry(Rota rota, FixedContainer careRun) {
        return rota.createOrFindResourceEntry(careRun.getDisplayName(), careRun.getServiceRecipientId(), BuildingServiceRecipient.DISCRIMINATOR);
    }

    private void populateCareRunEntry(DateTime start, DateTime end, FixedContainer careRun, RotaResourceViewModel rotaEntry,
                                      String calendarId) {
        Interval interval = new Interval(start, end);
        // add allocated to the rota
        rotaService.findRecurrencesFromCalendar(calendarId, interval).forEach(recurrence ->
            rotaService.addRelevantRecurrences(recurrence, rotaEntry, AppointmentSchedule.class, careRun.getServiceRecipientId())
        );
        // add availability to the rota
        Availability availability = rotaService.findAvailability(calendarId, interval, false);
        for (AvailableInterval ai : availability.getAvailableIntervals()) {
            rotaEntry.addAvailability(ai.getInterval().getStart(), ai.getInterval().getEnd());
        }
    }

    private BooleanExpression getWherePredicateForDemandSchedulesBuilding(RotaParams rota) {
        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().toDateTimeAtStartOfDay();
        return DemandSchedulePredicates.getWherePredicate(start, end, AppointmentSchedule.class)
                .and(demandSchedule.agreement.serviceRecipientId.eq(BaseRotaHandler.getDemandServiceRecipientId(rota.getDemandFilter()))); // the building's srId
    }
}
