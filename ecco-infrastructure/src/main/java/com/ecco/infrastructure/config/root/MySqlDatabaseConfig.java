package com.ecco.infrastructure.config.root;


import org.apache.tomcat.jdbc.pool.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.orm.jpa.vendor.Database;

@Configuration
@Profile({Profiles.DEV, Profiles.PROD})
public class MySqlDatabaseConfig extends AbstractSqlDatabaseConfig<DataSource> {

    @Autowired
    private EccoEnvironment env;

    @Override
    @Bean(destroyMethod = "close")
    public DataSource dataSource() {
        return createDataSource();
    }

    private DataSource createDataSource() {
        org.apache.tomcat.jdbc.pool.DataSource dataSource = getConnectionPool(env);
        dataSource.setDriverClassName(com.mysql.cj.jdbc.Driver.class.getCanonicalName());
        dataSource.setTestOnBorrow(true);
        dataSource.setValidationQuery("select 1+1");

        // old jdbc.url overrides new style to allow migration
        String jdbcUrl = env.getProperty("jdbc.url");
        if (jdbcUrl == null) {
            var useSSL = env.getProperty("jdbc.useSSL", Boolean.class, false);
            // -Ddb.port=
            String dbPort = env.getDbPort() == null ? "3306" : env.getDbPort().toString();
            jdbcUrl = "jdbc:mysql://" + env.getDbHostName() + ":" + dbPort
                    + "/" + env.getDbSchema()
                    + "?useUnicode=true"
                    + "&characterEncoding=UTF-8"
                    + "&sessionVariables=time_zone=UTC"
                    + "&nullDatabaseMeansCurrent=true"
                    + "&databaseTerm=CATALOG"
                    + "&allowPublicKeyRetrieval=" + !useSSL
                    + "&useSSL=" + useSSL;
        }
        dataSource.setUrl(jdbcUrl);
        dataSource.setUsername(env.getProperty("jdbc.username"));
        dataSource.setPassword(env.getProperty("jdbc.password"));

        return dataSource;
    }

    @Override
    public Database database() {
        return Database.MYSQL;
    }
}
