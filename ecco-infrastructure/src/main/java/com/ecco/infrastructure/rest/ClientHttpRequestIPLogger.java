package com.ecco.infrastructure.rest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;

@Slf4j
public class ClientHttpRequestIPLogger implements ClientHttpRequest {

    final ClientHttpRequest request;

    public ClientHttpRequestIPLogger(ClientHttpRequest request) {
        this.request = request;
    }

    @Override
    public ClientHttpResponse execute() throws IOException {
        // execute calls .connect() which then allows us to inspect the IP
        var response = request.execute();
        logIP();
        return response;
    }

    @Override
    public OutputStream getBody() throws IOException {
        return request.getBody();
    }

    @Override
    public String getMethodValue() {
        return request.getMethodValue();
    }

    @Override
    public URI getURI() {
        return request.getURI();
    }

    @Override
    public HttpHeaders getHeaders() {
        return request.getHeaders();
    }

    // original log ip suggested at https://forums.oracle.com/ords/apexds/post/getting-the-ip-address-of-an-httpurlconnection-2224
    // but added a few comments and let co-pilot do the rest
    private void logIP() {
        // the request will have a field called 'connection' in our usage
        /*HttpURLConnection conn = null;
        try {
            Field field = request.getClass().getDeclaredField("connection");
            field.setAccessible(true);
            conn = (HttpURLConnection) field.get(request);
            //System.out.println(conn.getURL().getHost());
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }

        // find the IP address of the connection
        try {
            Field field = conn.getClass().getDeclaredField("http");
            field.setAccessible(true);
            Socket socket = (Socket) field.get(conn);
            //System.out.println(socket.getInetAddress().getHostAddress());
            log.info("IP LOGGER: " + socket.getInetAddress().getHostAddress());
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }*/
    }

}
