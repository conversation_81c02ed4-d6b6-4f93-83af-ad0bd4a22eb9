package com.ecco.calendar.core;

import org.jspecify.annotations.NonNull;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public interface DaysOfWeek extends Serializable {
    /**
     * See if the days contain the specified ISO day (Mon=1).
     */
    boolean isCalendarDayISO(int dayOfWeekISO);

    /**
     * Return a new DaysOfWeek by merging other.
     * NB Ideally static, but not accessible where we need it
     */
    static DaysOfWeek merge(@NonNull DaysOfWeek one, @NonNull DaysOfWeek two) {
        var merged = new HashSet<>(DaysOfWeek.toSet(one)); // not immutable
        merged.addAll(DaysOfWeek.toSet(two));
        return DaysOfWeek.from(merged);
    }

    /**
     * Convert from ISO dow (Mon=1) to Java Calendar (Sun=1)
     */
    static int dayOfWeekISOToJavaCalendar(int dayOfWeekISO) {
        var dayOfWeekJava = dayOfWeekISO + 1;
        return dayOfWeekJava > 7 ? 1 : dayOfWeekJava;
    }

    static DaysOfWeek from(@NonNull Collection<Integer> daysISO) {
        return daysISO::contains;
    }

    static DaysOfWeek fromAllWeek() {
        return DaysOfWeek.from(allWeek());
    }

    static Set<Integer> toSet(@NonNull DaysOfWeek daysISO) {
        return IntStream.rangeClosed(1, 7)
                .mapToObj(d -> daysISO.isCalendarDayISO(d) ? d : 0)
                .filter(d -> !d.equals(0))
                .collect(Collectors.toUnmodifiableSet());
    }

    static Set<Integer> isoToJavaCalendarSet(@NonNull DaysOfWeek daysISO) {
        return IntStream.rangeClosed(1, 7)
                .mapToObj(d -> daysISO.isCalendarDayISO(d) ? d : 0)
                .filter(d -> !d.equals(0))
                .map(DaysOfWeek::dayOfWeekISOToJavaCalendar)
                .collect(Collectors.toUnmodifiableSet());
    }

    static Set<Integer> allWeek() {
        return Set.of(1,2,3,4,5,6,7);
    }

}
