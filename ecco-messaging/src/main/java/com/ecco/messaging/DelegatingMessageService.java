package com.ecco.messaging;

import org.springframework.stereotype.Component;

import javax.annotation.Nullable;

@Component
public class DelegatingMessageService implements MessageService {

    private final TwilioSmsService twilioSmsService;
    private final EsendexService esendexService;

    public DelegatingMessageService(
            @Nullable TwilioSmsService twilioSmsService,
            @Nullable EsendexService esendexService
    ) {
        this.twilioSmsService = twilioSmsService;
        this.esendexService = esendexService;
    }

    public String respondSms(
            String accountSid,
            String smsBody
    ) {
        if (twilioSmsService != null) {
            twilioSmsService.validateAccount(accountSid);
            return "Your message was added to your file";
        } else if (esendexService != null) {
            esendexService.validateAccount(accountSid);
            esendexService.sendSms(accountSid, "Your message was added to your file");
        }
        return null;
    }

    public MessageResult sendSms(
            String mobileNumber,
            String smsBody
    ) {
        if (mobileNumber == null) {
            return MessageResult.failed(null, "No phone number found");
        } else if (!mobileNumber.matches("((\\+447)|(07)).+")) {
            return MessageResult.failed(mobileNumber, "Phone number must start +447 or 07");
        } else {
            mobileNumber = mobileNumber.replaceAll("^07", "+447");

            if (twilioSmsService != null) {
                return twilioSmsService.send(mobileNumber, smsBody);
            } else if (esendexService != null) {
                return esendexService.sendSms(mobileNumber, smsBody);
            }

            throw new IllegalStateException("No SMS service configured");
        }
    }

}
